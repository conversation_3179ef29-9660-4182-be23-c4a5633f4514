import { ethers ,Transaction} from 'ethers';

// Replace with your actual private key
const privateKey = 'be1a0d0e07d460bc0cd04c16773b4c409d08a7ed1ff9532927d9292f926bf7ca';
const wallet = new ethers.Wallet(privateKey);
const unsignedTxSerialized = "0x02f87183aa36a70f8477359400853113a18c3f830720d0948fe26e14296ee49d8a99d9a40887b86b64e6fd1980b844e4457a8a00000000000000000000000000000000000000000000000000000000000003e80000000000000000000000000000000000000000000000000000000000000000c0";

const signTransaction= async ()=>{

// 将序列化的交易数据反序列化为对象
const unsignedTx = Transaction.from(unsignedTxSerialized);

// 签署交易
const signedTx = await wallet.signTransaction(unsignedTx);

console.log("Signed Transaction:", signedTx);

}

const splitIntoChunks = (signedTx: string, chunkSize = 64) =>{
    const chunks = [];
    for (let i = 0; i < signedTx.length; i += chunkSize) {
        chunks.push(signedTx.slice(i, i + chunkSize));
    }
    return chunks;
}

signTransaction().catch(console.error);

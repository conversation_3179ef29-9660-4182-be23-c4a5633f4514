import * as bip39 from 'bip39';
import { hdkey } from 'ethereumjs-wallet';
import * as ethUtil from 'ethereumjs-util';
// import Cosmos from '@cosmostation/cosmosjs';

// 生成助记词
// const mnemonic = bip39.generateMnemonic();
const mnemonic = "slush boost love decline forward master cupboard gravity satoshi breeze suit motion";

console.log(`助记词: ${mnemonic}`);

// 根据助记词生成种子
const seed = bip39.mnemonicToSeedSync(mnemonic);

// 使用种子生成HD钱包
const hdWallet = hdkey.fromMasterSeed(seed);

// 派生第一个以太坊地址
const ethPath = "m/44'/60'/0'/0/0";
const ethWallet = hdWallet.derivePath(ethPath).getWallet();
const ethAddress = `0x${ethWallet.getAddress().toString('hex')}`;
const ethPublicKey = ethWallet.getPublicKey().toString('hex');

console.log(`以太坊地址: ${ethAddress}`);
console.log(`以太坊公钥: ${ethPublicKey}`);
// 压缩公钥生成逻辑
const compressPublicKey = (publicKey: Buffer): Buffer => {
    const x = publicKey.slice(1, 33);  // X 坐标
    const y = publicKey.slice(33, 65); // Y 坐标
  
    // 如果 Y 是偶数，前缀为 0x02，否则为 0x03
    const prefix = (parseInt(y.toString('hex'), 16) % 2 === 0) ? 0x02 : 0x03;
    
    return Buffer.concat([Buffer.from([prefix]), x]);
  };
  
  // 压缩公钥
  const compressedPublicKey = compressPublicKey(ethWallet.getPublicKey());
  console.log(`压缩公钥: 0x${compressedPublicKey.toString('hex')}`);

// 使用Cosmos SDK生成ZetaChain地址
// const cosmos = new Cosmos('https://lcd-cosmoshub.blockapsis.com', 'cosmoshub-4');
// const zetaPath = "m/44'/118'/0'/0/0"; // Cosmos SDK标准路径
// const zetaWallet = cosmos.getECPairPriv(mnemonic, zetaPath);
// const zetaAddress = cosmos.getAddress(zetaWallet);
// const zetaPublicKey = cosmos.getPubKey(zetaWallet);

// console.log(`ZetaChain地址: ${zetaAddress}`);
// console.log(`ZetaChain公钥: ${zetaPublicKey}`);

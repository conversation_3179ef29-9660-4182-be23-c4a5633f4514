import { ethers } from "ethers";

async function signPermit(
    signer: ethers.Signer,
    tokenContract: ethers.Contract,
    owner: string,
    spender: string,
    value: ethers.BigNumberish,
    deadline: number
) {
    const nonce = await tokenContract.nonces(owner);
    const chainId = await signer.getChainId();

    const domain = {
        name: await tokenContract.name(),
        version: "1",
        chainId,
        verifyingContract: tokenContract.address
    };

    const types = {
        Permit: [
            { name: "owner", type: "address" },
            { name: "spender", type: "address" },
            { name: "value", type: "uint256" },
            { name: "nonce", type: "uint256" },
            { name: "deadline", type: "uint256" }
        ]
    };

    const message = {
        owner,
        spender,
        value,
        nonce: nonce.toString(),
        deadline
    };

    // 使用 signTypedData 方法
    const signature = await signer.signTypedData(domain, types, message);
    const { v, r, s } = ethers.utils.splitSignature(signature);

    return { v, r, s };
}

async function callPermit(
    tokenContract: ethers.Contract,
    owner: string,
    spender: string,
    value: ethers.BigNumberish,
    deadline: number,
    v: number,
    r: string,
    s: string
) {
    await tokenContract.permit(owner, spender, value, deadline, v, r, s);
}

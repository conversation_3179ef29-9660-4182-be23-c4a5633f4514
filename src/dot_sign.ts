import { ApiPromise, WsProvider } from '@polkadot/api';
import { cryptoWaitReady, mnemonicToMiniSecret, naclKeypairFromSeed } from '@polkadot/util-crypto';
import { u8aToHex, hexToU8a } from '@polkadot/util';

// TypeScript 类型定义
interface UnsignedTx {
  blockHash: string;
  eraPeriod: number;
  genesisHash: string;
  nonce: number;
  specVersion: number;
  tip: number;
  transactionVersion: number;
  method: {
    args: {
      value: string;
      payee: { staked: any };
    };
    name: string;
    pallet: string;
  };
}

async function main() {
  // 等待加密库准备好
  await cryptoWaitReady();

  // 助记词
  const mnemonic = 'your mnemonic here';

  // 生成密钥对
  const seed = mnemonicToMiniSecret(mnemonic);
  const { publicKey, secretKey } = naclKeypairFromSeed(seed);

  // 创建 API 客户端
  const provider = new WsProvider('wss://your-chain-url');
  const api = await ApiPromise.create({ provider });

  // 示例的 unsigned_tx_serialized
  const unsignedTxSerialized: UnsignedTx = {
    blockHash: '0x6e3abcd34d6c5507e776eb7a86037edb4fb801daa436a031ad4d93dba489c005',
    eraPeriod: 64,
    genesisHash: '0x91b171bb158e2d3848fa23a9f1c25182fb8e20313b2c1eb49219da7a70ce90c3',
    nonce: 0,
    specVersion: 1003000,
    tip: 0,
    transactionVersion: 26,
    method: {
      args: {
        value: '1000',
        payee: { staked: null }
      },
      name: 'bond',
      pallet: 'staking'
    }
  };

  // 获取链上的元数据
  const metadata = api.registry.metadata;
  const pallet = metadata.pallets.find(p => p.name.toString() === unsignedTxSerialized.method.pallet);
  if (!pallet) {
    throw new Error(`Module ${unsignedTxSerialized.method.pallet} not found in metadata`);
  }

  const call = pallet.calls.find(c => c.name.toString() === unsignedTxSerialized.method.name);
  if (!call) {
    throw new Error(`Call ${unsignedTxSerialized.method.name} not found in module ${unsignedTxSerialized.method.pallet}`);
  }

  // 创建交易
  const tx = api.tx[unsignedTxSerialized.method.pallet][unsignedTxSerialized.method.name](unsignedTxSerialized.method.args.value);

  // 签名选项
  const options = {
    blockHash: hexToU8a(unsignedTxSerialized.blockHash),
    era: api.registry.createType('ExtrinsicEra', { period: unsignedTxSerialized.eraPeriod, current: 0 }),
    genesisHash: hexToU8a(unsignedTxSerialized.genesisHash),
    nonce: unsignedTxSerialized.nonce,
    specVersion: unsignedTxSerialized.specVersion,
    tip: unsignedTxSerialized.tip,
    transactionVersion: unsignedTxSerialized.transactionVersion
  };

  // 签名交易
  const signedTx = await tx.signAsync(publicKey, options);

  // 将签名后的交易序列化为十六进制字符串
  const signedTxHex = u8aToHex(signedTx.toU8a(), -1, true);

  console.log(`Signed Transaction: ${signedTxHex}`);
}

main().catch(console.error);

import * as bip39 from 'bip39';
import { BIP32Factory } from 'bip32';
import * as ecc from 'tiny-secp256k1';
import { bech32 } from 'bech32';
import { sha256 } from 'js-sha256';
import * as crypto from 'crypto';

// 使用BIP32Factory创建bip32实例
const bip32 = BIP32Factory(ecc);

// ... 其他代码 ...

function generateZetaAddress(mnemonic: string): string {
    // 生成种子
    const seed = bip39.mnemonicToSeedSync(mnemonic);
    const root = bip32.fromSeed(seed);

    // 从根节点派生路径
    const path = "m/44'/118'/0'/0/0"; // 这是一个示例路径，具体路径可能需要根据Zeta Chain的要求调整
    const child = root.derivePath(path);

    // 获取公钥
    const publicKey = child.publicKey;
    console.log("publicKey:", publicKey);

    // 使用bech32编码生成地址
    const words = bech32.toWords(publicKey);
    const address = bech32.encode('zeta', words);

    return address;
}

// 示例助记词
const mnemonic = "slush boost love decline forward master cupboard gravity satoshi breeze suit motion";
const address = generateZetaAddress(mnemonic);
console.log("Zeta Chain Address:", address);

// ... 其他代码 ...
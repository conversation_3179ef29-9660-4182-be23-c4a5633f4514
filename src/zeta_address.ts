import * as bip39 from 'bip39';
import * as bip32 from 'bip32';
import { createHash } from 'crypto';
import * as bech32 from 'bech32';

function newMnemonic(): string {
  return bip39.generateMnemonic(128); // 生成128位熵的助记词
}

function sha256(buffer: Buffer): Buffer {
  return createHash('sha256').update(buffer).digest();
}

function ripemd160(buffer: Buffer): Buffer {
  return createHash('ripemd160').update(buffer).digest();
}

async function generateAddress(): Promise<void> {
  // 1. 生成助记词
  const mnemonic: string = newMnemonic();
  console.log("助记词:", mnemonic);

  // 2. 根据助记词生成种子
  const seed: Buffer = await bip39.mnemonicToSeed(mnemonic);

  // 3. 通过种子生成根私钥
  const root = bip32.fromSeed(seed);

  // 4. 从私钥派生公钥
  const publicKey: Buffer = root.publicKey;
  console.log("PublicKey:", publicKey.toString('hex'));

  // 5. 对公钥进行 SHA-256 哈希处理
  const shaHash: Buffer = sha256(publicKey);

  // 6. 对 SHA-256 哈希结果进行 RIPEMD-160 哈希处理
  const publicKeyHash: Buffer = ripemd160(shaHash);

  // 7. 使用 ConvertBits 将 8 比特字节转换为 5 比特字符编码
  const words: number[] = bech32.toWords(publicKeyHash);

  // 8. 使用 Bech32 编码生成 ZetaChain 地址
  const zetaAddress: string = bech32.encode('zeta', words);
  console.log("ZetaChain 地址:", zetaAddress);
}

// 运行地址生成函数
generateAddress();

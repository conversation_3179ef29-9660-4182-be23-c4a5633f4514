import {
    Connection,
    Keypair,
    Transaction,
    PublicKey,
    TransactionInstruction,
  } from '@solana/web3.js';
  import * as bip39 from 'bip39';
  import * as bs58 from 'bs58';
  import nacl from 'tweetnacl';
  
  async function main() {
    const mnemonic = "drastic lonely travel grab lady card follow heavy bird pottery impose entire";
    const unsignedTxSerialized = "02000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000003381e7587a8f7f42be79a868191be3b42b0fd086df6ba045e28f2200e244add3ab03fdacf079a3220b846a48bfa75bc3eb5a1a4c5d214fe22582e3d4ee1696000201060a03e53363b773bce1c70dac38bfdbac368377316f55f80bec81fd82a6de7e25af51f5f3871e65b84cc393458d0f23a413184cf2bb7093ae4e2c99d55b39a575c5f84ae7472e242c79b21700f4c8ebda6ab287208739df7bba978df47a3c51556df98e3135fcb53e71e6fafcb4da3a3cc36af1c76a1a7e72aa12eae1346d724c6c00000000000000000000000000000000000000000000000000000000000000000306466fe5211732ffecadba72c39be7bc8ce5bbc5f7126b2c439b3a4000000006a1d8179137542a983437bdfe2a7ab2557f535c8a78722b68a49dc00000000006a7d51718c774c928566398691d5eb68b5eb8a39b4b6d5c73555b210000000006a7d517192c568ee08a845f73d29788cf035c3145b21ab344d8062ea940000006a7d517193584d0feed9bb3431d13206be544281b57b8566cc5375ff4000000968e3353c859e8c68180f7b4a7a1e6da0152be9bf40a157c63790cc8919ed32f0404030308010404000000060502000709000c0400000001e1f5050000000005000502ec040000050009030000000000000000";
  
    // Step 1: Derive the Keypair from the Mnemonic
    const seed = bip39.mnemonicToSeedSync(mnemonic).slice(0, 32);
    const keypair = Keypair.fromSeed(seed);
  
    console.log("address =", keypair.publicKey.toBase58());
  
    // Step 2: Deserialize the Unsigned Transaction
    const unsignedTxBytes = Buffer.from(unsignedTxSerialized, 'hex');
    const transaction = Transaction.from(unsignedTxBytes);
  
    console.log("tx =", transaction);
  
    // Step 3: Sign Each Instruction
    const msgBytes = transaction.serializeMessage();
    const signature = nacl.sign.detached(msgBytes, keypair.secretKey);
  
    console.log("signature =", Buffer.from(signature).toString('hex'));
  
    // Optionally, add the signature to the transaction and send it to the network
    // transaction.addSignature(keypair.publicKey, signature);
  
    // // If you want to send the transaction to the Solana network
    // const connection = new Connection('https://api.mainnet-beta.solana.com');
    // const serializedTx = transaction.serialize();
    // const txHash = await connection.sendRawTransaction(serializedTx);
    // console.log("Transaction Hash:", txHash);
  }
  
  main().catch((err) => {
    console.error(err);
  });
  
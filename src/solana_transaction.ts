import { Transaction, VersionedTransaction, PublicKey, SystemProgram } from "@solana/web3.js";
import { TokenInstruction, decodeInstruction, createAssociatedTokenAccountInstruction } from "@solana/spl-token";

const rawTx = "AgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwumkOrSpqFsvImJAkIfGHgEMFGnBRV9KYfET3D0W6LQY7Miy3wrDKP2qycf5vpYxOVkLucF8A93NiAwXMY9cIAgADB5wlGHw/kp7+0FglzICIyDjkFSrO18AKICEha7UPhf2P3AkQ7cpTdvjgyGe+sjWjtoOn4g4hAdQ+HHJPzCHLkSmIcoK/wdawVL3Gyu5UN9wsqczDMeerooKls/VgnlByJuH/gcP0fpbxSFYqlRGcyY2DvD1PrNynrQ/KS/iynyNTzgEOYK/tsicXvWMZL1QUWj+WWjO7gtLHAp6yzh4ggmQDBkZv5SEXMv/srbpyw5vnvIzlu8X3EmssQ5s6QAAAAAbd9uHXZaGT2cvhRs7reawctIXtX1s3kTqM9YV+/wCpwQIHCs92acI9rY0D2KFnmppIFFG0uK6ZRtVRhUCPbhkDBQAJAzUIAAAAAAAABQAFAkANAwAGBAIEAwEKDKCGAQAAAAAABg==";

// 尝试解析为 Legacy 或 Versioned Transaction
try {
  const legacyTx = Transaction.from(Buffer.from(rawTx, "base64"));
  console.log("=== Legacy Transaction ===");
  console.log("Signatures:", legacyTx.signatures.map(s => s.signature?.toString("hex")));
  console.log("Fee Payer:", legacyTx.feePayer?.toString());

  legacyTx.instructions.forEach((ix, index) => {
    console.log(`\n--- Instruction #${index + 1} ---`);
    console.log("Program ID:", ix.programId.toString());
    console.log("Data (Hex):", ix.data.toString("hex"));
    console.log("Accounts:", ix.keys.map(acc => ({
      pubkey: acc.pubkey.toString(),
      isSigner: acc.isSigner,
      isWritable: acc.isWritable,
    })));

    // 识别指令类型
    const instructionType = identifyInstruction(ix);
    console.log("Instruction Type:", instructionType);
  });
} catch (e) {
  console.log("Not a Legacy Transaction, trying Versioned Transaction...");
  const versionedTx = VersionedTransaction.deserialize(Buffer.from(rawTx, "base64"));
  console.log("=== Versioned Transaction ===");
  console.log("Signatures:", versionedTx.signatures.map(s => s.toString("hex")));
  console.log("Message:", versionedTx.message);

  versionedTx.message.compiledInstructions.forEach((ix, index) => {
    const programId = versionedTx.message.staticAccountKeys[ix.programIdIndex];
    const accounts = ix.accountKeyIndexes.map(idx => ({
      pubkey: versionedTx.message.staticAccountKeys[idx].toString(),
      isSigner: versionedTx.message.isAccountSigner(idx),
      isWritable: versionedTx.message.isAccountWritable(idx),
    }));
    const data = Buffer.from(ix.data);

    console.log(`\n--- Instruction #${index + 1} ---`);
    console.log("Program ID:", programId.toString());
    console.log("Data (Hex):", data.toString("hex"));
    console.log("Accounts:", accounts);

    // 识别指令类型（Versioned Transaction 需手动构造类似 Legacy 的指令格式）
    const fakeLegacyInstruction = {
      programId,
      data,
      keys: accounts.map(acc => ({
        pubkey: new PublicKey(acc.pubkey),
        isSigner: acc.isSigner,
        isWritable: acc.isWritable,
      })),
    };
    const instructionType = identifyInstruction(fakeLegacyInstruction);
    console.log("Instruction Type:", instructionType);
  });
}

// 识别指令类型的辅助函数
function identifyInstruction(ix: {
  programId: PublicKey;
  data: Buffer;
  keys: { pubkey: PublicKey; isSigner: boolean; isWritable: boolean }[];
}): string {
  const { programId, data, keys } = ix;

  // 1. 系统程序指令（如转账）
  if (programId.equals(SystemProgram.programId)) {
    if (data.length === 0) return "SystemProgram: Unknown";
    const instructionType = SystemProgram.decodeInstructionType(ix);
    return `SystemProgram: ${instructionType}`; // e.g., "Transfer"
  }

  // 2. SPL Token 指令（如创建 ATA、转账代币）
  if (programId.equals(new PublicKey("TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"))) {
    try {
      const decoded = decodeInstruction(ix);
      return `SPL Token: ${TokenInstruction[decoded.instruction]} (${decoded.instruction})`;
    } catch (e) {
      return "SPL Token: Unknown";
    }
  }

  // 3. Associated Token Program 指令（如创建 ATA）
  if (programId.equals(new PublicKey("ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL"))) {
    // 检查是否匹配 createAssociatedTokenAccount 的账户结构
    if (keys.length >= 7) {
      return "AssociatedTokenProgram: CreateAssociatedTokenAccount";
    }
    return "AssociatedTokenProgram: Unknown";
  }

  // 4. 其他程序
  return `Unknown Program (${programId.toString()})`;
}
import { Transaction, VersionedTransaction, PublicKey, SystemProgram, SystemInstruction } from "@solana/web3.js";

/**
 * Sample base64 encoded Solana transaction for analysis
 */
const rawTx = "AgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADj/e0iwJV4yjsF/b9hZHcukrWjm465/D4F49N9BXb/LEcleoeEnsBUAFqgMnWviESy5zV+XBqLaiRqnWjULbwBAgACBZwlGHw/kp7+0FglzICIyDjkFSrO18AKICEha7UPhf2PsGww13L+/NryPfVo/hLDXuDHqY2qZ6P5CvKRhcb7851m88LzknssTI8mS9TrioBrhVt1OXhb/wPJMGfNKxZuAwMGRm/lIRcy/+ytunLDm+e8jOW7xfcSayxDmzpAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAxTZhf/CT0bBm48W5oIkKMRByl4WIaHY3UOaYW44d4ogMDAAkD0gAAAAAAAAADAAUCQA0DAAQCAQIMAgAAAM4XugAAAAAA";

/**
 * Analyzes a Solana transaction from base64 encoded data
 * Supports both Legacy and Versioned transactions
 */
function analyzeTransaction(rawTxData: string): void {
    console.log("Analyzing Solana transaction...\n");

    // Try to parse as Legacy Transaction first
    try {
        const legacyTx = Transaction.from(Buffer.from(rawTxData, "base64"));
        console.log("=== Legacy Transaction ===");
        console.log("Signatures:", legacyTx.signatures.map(s => s.signature?.toString("hex")));
        console.log("Fee Payer:", legacyTx.feePayer?.toString());

        legacyTx.instructions.forEach((ix, index) => {
            console.log(`\n--- Instruction #${index + 1} ---`);
            console.log("Program ID:", ix.programId.toString());
            console.log("Data (Hex):", ix.data.toString("hex"));
            console.log("Accounts:", ix.keys.map(acc => ({
                pubkey: acc.pubkey.toString(),
                isSigner: acc.isSigner,
                isWritable: acc.isWritable,
            })));

            // 识别指令类型
            const instructionType = identifyInstruction(ix);
            console.log("Instruction Type:", instructionType);
        });
    } catch (e) {
        console.log("Not a Legacy Transaction, trying Versioned Transaction...");
        const versionedTx = VersionedTransaction.deserialize(Buffer.from(rawTxData, "base64"));
        console.log("=== Versioned Transaction ===");
        console.log("Signatures:", versionedTx.signatures.map(s => Buffer.from(s).toString("hex")));
        console.log("Message:", versionedTx.message);

        versionedTx.message.compiledInstructions.forEach((ix, index) => {
            const programId = versionedTx.message.staticAccountKeys[ix.programIdIndex];
            const accounts = ix.accountKeyIndexes.map(idx => ({
                pubkey: versionedTx.message.staticAccountKeys[idx].toString(),
                isSigner: versionedTx.message.isAccountSigner(idx),
                isWritable: versionedTx.message.isAccountWritable(idx),
            }));
            const data = Buffer.from(ix.data);

            console.log(`\n--- Instruction #${index + 1} ---`);
            console.log("Program ID:", programId.toString());
            console.log("Data (Hex):", data.toString("hex"));
            console.log("Accounts:", accounts);

            // 识别指令类型（Versioned Transaction 需手动构造类似 Legacy 的指令格式）
            const fakeLegacyInstruction = {
                programId,
                data,
                keys: accounts.map(acc => ({
                    pubkey: new PublicKey(acc.pubkey),
                    isSigner: acc.isSigner,
                    isWritable: acc.isWritable,
                })),
            };
            const instructionType = identifyInstruction(fakeLegacyInstruction);
            console.log("Instruction Type:", instructionType);
        });
    }

    // 识别指令类型的辅助函数
    function identifyInstruction(ix: {
        programId: PublicKey;
        data: Buffer;
        keys: { pubkey: PublicKey; isSigner: boolean; isWritable: boolean }[];
    }): string {
        const { programId, data, keys } = ix;

        // 1. 系统程序指令（如转账）
        if (programId.equals(SystemProgram.programId)) {
            if (data.length === 0) return "SystemProgram: Unknown";
            const instructionType = SystemInstruction.decodeInstructionType(ix);
            return `SystemProgram: ${instructionType}`; // e.g., "Transfer"
        }

        // 2. SPL Token 指令（如创建 ATA、转账代币）
        if (programId.equals(new PublicKey("TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"))) {
            try {
                // Basic SPL Token instruction type detection based on instruction data
                if (data.length === 0) return "SPL Token: Unknown";
                const instructionType = data[0]; // First byte indicates instruction type

                // Common SPL Token instruction types
                const tokenInstructions: { [key: number]: string } = {
                    0: "InitializeMint",
                    1: "InitializeAccount",
                    2: "InitializeMultisig",
                    3: "Transfer",
                    4: "Approve",
                    5: "Revoke",
                    6: "SetAuthority",
                    7: "MintTo",
                    8: "Burn",
                    9: "CloseAccount",
                    10: "FreezeAccount",
                    11: "ThawAccount",
                    12: "TransferChecked",
                    13: "ApproveChecked",
                    14: "MintToChecked",
                    15: "BurnChecked",
                    16: "InitializeAccount2",
                    17: "SyncNative",
                    18: "InitializeAccount3",
                    19: "InitializeMultisig2",
                    20: "InitializeMint2"
                };

                const instructionName = tokenInstructions[instructionType] || `Unknown(${instructionType})`;
                return `SPL Token: ${instructionName}`;
            } catch (e) {
                return "SPL Token: Unknown";
            }
        }

        // 3. Associated Token Program 指令（如创建 ATA）
        if (programId.equals(new PublicKey("ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL"))) {
            // 检查是否匹配 createAssociatedTokenAccount 的账户结构
            if (keys.length >= 7) {
                return "AssociatedTokenProgram: CreateAssociatedTokenAccount";
            }
            return "AssociatedTokenProgram: Unknown";
        }

        // 4. 其他程序
        return `Unknown Program (${programId.toString()})`;
    }
}

// Execute the analysis
analyzeTransaction(rawTx);
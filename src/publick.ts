import { BIP32Factory } from 'bip32';
import { mnemonicToSeedSync } from 'bip39';
import { ethWallet } from 'ethereumjs-wallet';

// 设置bip32的curve参数为以太坊使用的secp256k1
const bip32 = BIP32Factory(ecc);

const mnemonic = 'abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon about';
const path = "m/44'/60'/0'/0/0";

// 根据助记词生成种子
const seed = mnemonicToSeedSync(mnemonic);

// 从种子生成根私钥
const root = bip32.fromSeed(seed);

// 根据路径生成子私钥
const child = root.derivePath(path);

// 从子私钥生成以太坊钱包
const wallet = ethWallet.fromExtendedPrivateKey(child.toBase58());

// 获取钱包地址
const address = wallet.getAddressString();

// 获取公钥
const publicKey = wallet.getPublicKeyString();

console.log('Ethereum Address:', address);
console.log('Ethereum Public Key:', publicKey);

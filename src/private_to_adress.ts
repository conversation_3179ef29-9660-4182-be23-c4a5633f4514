import { DirectSecp256k1Wallet } from "@cosmjs/proto-signing";
import { bech32 } from "bech32";

// 从私钥生成 Cosmos 地址的函数
async function generateCosmosAddressFromPrivateKey(privateKey: Uint8Array) {
  // 使用私钥创建钱包
  const wallet = await DirectSecp256k1Wallet.fromKey(privateKey);
  
  // 获取钱包的账户信息，其中包含公钥和地址
  const [{ address }] = await wallet.getAccounts();
  
  return address;
}

// 示例：从十六进制私钥生成地址
const privateKeyHex = "f4ca267d727b91e3678bb5233634dc95fc807e94dcdb9b854961e85d76e8970a";
const privateKey = Uint8Array.from(Buffer.from(privateKeyHex, "hex"));

generateCosmosAddressFromPrivateKey(privateKey).then((address) => {
  console.log("Cosmos Address:", address);
});

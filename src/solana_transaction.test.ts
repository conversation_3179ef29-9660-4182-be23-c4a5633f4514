import { Transaction, VersionedTransaction, PublicKey, SystemProgram, SystemInstruction } from "@solana/web3.js";

/**
 * Test file to verify the fixes in solana_transaction.ts
 */

// Test data - a simple system transfer transaction
const testLegacyTx = "AQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABAAEDArczbMia1tLmq7zz4DinMNN0pJ1JtLdqIJPUw3YrGCzYAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAG3fbh12Whk9nL4UbO63msHLSF7V9bN5E6jPWFfv8AqcEBAgIAAQwCAAAAQEIPAAAAAA==";

// Test data - versioned transaction (this will fail legacy parsing and use versioned)
const testVersionedTx = "AgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwumkOrSpqFsvImJAkIfGHgEMFGnBRV9KYfET3D0W6LQY7Miy3wrDKP2qycf5vpYxOVkLucF8A93NiAwXMY9cIAgADB5wlGHw/kp7+0FglzICIyDjkFSrO18AKICEha7UPhf2P3AkQ7cpTdvjgyGe+sjWjtoOn4g4hAdQ+HHJPzCHLkSmIcoK/wdawVL3Gyu5UN9wsqczDMeerooKls/VgnlByJuH/gcP0fpbxSFYqlRGcyY2DvD1PrNynrQ/KS/iynyNTzgEOYK/tsicXvWMZL1QUWj+WWjO7gtLHAp6yzh4ggmQDBkZv5SEXMv/srbpyw5vnvIzlu8X3EmssQ5s6QAAAAAbd9uHXZaGT2cvhRs7reawctIXtX1s3kTqM9YV+/wCpwQIHCs92acI9rY0D2KFnmppIFFG0uK6ZRtVRhUCPbhkDBQAJAzUIAAAAAAAABQAFAkANAwAGBAIEAwEKDKCGAQAAAAAABg==";

/**
 * Test function to verify transaction parsing works correctly
 */
function testTransactionParsing(): void {
    console.log("=== Testing Transaction Parsing ===\n");
    
    // Test 1: Legacy Transaction
    console.log("Test 1: Legacy Transaction");
    try {
        const legacyTx = Transaction.from(Buffer.from(testLegacyTx, "base64"));
        console.log("✅ Legacy transaction parsed successfully");
        console.log(`   Instructions: ${legacyTx.instructions.length}`);
        console.log(`   Fee Payer: ${legacyTx.feePayer?.toString() || "Unknown"}`);
    } catch (error) {
        console.log("❌ Failed to parse legacy transaction:", error);
    }
    
    // Test 2: Versioned Transaction signature handling
    console.log("\nTest 2: Versioned Transaction Signature Handling");
    try {
        const versionedTx = VersionedTransaction.deserialize(Buffer.from(testVersionedTx, "base64"));
        console.log("✅ Versioned transaction parsed successfully");
        
        // Test the signature conversion that was fixed
        const signatures = versionedTx.signatures.map(s => Buffer.from(s).toString("hex"));
        console.log(`   Signatures converted: ${signatures.length > 0 ? "✅" : "❌"}`);
        console.log(`   Instructions: ${versionedTx.message.compiledInstructions.length}`);
    } catch (error) {
        console.log("❌ Failed to parse versioned transaction:", error);
    }
    
    // Test 3: SystemInstruction API
    console.log("\nTest 3: SystemInstruction API");
    try {
        const legacyTx = Transaction.from(Buffer.from(testLegacyTx, "base64"));
        const systemInstruction = legacyTx.instructions.find(ix => 
            ix.programId.equals(SystemProgram.programId)
        );
        
        if (systemInstruction) {
            const instructionType = SystemInstruction.decodeInstructionType(systemInstruction);
            console.log(`✅ SystemInstruction.decodeInstructionType works: ${instructionType}`);
        } else {
            console.log("⚠️  No system instruction found in test transaction");
        }
    } catch (error) {
        console.log("❌ SystemInstruction API test failed:", error);
    }
    
    console.log("\n=== All Tests Completed ===");
}

// Run the tests
testTransactionParsing();

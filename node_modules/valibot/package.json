{"name": "valibot", "description": "The modular and type safe schema library for validating structural data", "version": "0.37.0", "license": "MIT", "author": "<PERSON>", "homepage": "https://valibot.dev", "repository": {"type": "git", "url": "https://github.com/fabian-hiller/valibot"}, "keywords": ["modular", "typescript", "schema", "validation", "parsing", "bundle-size", "type-safe", "runtime"], "type": "module", "main": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}}, "sideEffects": false, "files": ["dist"], "publishConfig": {"access": "public"}, "scripts": {"play": "tsm ./playground.ts", "test": "vitest --typecheck", "coverage": "vitest run --coverage --isolate", "lint": "eslint \"src/**/*.ts*\" && tsc --noEmit", "eslint.fix": "eslint \"src/**/*.ts*\" --fix", "format": "prettier --write ./src", "format.check": "prettier --check ./src", "build": "tsup", "publish.npm": "npm publish", "publish.jsr": "npx jsr publish"}, "devDependencies": {"@types/eslint": "^8.56.10", "@typescript-eslint/eslint-plugin": "^7.15.0", "@typescript-eslint/parser": "^7.15.0", "@vitest/coverage-v8": "^1.6.0", "eslint": "^8.57.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsdoc": "^48.5.2", "eslint-plugin-redos-detector": "^2.4.0", "eslint-plugin-regexp": "^2.6.0", "eslint-plugin-security": "^2.1.1", "jsdom": "^24.1.0", "tsm": "^2.3.0", "tsup": "^8.1.0", "typescript": "^5.5.3", "vite": "^5.3.3", "vitest": "1.6.0"}, "peerDependencies": {"typescript": ">=5"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}
{"version": 3, "file": "bn254.js", "sourceRoot": "", "sources": ["src/bn254.ts"], "names": [], "mappings": ";;;AAAA,sEAAsE;AACtE,iDAA8C;AAC9C,yDAA6C;AAC7C,8DAAwD;AACxD,+CAAkD;AAClD,8CAAiD;AACjD,sDAA8C;AAC9C,kDAAqE;AACrE,kDAA4D;AAG5D,kBAAkB;AAClB,MAAM,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AACxD,kBAAkB;AAClB,MAAM,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AAEtB;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA4BE;AAEF;;;;;;;;;;;;EAYE;AACF,MAAM,IAAI,GAAG,MAAM,CAAC,qBAAqB,CAAC,CAAC;AAC3C,MAAM,QAAQ,GAAG,IAAA,iBAAM,EAAC,IAAI,CAAC,CAAC;AAC9B,MAAM,aAAa,GAAG,GAAG,GAAG,IAAI,IAAI,GAAG,CAAC;AAExC,+EAA+E;AAC/E,MAAM,EAAE,GAAG,IAAA,kBAAK,EACd,MAAM,CAAC,+EAA+E,CAAC,CACxF,CAAC;AACF,iDAAiD;AACjD,MAAM,IAAI,GAAG;IACX,EAAE,EAAE,MAAM,CAAC,+EAA+E,CAAC;IAC3F,EAAE,EAAE,MAAM,CAAC,6EAA6E,CAAC;CAC1F,CAAC;AAEF,MAAM,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,IAAA,kBAAO,EAAC;IAChD,KAAK,EAAE,MAAM,CAAC,+EAA+E,CAAC;IAC9F,cAAc,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC;IAChC,SAAS,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC;IACtC,wDAAwD;IACxD,uCAAuC;IACvC,oBAAoB,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAQ,EAAE;QACzC,MAAM,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;QAC5C,MAAM,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;QAC5C,MAAM,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,GAAG,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QACxD,MAAM,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,GAAG,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QACxD,MAAM,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,GAAG,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QACxD,IAAI,EAAE,GAAG,GAAG,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC,CAAC,eAAe;QACjD,OAAO;YACL,EAAE,EAAE,GAAG,CAAC,MAAM,CAAC;gBACb,EAAE,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,wBAAwB;gBAC1E,EAAE,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,wBAAwB;gBAC1E,EAAE,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,EAAE,CAAC;aACjD,CAAC,EAAE,wBAAwB;YAC5B,EAAE,EAAE,GAAG,CAAC,MAAM,CAAC;gBACb,EAAE,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,uBAAuB;gBACzE,EAAE,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,uBAAuB;gBACzE,EAAE,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,EAAE,CAAC;aACjD,CAAC;SACH,CAAC,CAAC,uBAAuB;IAC5B,CAAC;IACD,iBAAiB,CAAC,GAAG,EAAE,CAAC;QACtB,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC;QACjB,KAAK,IAAI,CAAC,GAAG,QAAQ,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;YACvC,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;YAC9B,IAAI,IAAA,iBAAM,EAAC,CAAC,EAAE,CAAC,CAAC;gBAAE,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;QACzC,CAAC;QACD,OAAO,CAAC,CAAC;IACX,CAAC;IACD,uCAAuC;IACvC,uCAAuC;IACvC,qBAAqB,EAAE,CAAC,GAAG,EAAE,EAAE;QAC7B,MAAM,SAAS,GAAG,CAAC,GAAS,EAAE,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC;QAChF,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;QACxD,MAAM,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QACjD,MAAM,EAAE,GAAG,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;QAChD,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,iBAAiB,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;QACpD,MAAM,EAAE,GAAG,SAAS,CAAC,EAAE,CAAC,CAAC;QACzB,MAAM,EAAE,GAAG,SAAS,CAAC,IAAI,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAC,CAAC;QACjD,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;QAC1E,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;QAC5B,OAAO,IAAI,CAAC,GAAG,CACb,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EACrD,IAAI,CAAC,GAAG,CACN,IAAI,CAAC,YAAY,CAAC,EAAE,EAAE,CAAC,CAAC,EACxB,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAClE,CACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC;AAEH,sBAAsB;AACtB,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,IAAA,uBAAY,EAAC,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC,UAAU,CAAC,CAAC;AAE7D;;;;;;EAME;AACF,MAAM,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC;IAChC,wDAAwD;IACxD,GAAG,EAAE,8BAA8B;IACnC,SAAS,EAAE,8BAA8B;IACzC,CAAC,EAAE,EAAE,CAAC,KAAK;IACX,CAAC,EAAE,CAAC;IACJ,CAAC,EAAE,GAAG;IACN,MAAM,EAAE,KAAK;IACb,IAAI,EAAE,eAAM;CACJ,CAAC,CAAC;AAEZ;;;GAGG;AACU,QAAA,KAAK,GAAY,IAAA,YAAG,EAAC;IAChC,SAAS;IACT,MAAM,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,EAAE,EAAE;IAClC,EAAE,EAAE;QACF,EAAE;QACF,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;QACZ,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC;QACb,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC;QACb,CAAC,EAAE,EAAE,CAAC,IAAI;QACV,CAAC,EAAE,GAAG;QACN,WAAW,EAAE,EAAE,GAAG,WAAW,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,8BAA8B,EAAE;QAC1E,cAAc,EAAE,IAAI;QACpB,kBAAkB,EAAE,IAAI;QACxB,UAAU,EAAE,yBAAc;QAC1B,SAAS,EAAE,yBAAc;QACzB,OAAO,EAAE,yBAAc;QACvB,cAAc,EAAE;YACd,OAAO,EAAE,yBAAc;YACvB,UAAU,EAAE,yBAAc;YAC1B,KAAK,EAAE,yBAAc;SACtB;KACF;IACD,EAAE,EAAE;QACF,EAAE,EAAE,GAAG;QACP,2DAA2D;QAC3D,CAAC,EAAE,MAAM,CAAC,+EAA+E,CAAC;QAC1F,EAAE,EAAE,GAAG,CAAC,YAAY,CAAC;YACnB,MAAM,CAAC,+EAA+E,CAAC;YACvF,MAAM,CAAC,+EAA+E,CAAC;SACxF,CAAC;QACF,EAAE,EAAE,GAAG,CAAC,YAAY,CAAC;YACnB,MAAM,CAAC,8EAA8E,CAAC;YACtF,MAAM,CAAC,8EAA8E,CAAC;SACvF,CAAC;QACF,CAAC,EAAE,GAAG,CAAC,IAAI;QACX,CAAC,EAAE,IAAI;QACP,IAAI,EAAE,MAAM,CAAC,+EAA+E,CAAC;QAC7F,WAAW,EAAE,EAAE,GAAG,WAAW,EAAE;QAC/B,cAAc,EAAE,IAAI;QACpB,kBAAkB,EAAE,IAAI;QACxB,aAAa,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,iBAAiB;QAC/F,UAAU,EAAE,yBAAc;QAC1B,SAAS,EAAE,yBAAc;QACzB,OAAO,EAAE,yBAAc;QACvB,SAAS,EAAE;YACT,OAAO,EAAE,yBAAc;YACvB,UAAU,EAAE,yBAAc;YAC1B,KAAK,EAAE,yBAAc;SACtB;KACF;IACD,MAAM,EAAE;QACN,WAAW,EAAE,IAAI,GAAG,GAAG,GAAG,GAAG;QAC7B,CAAC,EAAE,EAAE,CAAC,KAAK;QACX,SAAS,EAAE,KAAK;QAChB,SAAS,EAAE,UAAU;KACtB;IACD,WAAW;IACX,IAAI,EAAE,eAAM;IACZ,WAAW,EAAX,mBAAW;IAEX,cAAc,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,QAAQ,EAAE,EAAE;QAC/C,MAAM,CAAC,GAAG,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;QACtB,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,QAAQ,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpD,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3B,QAAQ,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9C,CAAC;CACF,CAAC,CAAC;AAEH;;;;GAIG;AACU,QAAA,iBAAiB,GAAG,IAAA,4BAAW,EAAC;IAC3C,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;IACZ,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;IACZ,EAAE;IACF,CAAC,EAAE,MAAM,CAAC,+EAA+E,CAAC;IAC1F,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC;IACb,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC;IACb,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;IACZ,GAAG,IAAA,0BAAO,EAAC,eAAM,CAAC;CACnB,CAAC,CAAC"}
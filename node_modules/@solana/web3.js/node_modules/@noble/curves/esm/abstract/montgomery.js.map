{"version": 3, "file": "montgomery.js", "sourceRoot": "", "sources": ["../../src/abstract/montgomery.ts"], "names": [], "mappings": "AAAA,sEAAsE;AACtE,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE,MAAM,cAAc,CAAC;AACxC,OAAO,EACL,QAAQ,EACR,eAAe,EACf,WAAW,EACX,eAAe,EACf,cAAc,GACf,MAAM,YAAY,CAAC;AAEpB,MAAM,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AACtB,MAAM,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AAwBtB,SAAS,YAAY,CAAC,KAAgB;IACpC,cAAc,CACZ,KAAK,EACL;QACE,CAAC,EAAE,QAAQ;KACZ,EACD;QACE,cAAc,EAAE,eAAe;QAC/B,WAAW,EAAE,eAAe;QAC5B,iBAAiB,EAAE,UAAU;QAC7B,MAAM,EAAE,UAAU;QAClB,UAAU,EAAE,UAAU;QACtB,EAAE,EAAE,QAAQ;KACb,CACF,CAAC;IACF,eAAe;IACf,OAAO,MAAM,CAAC,MAAM,CAAC,EAAE,GAAG,KAAK,EAAW,CAAC,CAAC;AAC9C,CAAC;AAED,4IAA4I;AAC5I,0CAA0C;AAC1C,MAAM,UAAU,UAAU,CAAC,QAAmB;IAC5C,MAAM,KAAK,GAAG,YAAY,CAAC,QAAQ,CAAC,CAAC;IACrC,MAAM,EAAE,CAAC,EAAE,GAAG,KAAK,CAAC;IACpB,MAAM,IAAI,GAAG,CAAC,CAAS,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACtC,MAAM,cAAc,GAAG,KAAK,CAAC,cAAc,CAAC;IAC5C,MAAM,eAAe,GAAG,IAAI,CAAC,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC,CAAC;IACtD,MAAM,QAAQ,GAAG,KAAK,CAAC,WAAW,CAAC;IACnC,MAAM,iBAAiB,GAAG,KAAK,CAAC,iBAAiB,IAAI,CAAC,CAAC,KAAiB,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC;IACpF,MAAM,UAAU,GAAG,KAAK,CAAC,UAAU,IAAI,CAAC,CAAC,CAAS,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAEjF,kDAAkD;IAClD;;;;;;;;MAQE;IACF,SAAS,KAAK,CAAC,IAAY,EAAE,GAAW,EAAE,GAAW;QACnD,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC;QACvC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,KAAK,CAAC,CAAC;QACxB,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,KAAK,CAAC,CAAC;QACxB,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;IACpB,CAAC;IAED,gBAAgB;IAChB,sEAAsE;IACtE,MAAM,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;IAC9C;;;;;OAKG;IACH,SAAS,gBAAgB,CAAC,CAAS,EAAE,MAAc;QACjD,QAAQ,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;QACzB,QAAQ,CAAC,QAAQ,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;QACnC,kFAAkF;QAClF,mDAAmD;QACnD,MAAM,CAAC,GAAG,MAAM,CAAC;QACjB,MAAM,GAAG,GAAG,CAAC,CAAC;QACd,IAAI,GAAG,GAAG,GAAG,CAAC;QACd,IAAI,GAAG,GAAG,GAAG,CAAC;QACd,IAAI,GAAG,GAAG,CAAC,CAAC;QACZ,IAAI,GAAG,GAAG,GAAG,CAAC;QACd,IAAI,IAAI,GAAG,GAAG,CAAC;QACf,IAAI,EAAoB,CAAC;QACzB,KAAK,IAAI,CAAC,GAAG,MAAM,CAAC,cAAc,GAAG,CAAC,CAAC,EAAE,CAAC,IAAI,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC;YACvD,MAAM,GAAG,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,GAAG,CAAC;YAC3B,IAAI,IAAI,GAAG,CAAC;YACZ,EAAE,GAAG,KAAK,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;YAC3B,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;YACZ,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;YACZ,EAAE,GAAG,KAAK,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;YAC3B,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;YACZ,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;YACZ,IAAI,GAAG,GAAG,CAAC;YAEX,MAAM,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC;YACpB,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YACvB,MAAM,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC;YACpB,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YACvB,MAAM,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC;YAClB,MAAM,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC;YACpB,MAAM,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC;YACpB,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YACvB,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YACvB,MAAM,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC;YACrB,MAAM,KAAK,GAAG,EAAE,GAAG,EAAE,CAAC;YACtB,GAAG,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC;YACxB,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC;YACtC,GAAG,GAAG,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;YACpB,GAAG,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACvC,CAAC;QACD,qCAAqC;QACrC,EAAE,GAAG,KAAK,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QAC3B,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;QACZ,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;QACZ,qCAAqC;QACrC,EAAE,GAAG,KAAK,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QAC3B,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;QACZ,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;QACZ,cAAc;QACd,MAAM,EAAE,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC;QAC3B,6BAA6B;QAC7B,OAAO,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC,CAAC;IACxB,CAAC;IAED,SAAS,iBAAiB,CAAC,CAAS;QAClC,OAAO,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,eAAe,CAAC,CAAC;IACnD,CAAC;IAED,SAAS,iBAAiB,CAAC,IAAS;QAClC,qEAAqE;QACrE,wDAAwD;QACxD,MAAM,CAAC,GAAG,WAAW,CAAC,cAAc,EAAE,IAAI,EAAE,eAAe,CAAC,CAAC;QAC7D,IAAI,QAAQ,KAAK,EAAE;YAAE,CAAC,CAAC,EAAE,CAAC,IAAI,GAAG,CAAC,CAAC,cAAc;QACjD,OAAO,eAAe,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IACD,SAAS,YAAY,CAAC,CAAM;QAC1B,MAAM,KAAK,GAAG,WAAW,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;QACvC,MAAM,GAAG,GAAG,KAAK,CAAC,MAAM,CAAC;QACzB,IAAI,GAAG,KAAK,eAAe,IAAI,GAAG,KAAK,QAAQ;YAC7C,MAAM,IAAI,KAAK,CAAC,YAAY,eAAe,OAAO,QAAQ,eAAe,GAAG,EAAE,CAAC,CAAC;QAClF,OAAO,eAAe,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC,CAAC;IACnD,CAAC;IACD,SAAS,UAAU,CAAC,MAAW,EAAE,CAAM;QACrC,MAAM,MAAM,GAAG,iBAAiB,CAAC,CAAC,CAAC,CAAC;QACpC,MAAM,OAAO,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC;QACrC,MAAM,EAAE,GAAG,gBAAgB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;QAC7C,kCAAkC;QAClC,sCAAsC;QACtC,IAAI,EAAE,KAAK,GAAG;YAAE,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;QAC1E,OAAO,iBAAiB,CAAC,EAAE,CAAC,CAAC;IAC/B,CAAC;IACD,kFAAkF;IAClF,MAAM,OAAO,GAAG,iBAAiB,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;IAC5C,SAAS,cAAc,CAAC,MAAW;QACjC,OAAO,UAAU,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IACrC,CAAC;IAED,OAAO;QACL,UAAU;QACV,cAAc;QACd,eAAe,EAAE,CAAC,UAAe,EAAE,SAAc,EAAE,EAAE,CAAC,UAAU,CAAC,UAAU,EAAE,SAAS,CAAC;QACvF,YAAY,EAAE,CAAC,UAAe,EAAc,EAAE,CAAC,cAAc,CAAC,UAAU,CAAC;QACzE,KAAK,EAAE,EAAE,gBAAgB,EAAE,GAAG,EAAE,CAAC,KAAK,CAAC,WAAY,CAAC,KAAK,CAAC,WAAW,CAAC,EAAE;QACxE,OAAO,EAAE,OAAO;KACjB,CAAC;AACJ,CAAC"}
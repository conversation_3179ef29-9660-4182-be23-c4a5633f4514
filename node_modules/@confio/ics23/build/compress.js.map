{"version": 3, "file": "compress.js", "sourceRoot": "", "sources": ["../src/compress.ts"], "names": [], "mappings": ";;;AAAA,qDAA8C;AAE9C,SAAgB,QAAQ,CACtB,KAA6B;IAE7B,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE;QAChB,OAAO,KAAK,CAAC;KACd;IACD,OAAO,EAAE,UAAU,EAAE,aAAa,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC;AACpD,CAAC;AAPD,4BAOC;AAED,SAAgB,UAAU,CACxB,KAA6B;IAE7B,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE;QACrB,OAAO,KAAK,CAAC;KACd;IACD,OAAO,EAAE,KAAK,EAAE,eAAe,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE,CAAC;AACtD,CAAC;AAPD,gCAOC;AAED,SAAS,aAAa,CAAC,KAAwB;IAC7C,MAAM,QAAQ,GAAkC,EAAE,CAAC;IACnD,MAAM,MAAM,GAAqB,EAAE,CAAC;IACpC,MAAM,QAAQ,GAAG,IAAI,GAAG,EAAsB,CAAC;IAE/C,KAAK,MAAM,KAAK,IAAI,KAAK,CAAC,OAAQ,EAAE;QAClC,IAAI,KAAK,CAAC,KAAK,EAAE;YACf,MAAM,MAAM,GAAG,EAAE,KAAK,EAAE,aAAa,CAAC,KAAK,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,CAAC,EAAE,CAAC;YACvE,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;SACvB;aAAM,IAAI,KAAK,CAAC,QAAQ,EAAE;YACzB,MAAM,GAAG,GAAG,KAAK,CAAC,QAAQ,CAAC;YAC3B,MAAM,MAAM,GAAG;gBACb,QAAQ,EAAE;oBACR,GAAG,EAAE,GAAG,CAAC,GAAG;oBACZ,IAAI,EAAE,aAAa,CAAC,GAAG,CAAC,IAAI,EAAE,MAAM,EAAE,QAAQ,CAAC;oBAC/C,KAAK,EAAE,aAAa,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,CAAC;iBAClD;aACF,CAAC;YACF,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;SACvB;aAAM;YACL,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;SAC3D;KACF;IAED,OAAO;QACL,OAAO,EAAE,QAAQ;QACjB,YAAY,EAAE,MAAM;KACrB,CAAC;AACJ,CAAC;AAED,SAAS,aAAa,CACpB,KAA+C,EAC/C,MAAwB,EACxB,QAAiC;IAEjC,IAAI,CAAC,KAAK,EAAE;QACV,OAAO,SAAS,CAAC;KAClB;IAED,MAAM,IAAI,GAAG,KAAK,CAAC,IAAK,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;QACrC,MAAM,GAAG,GAAG,iBAAK,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,MAAM,EAAE,CAAC;QACjD,IAAI,GAAG,GAAG,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAC5B,IAAI,GAAG,KAAK,SAAS,EAAE;YACrB,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC;YACpB,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACnB,QAAQ,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;SACxB;QACD,OAAO,GAAG,CAAC;IACb,CAAC,CAAC,CAAC;IAEH,OAAO;QACL,GAAG,EAAE,KAAK,CAAC,GAAG;QACd,KAAK,EAAE,KAAK,CAAC,KAAK;QAClB,IAAI,EAAE,KAAK,CAAC,IAAI;QAChB,IAAI;KACL,CAAC;AACJ,CAAC;AAED,SAAS,eAAe,CACtB,KAAkC;IAElC,MAAM,MAAM,GAAG,KAAK,CAAC,YAAa,CAAC;IACnC,MAAM,OAAO,GAAG,KAAK,CAAC,OAAQ,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;QAC1C,IAAI,IAAI,CAAC,KAAK,EAAE;YACd,OAAO,EAAE,KAAK,EAAE,eAAe,CAAC,IAAI,CAAC,KAAK,EAAE,MAAM,CAAC,EAAE,CAAC;SACvD;aAAM,IAAI,IAAI,CAAC,QAAQ,EAAE;YACxB,MAAM,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC;YAC1B,OAAO;gBACL,QAAQ,EAAE;oBACR,GAAG,EAAE,GAAG,CAAC,GAAG;oBACZ,IAAI,EAAE,eAAe,CAAC,GAAG,CAAC,IAAI,EAAE,MAAM,CAAC;oBACvC,KAAK,EAAE,eAAe,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC;iBAC1C;aACF,CAAC;SACH;aAAM;YACL,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;SAC3D;IACH,CAAC,CAAC,CAAC;IACH,OAAO;QACL,OAAO;KACR,CAAC;AACJ,CAAC;AAED,SAAS,eAAe,CACtB,KAAyD,EACzD,MAAiC;IAEjC,IAAI,CAAC,KAAK,EAAE;QACV,OAAO,SAAS,CAAC;KAClB;IACD,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,KAAK,CAAC;IACzC,MAAM,OAAO,GAAG,CAAC,IAAI,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;IACvD,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC;AAC7C,CAAC"}
{"version": 3, "file": "testvectors.spec.js", "sourceRoot": "", "sources": ["../src/testvectors.spec.ts"], "names": [], "mappings": ";;AAAA,2BAAkC;AAElC,yCAAsC;AACtC,qDAA8C;AAC9C,mCAKiB;AACjB,qCAAoD;AACpD,yDAA6C;AAE7C,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;IAYtC,SAAS,QAAQ,CAAC,QAAgB;QAChC,MAAM,OAAO,GAAG,IAAA,iBAAY,EAAC,QAAQ,CAAC,CAAC,QAAQ,EAAE,CAAC;QAClD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QACxD,MAAM,CAAC,KAAK,CAAC,CAAC,WAAW,EAAE,CAAC;QAC5B,MAAM,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,CAAC;QAC3B,MAAM,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,CAAC;QAE1B,MAAM,MAAM,GAAG,iBAAK,CAAC,eAAe,CAAC,MAAM,CAAC,IAAA,0BAAO,EAAC,KAAK,CAAC,CAAC,CAAC;QAE5D,MAAM,IAAI,GAAG;YACX,IAAI,EAAE,IAAA,0BAAO,EAAC,IAAI,CAAC;YACnB,GAAG,EAAE,IAAA,0BAAO,EAAC,GAAG,CAAC;YACjB,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC,IAAA,0BAAO,EAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS;SAC1C,CAAC;QAEF,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC;IACjC,CAAC;IAOD,SAAS,kBAAkB,CAAC,QAAgB,EAAE,IAAsB;QAClE,MAAM,EACJ,KAAK,EACL,IAAI,EAAE,EAAE,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,GAC3B,GAAG,QAAQ,CAAC,QAAQ,CAAC,CAAC;QACvB,IAAI,KAAK,EAAE;YACT,MAAM,KAAK,GAAG,IAAA,wBAAgB,EAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;YAC9D,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SAC1B;aAAM;YACL,MAAM,KAAK,GAAG,IAAA,2BAAmB,EAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC;YAC1D,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SAC1B;IACH,CAAC;IAED,EAAE,CAAC,wBAAwB,EAAE,GAAG,EAAE;QAChC,kBAAkB,CAAC,kCAAkC,EAAE,iBAAQ,CAAC,CAAC;IACnE,CAAC,CAAC,CAAC;IACH,EAAE,CAAC,yBAAyB,EAAE,GAAG,EAAE;QACjC,kBAAkB,CAAC,mCAAmC,EAAE,iBAAQ,CAAC,CAAC;IACpE,CAAC,CAAC,CAAC;IACH,EAAE,CAAC,0BAA0B,EAAE,GAAG,EAAE;QAClC,kBAAkB,CAAC,oCAAoC,EAAE,iBAAQ,CAAC,CAAC;IACrE,CAAC,CAAC,CAAC;IACH,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;QAC3C,kBAAkB,CAAC,qCAAqC,EAAE,iBAAQ,CAAC,CAAC;IACtE,CAAC,CAAC,CAAC;IACH,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;QAC5C,kBAAkB,CAAC,sCAAsC,EAAE,iBAAQ,CAAC,CAAC;IACvE,CAAC,CAAC,CAAC;IACH,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;QAC7C,kBAAkB,CAAC,uCAAuC,EAAE,iBAAQ,CAAC,CAAC;IACxE,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;QACtC,kBAAkB,CAChB,wCAAwC,EACxC,uBAAc,CACf,CAAC;IACJ,CAAC,CAAC,CAAC;IACH,EAAE,CAAC,+BAA+B,EAAE,GAAG,EAAE;QACvC,kBAAkB,CAChB,yCAAyC,EACzC,uBAAc,CACf,CAAC;IACJ,CAAC,CAAC,CAAC;IACH,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;QACxC,kBAAkB,CAChB,0CAA0C,EAC1C,uBAAc,CACf,CAAC;IACJ,CAAC,CAAC,CAAC;IACH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;QACjD,kBAAkB,CAChB,2CAA2C,EAC3C,uBAAc,CACf,CAAC;IACJ,CAAC,CAAC,CAAC;IACH,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;QAClD,kBAAkB,CAChB,4CAA4C,EAC5C,uBAAc,CACf,CAAC;IACJ,CAAC,CAAC,CAAC;IACH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;QACnD,kBAAkB,CAChB,6CAA6C,EAC7C,uBAAc,CACf,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,SAAS,SAAS,CAAC,KAAwB;QACzC,IAAI,IAAI,GAAuB,EAAE,CAAC;QAClC,IAAI,OAAO,GAAiC,EAAE,CAAC;QAE/C,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;YACxB,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC;YACvC,IAAI,GAAG,CAAC,GAAG,IAAI,EAAE,IAAI,CAAC,CAAC;YACvB,IAAI,KAAK,CAAC,KAAK,EAAE;gBACf,OAAO,GAAG,CAAC,GAAG,OAAO,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,EAAE,CAAC,CAAC;aAChD;iBAAM,IAAI,KAAK,CAAC,QAAQ,EAAE;gBACzB,OAAO,GAAG,CAAC,GAAG,OAAO,EAAE,EAAE,QAAQ,EAAE,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC;aACtD;SACF;QACD,MAAM,MAAM,GAA2B;YACrC,KAAK,EAAE;gBACL,OAAO,EAAE,OAA8B;aACxC;SACF,CAAC;QAEF,OAAO;YACL,KAAK,EAAE,MAAM;YACb,IAAI,EAAE,IAAI;SACX,CAAC;IACJ,CAAC;IAED,SAAS,aAAa,CACpB,KAA6B,EAC7B,IAAsB,EACtB,IAAa;QAEb,MAAM,EAAE,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC;QAClC,IAAI,KAAK,EAAE;YACT,IAAI,KAAK,GAAG,IAAA,wBAAgB,EAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;YAC5D,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACzB,MAAM,KAAK,GAAG,IAAI,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;YACtC,KAAK,GAAG,IAAA,6BAAqB,EAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;YACxD,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SAC1B;aAAM;YACL,IAAI,KAAK,GAAG,IAAA,2BAAmB,EAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC;YACxD,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACzB,MAAM,IAAI,GAA0B,CAAC,GAAG,CAAC,CAAC;YAC1C,KAAK,GAAG,IAAA,gCAAwB,EAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;YAC1D,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SAC1B;IACH,CAAC;IAED,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;QAC1C,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,SAAS,CAAC;YAChC,kCAAkC;YAClC,mCAAmC;YACnC,oCAAoC;YACpC,qCAAqC;YACrC,sCAAsC;YACtC,uCAAuC;SACxC,CAAC,CAAC;QACH,aAAa,CAAC,KAAK,EAAE,iBAAQ,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1C,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;QAC7C,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,SAAS,CAAC;YAChC,kCAAkC;YAClC,mCAAmC;YACnC,oCAAoC;YACpC,qCAAqC;YACrC,sCAAsC;YACtC,uCAAuC;SACxC,CAAC,CAAC;QACH,aAAa,CAAC,KAAK,EAAE,iBAAQ,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1C,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;QACrD,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,SAAS,CAAC;YAChC,kCAAkC;YAClC,mCAAmC;YACnC,oCAAoC;YACpC,qCAAqC;YACrC,sCAAsC;YACtC,uCAAuC;SACxC,CAAC,CAAC;QACH,MAAM,KAAK,GAAG,IAAA,mBAAQ,EAAC,KAAK,CAAC,CAAC;QAE9B,6CAA6C;QAC7C,MAAM,OAAO,GAAG,iBAAK,CAAC,eAAe,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,MAAM,EAAE,CAAC;QAC7D,MAAM,QAAQ,GAAG,iBAAK,CAAC,eAAe,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,MAAM,EAAE,CAAC;QAC9D,MAAM,QAAQ,GAAG,iBAAK,CAAC,eAAe,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,MAAM,EAAE,CAAC;QAC9D,MAAM,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAClC,MAAM,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAEtC,aAAa,CAAC,KAAK,EAAE,iBAAQ,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1C,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;QACxD,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,SAAS,CAAC;YAChC,kCAAkC;YAClC,mCAAmC;YACnC,oCAAoC;YACpC,qCAAqC;YACrC,sCAAsC;YACtC,uCAAuC;SACxC,CAAC,CAAC;QACH,MAAM,KAAK,GAAG,IAAA,mBAAQ,EAAC,KAAK,CAAC,CAAC;QAE9B,6CAA6C;QAC7C,MAAM,OAAO,GAAG,iBAAK,CAAC,eAAe,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,MAAM,EAAE,CAAC;QAC7D,MAAM,QAAQ,GAAG,iBAAK,CAAC,eAAe,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,MAAM,EAAE,CAAC;QAC9D,MAAM,QAAQ,GAAG,iBAAK,CAAC,eAAe,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,MAAM,EAAE,CAAC;QAC9D,MAAM,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAClC,MAAM,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAEtC,aAAa,CAAC,KAAK,EAAE,iBAAQ,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1C,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;QAChD,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,SAAS,CAAC;YAChC,wCAAwC;YACxC,yCAAyC;YACzC,0CAA0C;YAC1C,2CAA2C;YAC3C,4CAA4C;YAC5C,6CAA6C;SAC9C,CAAC,CAAC;QACH,aAAa,CAAC,KAAK,EAAE,uBAAc,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;IAChD,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;QACnD,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,SAAS,CAAC;YAChC,wCAAwC;YACxC,yCAAyC;YACzC,0CAA0C;YAC1C,2CAA2C;YAC3C,4CAA4C;YAC5C,6CAA6C;SAC9C,CAAC,CAAC;QACH,aAAa,CAAC,KAAK,EAAE,uBAAc,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;IAChD,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC"}
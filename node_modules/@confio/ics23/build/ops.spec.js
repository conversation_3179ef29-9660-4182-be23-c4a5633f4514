"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const codecimpl_1 = require("./generated/codecimpl");
const ops_1 = require("./ops");
const testhelpers_spec_1 = require("./testhelpers.spec");
describe("doHash", () => {
    it("sha256 hashes food", () => {
        // echo -n food | sha256sum
        const hash = (0, ops_1.doHash)(codecimpl_1.ics23.HashOp.SHA256, (0, testhelpers_spec_1.toAscii)("food"));
        expect(hash).toEqual((0, testhelpers_spec_1.fromHex)("c1f026582fe6e8cb620d0c85a72fe421ddded756662a8ec00ed4c297ad10676b"));
    });
    it("sha512 hashes food", () => {
        // echo -n food | sha512sum
        const hash = (0, ops_1.doHash)(codecimpl_1.ics23.HashOp.SHA512, (0, testhelpers_spec_1.toAscii)("food"));
        expect(hash).toEqual((0, testhelpers_spec_1.fromHex)("c235548cfe84fc87678ff04c9134e060cdcd7512d09ed726192151a995541ed8db9fda5204e72e7ac268214c322c17787c70530513c59faede52b7dd9ce64331"));
    });
    it("ripemd160 hashes food", () => {
        // echo -n food | openssl dgst -rmd160 -hex | cut -d' ' -f2
        const hash = (0, ops_1.doHash)(codecimpl_1.ics23.HashOp.RIPEMD160, (0, testhelpers_spec_1.toAscii)("food"));
        expect(hash).toEqual((0, testhelpers_spec_1.fromHex)("b1ab9988c7c7c5ec4b2b291adfeeee10e77cdd46"));
    });
    it("'bitcoin' hashes food", () => {
        // echo -n c1f026582fe6e8cb620d0c85a72fe421ddded756662a8ec00ed4c297ad10676b | xxd -r -p | openssl dgst -rmd160 -hex
        const hash = (0, ops_1.doHash)(codecimpl_1.ics23.HashOp.BITCOIN, (0, testhelpers_spec_1.toAscii)("food"));
        expect(hash).toEqual((0, testhelpers_spec_1.fromHex)("0bcb587dfb4fc10b36d57f2bba1878f139b75d24"));
    });
});
describe("applyLeaf", () => {
    it("hashes foobar", () => {
        const op = { hash: codecimpl_1.ics23.HashOp.SHA256 };
        const key = (0, testhelpers_spec_1.toAscii)("foo");
        const value = (0, testhelpers_spec_1.toAscii)("bar");
        // echo -n foobar | sha256sum
        const expected = (0, testhelpers_spec_1.fromHex)("c3ab8ff13720e8ad9047dd39466b3c8974e592c2fa383d4a3960714caef0c4f2");
        expect((0, ops_1.applyLeaf)(op, key, value)).toEqual(expected);
    });
    it("hashes foobaz with sha-512", () => {
        const op = { hash: codecimpl_1.ics23.HashOp.SHA512 };
        const key = (0, testhelpers_spec_1.toAscii)("foo");
        const value = (0, testhelpers_spec_1.toAscii)("baz");
        // echo -n foobaz | sha512sum
        const expected = (0, testhelpers_spec_1.fromHex)("4f79f191298ec7461d60136c60f77c2ae8ddd85dbf6168bb925092d51bfb39b559219b39ae5385ba04946c87f64741385bef90578ea6fe6dac85dbf7ad3f79e1");
        expect((0, ops_1.applyLeaf)(op, key, value)).toEqual(expected);
    });
    it("hashes food with sha-512/256", () => {
        const op = { hash: codecimpl_1.ics23.HashOp.SHA512_256 };
        const key = (0, testhelpers_spec_1.toAscii)("fo");
        const value = (0, testhelpers_spec_1.toAscii)("od");
        const expected = (0, testhelpers_spec_1.fromHex)("5b3a452a6acbf1fc1e553a40c501585d5bd3cca176d562e0a0e19a3c43804e88");
        expect((0, ops_1.applyLeaf)(op, key, value)).toEqual(expected);
    });
    it("hashes foobar (different breakpoint)", () => {
        const op = { hash: codecimpl_1.ics23.HashOp.SHA256 };
        const key = (0, testhelpers_spec_1.toAscii)("f");
        const value = (0, testhelpers_spec_1.toAscii)("oobar");
        // echo -n foobar | sha256sum
        const expected = (0, testhelpers_spec_1.fromHex)("c3ab8ff13720e8ad9047dd39466b3c8974e592c2fa383d4a3960714caef0c4f2");
        expect((0, ops_1.applyLeaf)(op, key, value)).toEqual(expected);
    });
    it("hashes with length prefix", () => {
        const op = {
            hash: codecimpl_1.ics23.HashOp.SHA256,
            length: codecimpl_1.ics23.LengthOp.VAR_PROTO,
        };
        // echo -n food | xxd -ps
        const key = (0, testhelpers_spec_1.toAscii)("food"); // 04666f6f64
        const value = (0, testhelpers_spec_1.toAscii)("some longer text"); // 10736f6d65206c6f6e6765722074657874
        // echo -n 04666f6f6410736f6d65206c6f6e6765722074657874 | xxd -r -p | sha256sum -b
        const expected = (0, testhelpers_spec_1.fromHex)("b68f5d298e915ae1753dd333da1f9cf605411a5f2e12516be6758f365e6db265");
        expect((0, ops_1.applyLeaf)(op, key, value)).toEqual(expected);
    });
    it("hashes with length prefix (fixed 32-bit little-endian encoding)", () => {
        const op = {
            hash: codecimpl_1.ics23.HashOp.SHA256,
            length: codecimpl_1.ics23.LengthOp.FIXED32_LITTLE,
        };
        // echo -n food | xxd -ps
        const key = (0, testhelpers_spec_1.toAscii)("food"); // 04000000666f6f64
        const value = (0, testhelpers_spec_1.toAscii)("some longer text"); // 10000000736f6d65206c6f6e6765722074657874
        // echo -n 04000000666f6f6410000000736f6d65206c6f6e6765722074657874 | xxd -r -p | sha256sum
        const expected = (0, testhelpers_spec_1.fromHex)("c853652437be02501c674744bf2a2b45d92a0a9f29c4b1044010fb3e2d43a949");
        expect((0, ops_1.applyLeaf)(op, key, value)).toEqual(expected);
    });
    it("hashes with prehash and length prefix", () => {
        const op = {
            hash: codecimpl_1.ics23.HashOp.SHA256,
            length: codecimpl_1.ics23.LengthOp.VAR_PROTO,
            prehashValue: codecimpl_1.ics23.HashOp.SHA256,
        };
        const key = (0, testhelpers_spec_1.toAscii)("food"); // 04666f6f64
        // echo -n yet another long string | sha256sum
        const value = (0, testhelpers_spec_1.toAscii)("yet another long string"); // 20a48c2d4f67b9f80374938535285ed285819d8a5a8fc1fccd1e3244e437cf290d
        // echo -n 04666f6f6420a48c2d4f67b9f80374938535285ed285819d8a5a8fc1fccd1e3244e437cf290d | xxd -r -p | sha256sum
        const expected = (0, testhelpers_spec_1.fromHex)("87e0483e8fb624aef2e2f7b13f4166cda485baa8e39f437c83d74c94bedb148f");
        expect((0, ops_1.applyLeaf)(op, key, value)).toEqual(expected);
    });
    it("requires key", () => {
        const op = {
            hash: codecimpl_1.ics23.HashOp.SHA256,
        };
        const key = (0, testhelpers_spec_1.toAscii)("food");
        const value = (0, testhelpers_spec_1.toAscii)("");
        expect(() => (0, ops_1.applyLeaf)(op, key, value)).toThrow();
    });
    it("requires value", () => {
        const op = {
            hash: codecimpl_1.ics23.HashOp.SHA256,
        };
        const key = (0, testhelpers_spec_1.toAscii)("");
        const value = (0, testhelpers_spec_1.toAscii)("time");
        expect(() => (0, ops_1.applyLeaf)(op, key, value)).toThrow();
    });
});
describe("applyInner", () => {
    it("hash child with prefix and suffix", () => {
        const op = {
            hash: codecimpl_1.ics23.HashOp.SHA256,
            prefix: (0, testhelpers_spec_1.fromHex)("0123456789"),
            suffix: (0, testhelpers_spec_1.fromHex)("deadbeef"),
        };
        const child = (0, testhelpers_spec_1.fromHex)("00cafe00");
        // echo -n 012345678900cafe00deadbeef | xxd -r -p | sha256sum
        const expected = (0, testhelpers_spec_1.fromHex)("0339f76086684506a6d42a60da4b5a719febd4d96d8b8d85ae92849e3a849a5e");
        expect((0, ops_1.applyInner)(op, child)).toEqual(expected);
    });
    it("requies child", () => {
        const op = {
            hash: codecimpl_1.ics23.HashOp.SHA256,
            prefix: (0, testhelpers_spec_1.fromHex)("0123456789"),
            suffix: (0, testhelpers_spec_1.fromHex)("deadbeef"),
        };
        expect(() => (0, ops_1.applyInner)(op, (0, testhelpers_spec_1.fromHex)(""))).toThrow();
    });
    it("hash child with only prefix", () => {
        const op = {
            hash: codecimpl_1.ics23.HashOp.SHA256,
            prefix: (0, testhelpers_spec_1.fromHex)("00204080a0c0e0"),
        };
        const child = (0, testhelpers_spec_1.fromHex)("ffccbb997755331100");
        // echo -n 00204080a0c0e0ffccbb997755331100 | xxd -r -p | sha256sum
        const expected = (0, testhelpers_spec_1.fromHex)("45bece1678cf2e9f4f2ae033e546fc35a2081b2415edcb13121a0e908dca1927");
        expect((0, ops_1.applyInner)(op, child)).toEqual(expected);
    });
    it("hash child with only suffix", () => {
        const op = {
            hash: codecimpl_1.ics23.HashOp.SHA256,
            suffix: (0, testhelpers_spec_1.toAscii)(" just kidding!"),
        };
        const child = (0, testhelpers_spec_1.toAscii)("this is a sha256 hash, really....");
        // echo -n 'this is a sha256 hash, really.... just kidding!'  | sha256sum
        const expected = (0, testhelpers_spec_1.fromHex)("79ef671d27e42a53fba2201c1bbc529a099af578ee8a38df140795db0ae2184b");
        expect((0, ops_1.applyInner)(op, child)).toEqual(expected);
    });
});
//# sourceMappingURL=ops.spec.js.map
import { ics23 } from "./generated/codecimpl";
export declare const iavlSpec: ics23.IProofSpec;
export declare const tendermintSpec: ics23.IProofSpec;
export declare const smtSpec: ics23.IProofSpec;
export declare type CommitmentRoot = Uint8Array;
export declare function verifyExistence(proof: ics23.IExistenceProof, spec: ics23.IProofSpec, root: CommitmentRoot, key: Uint8Array, value: Uint8Array): void;
export declare function verifyNonExistence(proof: ics23.INonExistenceProof, spec: ics23.IProofSpec, root: CommitmentRoot, key: Uint8Array): void;
export declare function calculateExistenceRoot(proof: ics23.IExistenceProof): CommitmentRoot;
export declare function ensureSpec(proof: ics23.IExistenceProof, spec: ics23.IProofSpec): void;
export declare function ensureLeftNeighbor(spec: ics23.IInnerSpec, left: readonly ics23.IInnerOp[], right: readonly ics23.IInnerOp[]): void;

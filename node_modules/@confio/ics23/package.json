{"name": "@confio/ics23", "version": "0.6.8", "description": "Merkle proof verification library - implements Cosmos ICS23 Spec", "main": "build/index.js", "types": "build/index.d.ts", "repository": {"type": "git", "url": "https://github.com/confio/ics23/tree/master/js"}, "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org/"}, "author": "<PERSON>", "license": "Apache-2.0", "private": false, "files": ["build/**", "yarn.lock"], "scripts": {"lint": "eslint --max-warnings 0 './src/**/*.ts'", "lint:fix": "yarn lint --fix", "format": "prettier --write --loglevel warn \"./src/**/*.ts\"", "prebuild": "yarn lint && yarn format", "prepublish": "yarn build", "test": "yarn -s build && node jasmine-testrunner.js", "build": "shx rm -rf ./build && tsc && shx cp -r src/generated build/generated", "pack-proto": "pbjs -t static-module -w commonjs -o src/generated/codecimpl.js ../proofs.proto", "define-proto": "pbts src/generated/codecimpl.js -o src/generated/codecimpl.d.ts", "protoc": "yarn pack-proto && yarn define-proto && yarn format"}, "devDependencies": {"@types/jasmine": "^3.5.0", "@typescript-eslint/eslint-plugin": "^5.10.2", "@typescript-eslint/parser": "^5.10.2", "eslint": "^8.8.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-import": "^2.25.4", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-simple-import-sort": "^7.0.0", "jasmine": "^3.5.0", "jasmine-console-reporter": "^3.1.0", "prettier": "^2.5.1", "shx": "^0.3.4", "source-map-support": "^0.5.16", "typescript": "~4.5"}, "dependencies": {"@noble/hashes": "^1.0.0", "protobufjs": "^6.8.8"}}
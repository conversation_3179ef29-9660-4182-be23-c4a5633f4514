"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.bestNumber = void 0;
const util_js_1 = require("./util.js");
/**
 * @name bestNumber
 * @returns The latest block number.
 * @example
 * <BR>
 *
 * ```javascript
 * api.derive.chain.bestNumber((blockNumber) => {
 *   console.log(`the current best block is #${blockNumber}`);
 * });
 * ```
 */
exports.bestNumber = (0, util_js_1.createBlockNumberDerive)((api) => api.rpc.chain.subscribeNewHeads());

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getBlockByNumber = getBlockByNumber;
const rxjs_1 = require("rxjs");
const index_js_1 = require("../util/index.js");
function getBlockByNumber(instanceId, api) {
    return (0, index_js_1.memo)(instanceId, (blockNumber) => api.rpc.chain.getBlockHash(blockNumber).pipe((0, rxjs_1.switchMap)((h) => api.derive.chain.getBlock(h))));
}

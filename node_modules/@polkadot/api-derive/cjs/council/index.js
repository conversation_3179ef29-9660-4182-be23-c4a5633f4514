"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.prime = exports.proposals = exports.proposalHashes = exports.proposalCount = exports.proposal = exports.hasProposals = exports.members = void 0;
const tslib_1 = require("tslib");
const index_js_1 = require("../collective/index.js");
tslib_1.__exportStar(require("./votes.js"), exports);
tslib_1.__exportStar(require("./votesOf.js"), exports);
exports.members = (0, index_js_1.members)('council');
exports.hasProposals = (0, index_js_1.hasProposals)('council');
exports.proposal = (0, index_js_1.proposal)('council');
exports.proposalCount = (0, index_js_1.proposalCount)('council');
exports.proposalHashes = (0, index_js_1.proposalHashes)('council');
exports.proposals = (0, index_js_1.proposals)('council');
exports.prime = (0, index_js_1.prime)('council');

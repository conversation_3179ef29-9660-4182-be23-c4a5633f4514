"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.votesOf = votesOf;
const rxjs_1 = require("rxjs");
const index_js_1 = require("../util/index.js");
function votesOf(instanceId, api) {
    return (0, index_js_1.memo)(instanceId, (accountId) => api.derive.council.votes().pipe((0, rxjs_1.map)((votes) => (votes.find(([from]) => from.eq(accountId)) ||
        [null, { stake: api.registry.createType('Balance'), votes: [] }])[1])));
}

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.nextElected = nextElected;
exports.validators = validators;
const rxjs_1 = require("rxjs");
const index_js_1 = require("../util/index.js");
function nextElected(instanceId, api) {
    return (0, index_js_1.memo)(instanceId, () => 
    // Compatibility for future generation changes in staking.
    api.query.staking.erasStakersPaged
        ? api.derive.session.indexes().pipe(
        // only populate for next era in the last session, so track both here - entries are not
        // subscriptions, so we need a trigger - currentIndex acts as that trigger to refresh
        (0, rxjs_1.switchMap)(({ currentEra }) => api.query.staking.erasStakersPaged.keys(currentEra)), 
        // Dedupe any duplicates
        (0, rxjs_1.map)((keys) => [...new Set(keys.map(({ args: [, accountId] }) => accountId.toString()))].map((a) => api.registry.createType('AccountId', a))))
        : api.query.staking.erasStakers
            ? api.derive.session.indexes().pipe(
            // only populate for next era in the last session, so track both here - entries are not
            // subscriptions, so we need a trigger - currentIndex acts as that trigger to refresh
            (0, rxjs_1.switchMap)(({ currentEra }) => api.query.staking.erasStakers.keys(currentEra)), 
            // Dedupe any duplicates
            (0, rxjs_1.map)((keys) => [...new Set(keys.map(({ args: [, accountId] }) => accountId.toString()))].map((a) => api.registry.createType('AccountId', a))))
            : api.query.staking['currentElected']());
}
/**
 * @description Retrieve latest list of validators
 */
function validators(instanceId, api) {
    return (0, index_js_1.memo)(instanceId, () => 
    // Sadly the node-template is (for some obscure reason) not comprehensive, so while the derive works
    // in all actual real-world deployed chains, it does create some confusion for limited template chains
    (0, rxjs_1.combineLatest)([
        api.query.session
            ? api.query.session.validators()
            : (0, rxjs_1.of)([]),
        api.query.staking
            ? api.derive.staking.nextElected()
            : (0, rxjs_1.of)([])
    ]).pipe((0, rxjs_1.map)(([validators, nextElected]) => ({
        nextElected: nextElected.length
            ? nextElected
            : validators,
        validators
    }))));
}

{"magicNumber": **********, "metadata": {"v14": {"pallets": [{"name": "System", "storage": {"prefix": "System", "items": [{"name": "Account", "modifier": "<PERSON><PERSON><PERSON>", "type": {"map": {"hashers": ["Blake2_128Concat"], "key": 0, "value": 3}}, "fallback": "0x******************************************************************************************************************************************************0000000080", "docs": [" The full account information for a particular account ID."]}, {"name": "ExtrinsicCount", "modifier": "Optional", "type": {"plain": 4}, "fallback": "0x00", "docs": [" Total extrinsics count for the current block."]}, {"name": "InherentsApplied", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 8}, "fallback": "0x00", "docs": [" Whether all inherents have been applied."]}, {"name": "BlockWeight", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 9}, "fallback": "0x000000000000", "docs": [" The current weight for the block."]}, {"name": "AllExtrinsicsLen", "modifier": "Optional", "type": {"plain": 4}, "fallback": "0x00", "docs": [" Total length (in bytes) for all extrinsics put together, for the current block."]}, {"name": "BlockHash", "modifier": "<PERSON><PERSON><PERSON>", "type": {"map": {"hashers": ["Twox64Concat"], "key": 4, "value": 13}}, "fallback": "0x************************************************************0000", "docs": [" Map of block numbers to block hashes."]}, {"name": "ExtrinsicData", "modifier": "<PERSON><PERSON><PERSON>", "type": {"map": {"hashers": ["Twox64Concat"], "key": 4, "value": 14}}, "fallback": "0x00", "docs": [" Extrinsics data for the current block (maps an extrinsic's index to its data)."]}, {"name": "Number", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 4}, "fallback": "0x00000000", "docs": [" The current block number being processed. Set by `execute_block`."]}, {"name": "ParentHash", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 13}, "fallback": "0x************************************************************0000", "docs": [" Hash of the previous block."]}, {"name": "Digest", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 15}, "fallback": "0x00", "docs": [" Digest of the current block, also part of the block header."]}, {"name": "Events", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 19}, "fallback": "0x00", "docs": [" Events deposited for the current block.", "", " NOTE: The item is unbound and should therefore never be read on chain.", " It could otherwise inflate the PoV size of a block.", "", " Events have a large in-memory size. Box the events to not go out-of-memory", " just in case someone still reads them from within the runtime."]}, {"name": "EventCount", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 4}, "fallback": "0x00000000", "docs": [" The number of events in the `Events<T>` list."]}, {"name": "EventTopics", "modifier": "<PERSON><PERSON><PERSON>", "type": {"map": {"hashers": ["Blake2_128Concat"], "key": 13, "value": 514}}, "fallback": "0x00", "docs": [" Mapping between a topic (represented by T::Hash) and a vector of indexes", " of events in the `<Events<T>>` list.", "", " All topic vectors have deterministic storage locations depending on the topic. This", " allows light-clients to leverage the changes trie storage tracking mechanism and", " in case of changes fetch the list of events of interest.", "", " The value has the type `(BlockNumberFor<T>, EventIndex)` because if we used only just", " the `EventIndex` then in case if the topic has the same contents on the next block", " no notification will be triggered thus the event might be lost."]}, {"name": "LastRuntimeUpgrade", "modifier": "Optional", "type": {"plain": 515}, "fallback": "0x00", "docs": [" Stores the `spec_version` and `spec_name` of when the last runtime upgrade happened."]}, {"name": "UpgradedToU32RefCount", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 8}, "fallback": "0x00", "docs": [" True if we have upgraded so that `type RefCount` is `u32`. False (default) if not."]}, {"name": "UpgradedToTripleRefCount", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 8}, "fallback": "0x00", "docs": [" True if we have upgraded so that AccountInfo contains three types of `RefCount`. False", " (default) if not."]}, {"name": "ExecutionPhase", "modifier": "Optional", "type": {"plain": 513}, "fallback": "0x00", "docs": [" The execution phase of the block."]}, {"name": "AuthorizedUpgrade", "modifier": "Optional", "type": {"plain": 517}, "fallback": "0x00", "docs": [" `Some` if a code upgrade has been authorized."]}]}, "calls": {"type": 142}, "events": {"type": 22}, "constants": [{"name": "BlockWeights", "type": 518, "value": "0x624d186c000b00204aa9d10113ffffffffffffffff4247871900010b30f6a7a72e011366666666666666a6010b0098f73e5d0113ffffffffffffffbf0100004247871900010b307efa11a3011366666666666666e6010b00204aa9d10113ffffffffffffffff01070088526a741300000000000000404247871900000000", "docs": [" Block & extrinsics weights: base values and limits."]}, {"name": "BlockLength", "type": 521, "value": "0x00003c000000500000005000", "docs": [" The maximum length of a block (in bytes)."]}, {"name": "BlockHashCount", "type": 4, "value": "0x60090000", "docs": [" Maximum number of block number to block hash mappings to keep (oldest pruned first)."]}, {"name": "DbWeight", "type": 523, "value": "0x40787d010000000000e1f50500000000", "docs": [" The weight of runtime database operations the runtime can invoke."]}, {"name": "Version", "type": 524, "value": "0x106e6f6465387375627374726174652d6e6f64650a0000000c010000000000005cdf6acb689907609b0500000037e397fc7c91f5e40200000040fe3ad401f8959a06000000d2bc9897eed08f1503000000be9fb0c91a8046cf01000000f78b278be53f454c02000000ed99c5acb25eedf50300000017a6bc0d0062aeb30100000018ef58a3b67ba77001000000cbca25e39f14238702000000687ad44ad37f03c201000000bc9d89904f5b923f010000008453b50b222939770100000068b66ba122c93fa70200000037c8bb1350a9a2a8040000008a8047a53a8277ec01000000f3ff14d5ab52705903000000899a250cbe84f2500100000049eaaf1b548a0cb00400000091d5df18b0d2cf58020000006fd7c327202e4a8d01000000ab3c0572291feb8b01000000fbc577b9d747efd6010000000200000001", "docs": [" Get the chain's in-code version."]}, {"name": "SS58Prefix", "type": 101, "value": "0x2a00", "docs": [" The designated SS58 prefix of this chain.", "", " This replaces the \"ss58Format\" property declared in the chain spec. Reason is", " that the runtime should know about the prefix in order to make use of it as", " an identifier of the chain."]}], "errors": {"type": 528}, "index": 0}, {"name": "Utility", "storage": null, "calls": {"type": 146}, "events": {"type": 31}, "constants": [{"name": "batched_calls_limit", "type": 4, "value": "0xaa2a0000", "docs": [" The limit on the number of batched calls."]}], "errors": {"type": 529}, "index": 1}, {"name": "<PERSON>", "storage": {"prefix": "<PERSON>", "items": [{"name": "EpochIndex", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 12}, "fallback": "0x***************0", "docs": [" Current epoch index."]}, {"name": "Authorities", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 530}, "fallback": "0x00", "docs": [" Current epoch authorities."]}, {"name": "GenesisSlot", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 159}, "fallback": "0x***************0", "docs": [" The slot at which the first epoch actually started. This is 0", " until the first block of the chain."]}, {"name": "CurrentSlot", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 159}, "fallback": "0x***************0", "docs": [" Current slot number."]}, {"name": "Randomness", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 1}, "fallback": "0x************************************************************0000", "docs": [" The epoch randomness for the *current* epoch.", "", " # Security", "", " This MUST NOT be used for gambling, as it can be influenced by a", " malicious validator in the short term. It MAY be used in many", " cryptographic protocols, however, so long as one remembers that this", " (like everything else on-chain) it is public. For example, it can be", " used where a number is needed that cannot have been chosen by an", " adversary, for purposes such as public-coin zero-knowledge proofs."]}, {"name": "PendingEpochConfigChange", "modifier": "Optional", "type": {"plain": 161}, "fallback": "0x00", "docs": [" Pending epoch configuration change that will be applied when the next epoch is enacted."]}, {"name": "NextRandomness", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 1}, "fallback": "0x************************************************************0000", "docs": [" Next epoch randomness."]}, {"name": "NextAuthorities", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 530}, "fallback": "0x00", "docs": [" Next epoch authorities."]}, {"name": "SegmentIndex", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 4}, "fallback": "0x00000000", "docs": [" Randomness under construction.", "", " We make a trade-off between storage accesses and list length.", " We store the under-construction randomness in segments of up to", " `UNDER_CONSTRUCTION_SEGMENT_LENGTH`.", "", " Once a segment reaches this length, we begin the next one.", " We reset all segments and return to `0` at the beginning of every", " epoch."]}, {"name": "UnderConstruction", "modifier": "<PERSON><PERSON><PERSON>", "type": {"map": {"hashers": ["Twox64Concat"], "key": 4, "value": 533}}, "fallback": "0x00", "docs": [" TWOX-NOTE: `SegmentIndex` is an increasing integer, so this is okay."]}, {"name": "Initialized", "modifier": "Optional", "type": {"plain": 535}, "fallback": "0x00", "docs": [" Temporary value (cleared at block finalization) which is `Some`", " if per-block initialization has already been called for current block."]}, {"name": "Author<PERSON>rf<PERSON><PERSON><PERSON><PERSON>", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 95}, "fallback": "0x00", "docs": [" This field should always be populated during block processing unless", " secondary plain slots are enabled (which don't contain a VRF output).", "", " It is set in `on_finalize`, before it will contain the value from the last block."]}, {"name": "EpochStart", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 94}, "fallback": "0x***************0", "docs": [" The block numbers when the last and current epoch have started, respectively `N-1` and", " `N`.", " NOTE: We track this is in order to annotate the block number when a given pool of", " entropy was fixed (i.e. it was known to chain observers). Since epochs are defined in", " slots, which may be skipped, the block numbers may not line up with the slot numbers."]}, {"name": "Lateness", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 4}, "fallback": "0x00000000", "docs": [" How late the current block is compared to its parent.", "", " This entry is populated as part of block execution and is cleaned up", " on block finalization. Querying this storage entry outside of block", " execution context should always yield zero."]}, {"name": "EpochConfig", "modifier": "Optional", "type": {"plain": 541}, "fallback": "0x00", "docs": [" The configuration for the current epoch. Should never be `None` as it is initialized in", " genesis."]}, {"name": "NextEpochConfig", "modifier": "Optional", "type": {"plain": 541}, "fallback": "0x00", "docs": [" The configuration for the next epoch, `None` if the config will not change", " (you can fallback to `EpochConfig` instead in that case)."]}, {"name": "SkippedEpochs", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 542}, "fallback": "0x00", "docs": [" A list of the last 100 skipped epochs and the corresponding session index", " when the epoch was skipped.", "", " This is only used for validating equivocation proofs. An equivocation proof", " must contains a key-ownership proof for a given session, therefore we need a", " way to tie together sessions and epoch indices, i.e. we need to validate that", " a validator was the owner of a given key on a given session, and what the", " active epoch index was during that session."]}]}, "calls": {"type": 154}, "events": null, "constants": [{"name": "EpochDuration", "type": 12, "value": "0xc800000000000000", "docs": [" The amount of time, in slots, that each epoch should last.", " NOTE: Currently it is not possible to change the epoch duration after", " the chain has started. Attempting to do so will brick block production."]}, {"name": "ExpectedBlockTime", "type": 12, "value": "0xb80b000000000000", "docs": [" The expected average block time at which BABE should be creating", " blocks. Since BABE is probabilistic it is not trivial to figure out", " what the expected average block time should be based on the slot", " duration and the security parameter `c` (where `1 - c` represents", " the probability of a slot being empty)."]}, {"name": "MaxAuthorities", "type": 4, "value": "0x64000000", "docs": [" Max number of authorities allowed"]}, {"name": "MaxNominators", "type": 4, "value": "0x40000000", "docs": [" The maximum number of nominators for each validator."]}], "errors": {"type": 545}, "index": 2}, {"name": "Timestamp", "storage": {"prefix": "Timestamp", "items": [{"name": "Now", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 12}, "fallback": "0x***************0", "docs": [" The current time for the current block."]}, {"name": "DidUpdate", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 8}, "fallback": "0x00", "docs": [" Whether the timestamp has been updated in this block.", "", " This value is updated to `true` upon successful submission of a timestamp by a node.", " It is then checked at the end of each block execution in the `on_finalize` hook."]}]}, "calls": {"type": 164}, "events": null, "constants": [{"name": "MinimumPeriod", "type": 12, "value": "0xdc05000000000000", "docs": [" The minimum period between blocks.", "", " Be aware that this is different to the *expected* period that the block production", " apparatus provides. Your chosen consensus system will generally work with this to", " determine a sensible block time. For example, in the Aura pallet it will be double this", " period on default settings."]}], "errors": null, "index": 3}, {"name": "Authorship", "storage": {"prefix": "Authorship", "items": [{"name": "Author", "modifier": "Optional", "type": {"plain": 0}, "fallback": "0x00", "docs": [" Author of current block."]}]}, "calls": null, "events": null, "constants": [], "errors": null, "index": 4}, {"name": "Indices", "storage": {"prefix": "Indices", "items": [{"name": "Accounts", "modifier": "Optional", "type": {"map": {"hashers": ["Blake2_128Concat"], "key": 4, "value": 546}}, "fallback": "0x00", "docs": [" The lookup from index to account."]}]}, "calls": {"type": 165}, "events": {"type": 34}, "constants": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "type": 6, "value": "0x00407a10f35a***************00000", "docs": [" The deposit needed for reserving an index."]}], "errors": {"type": 547}, "index": 5}, {"name": "Balances", "storage": {"prefix": "Balances", "items": [{"name": "TotalIssuance", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 6}, "fallback": "0x******************************00", "docs": [" The total units issued in the system."]}, {"name": "InactiveIssuance", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 6}, "fallback": "0x******************************00", "docs": [" The total units of outstanding deactivated balance in the system."]}, {"name": "Account", "modifier": "<PERSON><PERSON><PERSON>", "type": {"map": {"hashers": ["Blake2_128Concat"], "key": 0, "value": 5}}, "fallback": "0x************************************************************************************************************************00000080", "docs": [" The Balances pallet example of storing the balance of an account.", "", " # Example", "", " ```nocompile", "  impl pallet_balances::Config for Runtime {", "    type AccountStore = StorageMapShim<Self::Account<Runtime>, frame_system::Provider<Runtime>, AccountId, Self::AccountData<Balance>>", "  }", " ```", "", " You can also store the balance of an account in the `System` pallet.", "", " # Example", "", " ```nocompile", "  impl pallet_balances::Config for Runtime {", "   type AccountStore = System", "  }", " ```", "", " But this comes with tradeoffs, storing account balances in the system pallet stores", " `frame_system` data alongside the account data contrary to storing account balances in the", " `Balances` pallet, which uses a `StorageMap` to store balances data only.", " NOTE: This is only used in the case that this pallet is used to store balances."]}, {"name": "Locks", "modifier": "<PERSON><PERSON><PERSON>", "type": {"map": {"hashers": ["Blake2_128Concat"], "key": 0, "value": 548}}, "fallback": "0x00", "docs": [" Any liquidity locks on some account balances.", " NOTE: Should only be accessed when setting, changing and freeing a lock.", "", " Use of locks is deprecated in favour of freezes. See `https://github.com/paritytech/substrate/pull/12951/`"]}, {"name": "Reserves", "modifier": "<PERSON><PERSON><PERSON>", "type": {"map": {"hashers": ["Blake2_128Concat"], "key": 0, "value": 552}}, "fallback": "0x00", "docs": [" Named reserves on some account balances.", "", " Use of reserves is deprecated in favour of holds. See `https://github.com/paritytech/substrate/pull/12951/`"]}, {"name": "Holds", "modifier": "<PERSON><PERSON><PERSON>", "type": {"map": {"hashers": ["Blake2_128Concat"], "key": 0, "value": 555}}, "fallback": "0x00", "docs": [" Holds on account balances."]}, {"name": "Freezes", "modifier": "<PERSON><PERSON><PERSON>", "type": {"map": {"hashers": ["Blake2_128Concat"], "key": 0, "value": 566}}, "fallback": "0x00", "docs": [" Freeze locks on account balances."]}]}, "calls": {"type": 168}, "events": {"type": 35}, "constants": [{"name": "ExistentialDeposit", "type": 6, "value": "0x00407a10f35a***************00000", "docs": [" The minimum amount required to keep an account open. MUST BE GREATER THAN ZERO!", "", " If you *really* need it to be zero, you can enable the feature `insecure_zero_ed` for", " this pallet. However, you do so at your own risk: this will open up a major DoS vector.", " In case you have multiple sources of provider references, you may also get unexpected", " behaviour if you set this to zero.", "", " Bottom line: Do yourself a favour and make it at least one!"]}, {"name": "MaxLocks", "type": 4, "value": "0x32000000", "docs": [" The maximum number of locks that should exist on an account.", " Not strictly enforced, but used for weight estimation.", "", " Use of locks is deprecated in favour of freezes. See `https://github.com/paritytech/substrate/pull/12951/`"]}, {"name": "MaxReserves", "type": 4, "value": "0x32000000", "docs": [" The maximum number of named reserves that can exist on an account.", "", " Use of reserves is deprecated in favour of holds. See `https://github.com/paritytech/substrate/pull/12951/`"]}, {"name": "MaxFreezes", "type": 4, "value": "0x01000000", "docs": [" The maximum number of individual freeze locks that can exist on an account at any time."]}], "errors": {"type": 571}, "index": 6}, {"name": "TransactionPayment", "storage": {"prefix": "TransactionPayment", "items": [{"name": "NextFeeMultiplier", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 71}, "fallback": "0x000064a7b3b6e00d***************0", "docs": []}, {"name": "StorageVersion", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 572}, "fallback": "0x00", "docs": []}]}, "calls": null, "events": {"type": 37}, "constants": [{"name": "OperationalFeeMultiplier", "type": 2, "value": "0x05", "docs": [" A fee multiplier for `Operational` extrinsics to compute \"virtual tip\" to boost their", " `priority`", "", " This value is multiplied by the `final_fee` to obtain a \"virtual tip\" that is later", " added to a tip component in regular `priority` calculations.", " It means that a `Normal` transaction can front-run a similarly-sized `Operational`", " extrinsic (with no tip), by including a tip value greater than the virtual tip.", "", " ```rust,ignore", " // For `Normal`", " let priority = priority_calc(tip);", "", " // For `Operational`", " let virtual_tip = (inclusion_fee + tip) * OperationalFeeMultiplier;", " let priority = priority_calc(tip + virtual_tip);", " ```", "", " Note that since we use `final_fee` the multiplier applies also to the regular `tip`", " sent with the transaction. So, not only does the transaction get a priority bump based", " on the `inclusion_fee`, but we also amplify the impact of tips applied to `Operational`", " transactions."]}], "errors": null, "index": 7}, {"name": "AssetTxPayment", "storage": null, "calls": null, "events": {"type": 38}, "constants": [], "errors": null, "index": 8}, {"name": "AssetConversionTxPayment", "storage": null, "calls": null, "events": {"type": 40}, "constants": [], "errors": null, "index": 9}, {"name": "ElectionProviderMultiPhase", "storage": {"prefix": "ElectionProviderMultiPhase", "items": [{"name": "Round", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 4}, "fallback": "0x01000000", "docs": [" Internal counter for the number of rounds.", "", " This is useful for de-duplication of transactions submitted to the pool, and general", " diagnostics of the pallet.", "", " This is merely incremented once per every time that an upstream `elect` is called."]}, {"name": "CurrentPhase", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 45}, "fallback": "0x00", "docs": [" Current phase."]}, {"name": "QueuedSolution", "modifier": "Optional", "type": {"plain": 573}, "fallback": "0x00", "docs": [" Current best solution, signed or unsigned, queued to be returned upon `elect`.", "", " Always sorted by score."]}, {"name": "Snapshot", "modifier": "Optional", "type": {"plain": 575}, "fallback": "0x00", "docs": [" Snapshot data of the round.", "", " This is created at the beginning of the signed phase and cleared upon calling `elect`.", " Note: This storage type must only be mutated through [`SnapshotWrapper`]."]}, {"name": "DesiredTargets", "modifier": "Optional", "type": {"plain": 4}, "fallback": "0x00", "docs": [" Desired number of targets to elect for this round.", "", " Only exists when [`<PERSON><PERSON><PERSON>`] is present.", " Note: This storage type must only be mutated through [`SnapshotWrapper`]."]}, {"name": "SnapshotMetadata", "modifier": "Optional", "type": {"plain": 223}, "fallback": "0x00", "docs": [" The metadata of the [`RoundSnapshot`]", "", " Only exists when [`<PERSON><PERSON><PERSON>`] is present.", " Note: This storage type must only be mutated through [`SnapshotWrapper`]."]}, {"name": "SignedSubmissionNextIndex", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 4}, "fallback": "0x00000000", "docs": [" The next index to be assigned to an incoming signed submission.", "", " Every accepted submission is assigned a unique index; that index is bound to that particular", " submission for the duration of the election. On election finalization, the next index is", " reset to 0.", "", " We can't just use `SignedSubmissionIndices.len()`, because that's a bounded set; past its", " capacity, it will simply saturate. We can't just iterate over `SignedSubmissionsMap`,", " because iteration is slow. Instead, we store the value here."]}, {"name": "SignedSubmissionIndices", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 579}, "fallback": "0x00", "docs": [" A sorted, bounded vector of `(score, block_number, index)`, where each `index` points to a", " value in `SignedSubmissions`.", "", " We never need to process more than a single signed submission at a time. Signed submissions", " can be quite large, so we're willing to pay the cost of multiple database accesses to access", " them one at a time instead of reading and decoding all of them at once."]}, {"name": "SignedSubmissionsMap", "modifier": "Optional", "type": {"map": {"hashers": ["Twox64Concat"], "key": 4, "value": 582}}, "fallback": "0x00", "docs": [" Unchecked, signed solutions.", "", " Together with `SubmissionIndices`, this stores a bounded set of `SignedSubmissions` while", " allowing us to keep only a single one in memory at a time.", "", " Twox note: the key of the map is an auto-incrementing index which users cannot inspect or", " affect; we shouldn't need a cryptographically secure hasher."]}, {"name": "MinimumUntrustedScore", "modifier": "Optional", "type": {"plain": 44}, "fallback": "0x00", "docs": [" The minimum score that each 'untrusted' solution must attain in order to be considered", " feasible.", "", " Can be set via `set_minimum_untrusted_score`."]}]}, "calls": {"type": 170}, "events": {"type": 41}, "constants": [{"name": "BetterSignedThreshold", "type": 49, "value": "0x00000000", "docs": [" The minimum amount of improvement to the solution score that defines a solution as", " \"better\" in the Signed phase."]}, {"name": "OffchainRepeat", "type": 4, "value": "0x05000000", "docs": [" The repeat threshold of the offchain worker.", "", " For example, if it is 5, that means that at least 5 blocks will elapse between attempts", " to submit the worker's solution."]}, {"name": "MinerTxPriority", "type": 12, "value": "0xfeffffffffffff7f", "docs": [" The priority of the unsigned transaction submitted in the unsigned-phase"]}, {"name": "SignedMaxSubmissions", "type": 4, "value": "0x0a000000", "docs": [" Maximum number of signed submissions that can be queued.", "", " It is best to avoid adjusting this during an election, as it impacts downstream data", " structures. In particular, `SignedSubmissionIndices<T>` is bounded on this value. If you", " update this value during an election, you _must_ ensure that", " `SignedSubmissionIndices.len()` is less than or equal to the new value. Otherwise,", " attempts to submit new solutions may cause a runtime panic."]}, {"name": "SignedMaxWeight", "type": 10, "value": "0x0bd8e2a18c2e011366666666666666a6", "docs": [" Maximum weight of a signed solution.", "", " If [`Config::MinerConfig`] is being implemented to submit signed solutions (outside of", " this pallet), then [`MinerConfig::solution_weight`] is used to compare against", " this value."]}, {"name": "SignedMaxRefunds", "type": 4, "value": "0x03000000", "docs": [" The maximum amount of unchecked solutions to refund the call fee for."]}, {"name": "SignedRewardBase", "type": 6, "value": "0x00407a10f35a***************00000", "docs": [" Base reward for a signed solution"]}, {"name": "SignedDepositByte", "type": 6, "value": "0x0010a5d4e8***************0000000", "docs": [" Per-byte deposit for a signed solution."]}, {"name": "SignedDepositWeight", "type": 6, "value": "0x******************************00", "docs": [" Per-weight deposit for a signed solution."]}, {"name": "MaxWinners", "type": 4, "value": "0xe8030000", "docs": [" The maximum number of winners that can be elected by this `ElectionProvider`", " implementation.", "", " Note: This must always be greater or equal to `T::DataProvider::desired_targets()`."]}, {"name": "MinerMax<PERSON><PERSON><PERSON>", "type": 4, "value": "0x00003600", "docs": []}, {"name": "MinerMaxWeight", "type": 10, "value": "0x0bd8e2a18c2e011366666666666666a6", "docs": []}, {"name": "MinerMaxVotesPerVoter", "type": 4, "value": "0x10000000", "docs": []}, {"name": "MinerMaxWinners", "type": 4, "value": "0xe8030000", "docs": []}], "errors": {"type": 583}, "index": 10}, {"name": "Staking", "storage": {"prefix": "Staking", "items": [{"name": "ValidatorCount", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 4}, "fallback": "0x00000000", "docs": [" The ideal number of active validators."]}, {"name": "MinimumValidatorCount", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 4}, "fallback": "0x00000000", "docs": [" Minimum number of staking participants before emergency conditions are imposed."]}, {"name": "Invulnerables", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 89}, "fallback": "0x00", "docs": [" Any validators that may never be slashed or forcibly kicked. It's a Vec since they're", " easy to initialize and the performance hit is minimal (we expect no more than four", " invulnerables) and restricted to testnets."]}, {"name": "Bonded", "modifier": "Optional", "type": {"map": {"hashers": ["Twox64Concat"], "key": 0, "value": 0}}, "fallback": "0x00", "docs": [" Map from all locked \"stash\" accounts to the controller account.", "", " TWOX-NOTE: SAFE since `AccountId` is a secure hash."]}, {"name": "MinNominatorBond", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 6}, "fallback": "0x******************************00", "docs": [" The minimum active bond to become and maintain the role of a nominator."]}, {"name": "MinValidatorBond", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 6}, "fallback": "0x******************************00", "docs": [" The minimum active bond to become and maintain the role of a validator."]}, {"name": "MinimumActiveStake", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 6}, "fallback": "0x******************************00", "docs": [" The minimum active nominator stake of the last successful election."]}, {"name": "MinCommission", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 49}, "fallback": "0x00000000", "docs": [" The minimum amount of commission that validators can set.", "", " If set to `0`, no limit exists."]}, {"name": "Ledger", "modifier": "Optional", "type": {"map": {"hashers": ["Blake2_128Concat"], "key": 0, "value": 584}}, "fallback": "0x00", "docs": [" Map from all (unlocked) \"controller\" accounts to the info regarding the staking.", "", " Note: All the reads and mutations to this storage *MUST* be done through the methods exposed", " by [`StakingLedger`] to ensure data and lock consistency."]}, {"name": "Payee", "modifier": "Optional", "type": {"map": {"hashers": ["Twox64Concat"], "key": 0, "value": 48}}, "fallback": "0x00", "docs": [" Where the reward payment should be made. Keyed by stash.", "", " TWOX-NOTE: SAFE since `AccountId` is a secure hash."]}, {"name": "Validators", "modifier": "<PERSON><PERSON><PERSON>", "type": {"map": {"hashers": ["Twox64Concat"], "key": 0, "value": 50}}, "fallback": "0x0000", "docs": [" The map from (wannabe) validator stash key to the preferences of that validator.", "", " TWOX-NOTE: SAFE since `AccountId` is a secure hash."]}, {"name": "CounterForValidators", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 4}, "fallback": "0x00000000", "docs": ["Counter for the related counted storage map"]}, {"name": "MaxValidatorsCount", "modifier": "Optional", "type": {"plain": 4}, "fallback": "0x00", "docs": [" The maximum validator count before we stop allowing new validators to join.", "", " When this value is not set, no limits are enforced."]}, {"name": "Nominators", "modifier": "Optional", "type": {"map": {"hashers": ["Twox64Concat"], "key": 0, "value": 586}}, "fallback": "0x00", "docs": [" The map from nominator stash key to their nomination preferences, namely the validators that", " they wish to support.", "", " Note that the keys of this storage map might become non-decodable in case the", " account's [`NominationsQuota::MaxNominations`] configuration is decreased.", " In this rare case, these nominators", " are still existent in storage, their key is correct and retrievable (i.e. `contains_key`", " indicates that they exist), but their value cannot be decoded. Therefore, the non-decodable", " nominators will effectively not-exist, until they re-submit their preferences such that it", " is within the bounds of the newly set `Config::MaxNominations`.", "", " This implies that `::iter_keys().count()` and `::iter().count()` might return different", " values for this map. Moreover, the main `::count()` is aligned with the former, namely the", " number of keys that exist.", "", " Lastly, if any of the nominators become non-decodable, they can be chilled immediately via", " [`Call::chill_other`] dispatchable by anyone.", "", " TWOX-NOTE: SAFE since `AccountId` is a secure hash."]}, {"name": "CounterForNominators", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 4}, "fallback": "0x00000000", "docs": ["Counter for the related counted storage map"]}, {"name": "VirtualStakers", "modifier": "Optional", "type": {"map": {"hashers": ["Twox64Concat"], "key": 0, "value": 33}}, "fallback": "0x00", "docs": [" Stakers whose funds are managed by other pallets.", "", " This pallet does not apply any locks on them, therefore they are only virtually bonded. They", " are expected to be keyless accounts and hence should not be allowed to mutate their ledger", " directly via this pallet. Instead, these accounts are managed by other pallets and accessed", " via low level apis. We keep track of them to do minimal integrity checks."]}, {"name": "CounterForVirtualStakers", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 4}, "fallback": "0x00000000", "docs": ["Counter for the related counted storage map"]}, {"name": "MaxNominatorsCount", "modifier": "Optional", "type": {"plain": 4}, "fallback": "0x00", "docs": [" The maximum nominator count before we stop allowing new validators to join.", "", " When this value is not set, no limits are enforced."]}, {"name": "CurrentEra", "modifier": "Optional", "type": {"plain": 4}, "fallback": "0x00", "docs": [" The current era index.", "", " This is the latest planned era, depending on how the Session pallet queues the validator", " set, it might be active or not."]}, {"name": "ActiveEra", "modifier": "Optional", "type": {"plain": 587}, "fallback": "0x00", "docs": [" The active era information, it holds index and start.", "", " The active era is the era being currently rewarded. Validator set of this era must be", " equal to [`SessionInterface::validators`]."]}, {"name": "ErasStartSessionIndex", "modifier": "Optional", "type": {"map": {"hashers": ["Twox64Concat"], "key": 4, "value": 4}}, "fallback": "0x00", "docs": [" The session index at which the era start for the last [`Config::HistoryDepth`] eras.", "", " Note: This tracks the starting session (i.e. session index when era start being active)", " for the eras in `[CurrentEra - HISTORY_DEPTH, CurrentEra]`."]}, {"name": "ErasStakers", "modifier": "<PERSON><PERSON><PERSON>", "type": {"map": {"hashers": ["Twox64Concat", "Twox64Concat"], "key": 589, "value": 80}}, "fallback": "0x000000", "docs": [" Exposure of validator at era.", "", " This is keyed first by the era index to allow bulk deletion and then the stash account.", "", " Is it removed after [`Config::HistoryDepth`] eras.", " If stakers hasn't been set or has been removed then empty exposure is returned.", "", " Note: Deprecated since v14. Use `EraInfo` instead to work with exposures."]}, {"name": "ErasStakersOverview", "modifier": "Optional", "type": {"map": {"hashers": ["Twox64Concat", "Twox64Concat"], "key": 589, "value": 590}}, "fallback": "0x00", "docs": [" Summary of validator exposure at a given era.", "", " This contains the total stake in support of the validator and their own stake. In addition,", " it can also be used to get the number of nominators backing this validator and the number of", " exposure pages they are divided into. The page count is useful to determine the number of", " pages of rewards that needs to be claimed.", "", " This is keyed first by the era index to allow bulk deletion and then the stash account.", " Should only be accessed through `EraInfo`.", "", " Is it removed after [`Config::HistoryDepth`] eras.", " If stakers hasn't been set or has been removed then empty overview is returned."]}, {"name": "ErasStakersClipped", "modifier": "<PERSON><PERSON><PERSON>", "type": {"map": {"hashers": ["Twox64Concat", "Twox64Concat"], "key": 589, "value": 80}}, "fallback": "0x000000", "docs": [" Clipped Exposure of validator at era.", "", " Note: This is deprecated, should be used as read-only and will be removed in the future.", " New `Exposure`s are stored in a paged manner in `ErasStakersPaged` instead.", "", " This is similar to [`ErasStakers`] but number of nominators exposed is reduced to the", " `T::MaxExposurePageSize` biggest stakers.", " (Note: the field `total` and `own` of the exposure remains unchanged).", " This is used to limit the i/o cost for the nominator payout.", "", " This is keyed fist by the era index to allow bulk deletion and then the stash account.", "", " It is removed after [`Config::HistoryDepth`] eras.", " If stakers hasn't been set or has been removed then empty exposure is returned.", "", " Note: Deprecated since v14. Use `EraInfo` instead to work with exposures."]}, {"name": "ErasStakersPaged", "modifier": "Optional", "type": {"map": {"hashers": ["Twox64Concat", "Twox64Concat", "Twox64Concat"], "key": 591, "value": 592}}, "fallback": "0x00", "docs": [" Paginated exposure of a validator at given era.", "", " This is keyed first by the era index to allow bulk deletion, then stash account and finally", " the page. Should only be accessed through `EraInfo`.", "", " This is cleared after [`Config::HistoryDepth`] eras."]}, {"name": "ClaimedRewards", "modifier": "<PERSON><PERSON><PERSON>", "type": {"map": {"hashers": ["Twox64Concat", "Twox64Concat"], "key": 589, "value": 114}}, "fallback": "0x00", "docs": [" History of claimed paged rewards by era and validator.", "", " This is keyed by era and validator stash which maps to the set of page indexes which have", " been claimed.", "", " It is removed after [`Config::HistoryDepth`] eras."]}, {"name": "ErasValidatorPrefs", "modifier": "<PERSON><PERSON><PERSON>", "type": {"map": {"hashers": ["Twox64Concat", "Twox64Concat"], "key": 589, "value": 50}}, "fallback": "0x0000", "docs": [" Similar to `ErasStakers`, this holds the preferences of validators.", "", " This is keyed first by the era index to allow bulk deletion and then the stash account.", "", " Is it removed after [`Config::HistoryDepth`] eras."]}, {"name": "ErasValidatorReward", "modifier": "Optional", "type": {"map": {"hashers": ["Twox64Concat"], "key": 4, "value": 6}}, "fallback": "0x00", "docs": [" The total validator era payout for the last [`Config::HistoryDepth`] eras.", "", " Eras that haven't finished yet or has been removed doesn't have reward."]}, {"name": "ErasRewardPoints", "modifier": "<PERSON><PERSON><PERSON>", "type": {"map": {"hashers": ["Twox64Concat"], "key": 4, "value": 593}}, "fallback": "0x0000000000", "docs": [" Rewards for the last [`Config::HistoryDepth`] eras.", " If reward hasn't been set or has been removed then 0 reward is returned."]}, {"name": "ErasTotalStake", "modifier": "<PERSON><PERSON><PERSON>", "type": {"map": {"hashers": ["Twox64Concat"], "key": 4, "value": 6}}, "fallback": "0x******************************00", "docs": [" The total amount staked for the last [`Config::HistoryDepth`] eras.", " If total hasn't been set or has been removed then 0 stake is returned."]}, {"name": "ForceEra", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 52}, "fallback": "0x00", "docs": [" Mode of era forcing."]}, {"name": "MaxStakedRewards", "modifier": "Optional", "type": {"plain": 230}, "fallback": "0x00", "docs": [" Maximum staked rewards, i.e. the percentage of the era inflation that", " is used for stake rewards.", " See [Era payout](./index.html#era-payout)."]}, {"name": "SlashRewardFraction", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 49}, "fallback": "0x00000000", "docs": [" The percentage of the slash that is distributed to reporters.", "", " The rest of the slashed value is handled by the `Slash`."]}, {"name": "CanceledSlashPayout", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 6}, "fallback": "0x******************************00", "docs": [" The amount of currency given to reporters of a slash event which was", " canceled by extraordinary circumstances (e.g. governance)."]}, {"name": "UnappliedSlashes", "modifier": "<PERSON><PERSON><PERSON>", "type": {"map": {"hashers": ["Twox64Concat"], "key": 4, "value": 597}}, "fallback": "0x00", "docs": [" All unapplied slashes that are queued for later."]}, {"name": "BondedEras", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 514}, "fallback": "0x00", "docs": [" A mapping from still-bonded eras to the first session index of that era.", "", " Must contains information for eras for the range:", " `[active_era - bounding_duration; active_era]`"]}, {"name": "ValidatorSlashInEra", "modifier": "Optional", "type": {"map": {"hashers": ["Twox64Concat", "Twox64Concat"], "key": 589, "value": 599}}, "fallback": "0x00", "docs": [" All slashing events on validators, mapped by era to the highest slash proportion", " and slash value of the era."]}, {"name": "NominatorSlashInEra", "modifier": "Optional", "type": {"map": {"hashers": ["Twox64Concat", "Twox64Concat"], "key": 589, "value": 6}}, "fallback": "0x00", "docs": [" All slashing events on nominators, mapped by era to the highest slash value of the era."]}, {"name": "SlashingSpans", "modifier": "Optional", "type": {"map": {"hashers": ["Twox64Concat"], "key": 0, "value": 600}}, "fallback": "0x00", "docs": [" Slashing spans for stash accounts."]}, {"name": "SpanSlash", "modifier": "<PERSON><PERSON><PERSON>", "type": {"map": {"hashers": ["Twox64Concat"], "key": 596, "value": 601}}, "fallback": "0x************************************************************0000", "docs": [" Records information about the maximum slash of a stash within a slashing span,", " as well as how much reward has been paid out."]}, {"name": "CurrentPlannedSession", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 4}, "fallback": "0x00000000", "docs": [" The last planned session scheduled by the session pallet.", "", " This is basically in sync with the call to [`pallet_session::SessionManager::new_session`]."]}, {"name": "DisabledValidators", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 114}, "fallback": "0x00", "docs": [" Indices of validators that have offended in the active era. The offenders are disabled for a", " whole era. For this reason they are kept here - only staking pallet knows about eras. The", " implementor of [`DisablingStrategy`] defines if a validator should be disabled which", " implicitly means that the implementor also controls the max number of disabled validators.", "", " The vec is always kept sorted so that we can find whether a given validator has previously", " offended using binary search."]}, {"name": "ChillThreshold", "modifier": "Optional", "type": {"plain": 230}, "fallback": "0x00", "docs": [" The threshold for when users can start calling `chill_other` for other validators /", " nominators. The threshold is compared to the actual number of validators / nominators", " (`CountFor*`) in the system compared to the configured max (`Max*Count`)."]}]}, "calls": {"type": 228}, "events": {"type": 47}, "constants": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "type": 4, "value": "0x54000000", "docs": [" Number of eras to keep in history.", "", " Following information is kept for eras in `[current_era -", " HistoryDepth, current_era]`: `ErasStakers`, `ErasStakersClipped`,", " `ErasValidatorPrefs`, `ErasValidatorReward`, `ErasRewardPoints`,", " `ErasTotalStake`, `ErasStartSessionIndex`, `ClaimedRewards`, `ErasStakersPaged`,", " `ErasStakersOverview`.", "", " Must be more than the number of eras delayed by session.", " I.e. active era must always be in history. I.e. `active_era >", " current_era - history_depth` must be guaranteed.", "", " If migrating an existing pallet from storage value to config value,", " this should be set to same value or greater as in storage.", "", " Note: `<PERSON><PERSON><PERSON>h` is used as the upper bound for the `BoundedVec`", " item `StakingLedger.legacy_claimed_rewards`. Setting this value lower than", " the existing value can lead to inconsistencies in the", " `StakingLedger` and will need to be handled properly in a migration.", " The test `reducing_history_depth_abrupt` shows this effect."]}, {"name": "SessionsPerEra", "type": 4, "value": "0x06000000", "docs": [" Number of sessions per era."]}, {"name": "BondingDuration", "type": 4, "value": "0xa0020000", "docs": [" Number of eras that staked funds must remain bonded for."]}, {"name": "SlashDeferDuration", "type": 4, "value": "0xa8000000", "docs": [" Number of eras that slashes are deferred by, after computation.", "", " This should be less than the bonding duration. Set to 0 if slashes", " should be applied immediately, without opportunity for intervention."]}, {"name": "MaxExposurePageSize", "type": 4, "value": "0x00010000", "docs": [" The maximum size of each `T::ExposurePage`.", "", " An `ExposurePage` is weakly bounded to a maximum of `MaxExposurePageSize`", " nominators.", "", " For older non-paged exposure, a reward payout was restricted to the top", " `MaxExposurePageSize` nominators. This is to limit the i/o cost for the", " nominator payout.", "", " Note: `MaxExposurePageSize` is used to bound `ClaimedRewards` and is unsafe to reduce", " without handling it in a migration."]}, {"name": "MaxUnlockingChunks", "type": 4, "value": "0x20000000", "docs": [" The maximum number of `unlocking` chunks a [`StakingLedger`] can", " have. Effectively determines how many unique eras a staker may be", " unbonding in.", "", " Note: `MaxUnlockingChunks` is used as the upper bound for the", " `BoundedVec` item `StakingLedger.unlocking`. Setting this value", " lower than the existing value can lead to inconsistencies in the", " `StakingLedger` and will need to be handled properly in a runtime", " migration. The test `reducing_max_unlocking_chunks_abrupt` shows", " this effect."]}], "errors": {"type": 602}, "index": 11}, {"name": "Session", "storage": {"prefix": "Session", "items": [{"name": "Validators", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 89}, "fallback": "0x00", "docs": [" The current set of validators."]}, {"name": "CurrentIndex", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 4}, "fallback": "0x00000000", "docs": [" Current index of the session."]}, {"name": "<PERSON>ued<PERSON><PERSON>ed", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 8}, "fallback": "0x00", "docs": [" True if the underlying economic identities or weighting behind the validators", " has changed in the queued validator set."]}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 603}, "fallback": "0x00", "docs": [" The queued keys for the next session. When the next session begins, these keys", " will be used to determine the validator's session keys."]}, {"name": "DisabledValidators", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 114}, "fallback": "0x00", "docs": [" Indices of disabled validators.", "", " The vec is always kept sorted so that we can find whether a given validator is", " disabled using binary search. It gets cleared when `on_session_ending` returns", " a new set of identities."]}, {"name": "NextKeys", "modifier": "Optional", "type": {"map": {"hashers": ["Twox64Concat"], "key": 0, "value": 242}}, "fallback": "0x00", "docs": [" The next session keys for a validator."]}, {"name": "KeyOwn<PERSON>", "modifier": "Optional", "type": {"map": {"hashers": ["Twox64Concat"], "key": 605, "value": 0}}, "fallback": "0x00", "docs": [" The owner of a key. The key is the `KeyTypeId` + the encoded key."]}]}, "calls": {"type": 241}, "events": {"type": 53}, "constants": [], "errors": {"type": 607}, "index": 12}, {"name": "Democracy", "storage": {"prefix": "Democracy", "items": [{"name": "PublicPropCount", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 4}, "fallback": "0x00000000", "docs": [" The number of (public) proposals that have been made so far."]}, {"name": "PublicProps", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 608}, "fallback": "0x00", "docs": [" The public proposals. Unsorted. The second item is the proposal."]}, {"name": "DepositOf", "modifier": "Optional", "type": {"map": {"hashers": ["Twox64Concat"], "key": 4, "value": 611}}, "fallback": "0x00", "docs": [" Those who have locked a deposit.", "", " TWOX-NOTE: Safe, as increasing integer keys are safe."]}, {"name": "ReferendumCount", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 4}, "fallback": "0x00000000", "docs": [" The next free referendum index, aka the number of referenda started so far."]}, {"name": "LowestUnbaked", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 4}, "fallback": "0x00000000", "docs": [" The lowest referendum index representing an unbaked referendum. Equal to", " `ReferendumCount` if there isn't a unbaked referendum."]}, {"name": "ReferendumInfoOf", "modifier": "Optional", "type": {"map": {"hashers": ["Twox64Concat"], "key": 4, "value": 613}}, "fallback": "0x00", "docs": [" Information concerning any given referendum.", "", " TWOX-NOTE: SAFE as indexes are not under an attacker’s control."]}, {"name": "VotingOf", "modifier": "<PERSON><PERSON><PERSON>", "type": {"map": {"hashers": ["Twox64Concat"], "key": 0, "value": 616}}, "fallback": "0x*********************************************************************************************************000", "docs": [" All votes for a particular voter. We store the balance for the number of votes that we", " have recorded. The second item is the total amount of delegations, that will be added.", "", " TWOX-NOTE: SAFE as `AccountId`s are crypto hashes anyway."]}, {"name": "LastTabledWasExternal", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 8}, "fallback": "0x00", "docs": [" True if the last referendum tabled was submitted externally. False if it was a public", " proposal."]}, {"name": "NextExternal", "modifier": "Optional", "type": {"plain": 622}, "fallback": "0x00", "docs": [" The referendum to be tabled whenever it would be valid to table an external proposal.", " This happens when a referendum needs to be tabled and one of two conditions are met:", " - `LastTabledWasExternal` is `false`; or", " - `PublicProps` is empty."]}, {"name": "Blacklist", "modifier": "Optional", "type": {"map": {"hashers": ["Identity"], "key": 13, "value": 623}}, "fallback": "0x00", "docs": [" A record of who vetoed what. Maps proposal hash to a possible existent block number", " (until when it may not be resubmitted) and who vetoed it."]}, {"name": "Cancellations", "modifier": "<PERSON><PERSON><PERSON>", "type": {"map": {"hashers": ["Identity"], "key": 13, "value": 8}}, "fallback": "0x00", "docs": [" Record of all proposals that have been subject to emergency cancellation."]}, {"name": "MetadataOf", "modifier": "Optional", "type": {"map": {"hashers": ["Blake2_128Concat"], "key": 58, "value": 13}}, "fallback": "0x00", "docs": [" General information concerning any proposal or referendum.", " The `Hash` refers to the preimage of the `Preimages` provider which can be a JSON", " dump or IPFS hash of a JSON file.", "", " Consider a garbage collection for a metadata of finished referendums to `unrequest` (remove)", " large preimages."]}]}, "calls": {"type": 247}, "events": {"type": 54}, "constants": [{"name": "EnactmentPeriod", "type": 4, "value": "0x002f0d00", "docs": [" The period between a proposal being approved and enacted.", "", " It should generally be a little more than the unstake period to ensure that", " voting stakers have an opportunity to remove themselves from the system in the case", " where they are on the losing side of a vote."]}, {"name": "LaunchPeriod", "type": 4, "value": "0x004e0c00", "docs": [" How often (in blocks) new public referenda are launched."]}, {"name": "VotingPeriod", "type": 4, "value": "0x004e0c00", "docs": [" How often (in blocks) to check for new votes."]}, {"name": "VoteLockingPeriod", "type": 4, "value": "0x002f0d00", "docs": [" The minimum period of vote locking.", "", " It should be no shorter than enactment period to ensure that in the case of an approval,", " those successful voters are locked into the consequences that their votes entail."]}, {"name": "MinimumDeposit", "type": 6, "value": "0x0000c16ff28623***************000", "docs": [" The minimum amount to be used as a deposit for a public referendum proposal."]}, {"name": "InstantAllowed", "type": 8, "value": "0x01", "docs": [" Indicator for whether an emergency origin is even allowed to happen. Some chains may", " want to set this permanently to `false`, others may want to condition it on things such", " as an upgrade having happened recently."]}, {"name": "FastTrackVotingPeriod", "type": 4, "value": "0x80510100", "docs": [" Minimum voting period allowed for a fast-track referendum."]}, {"name": "CooloffPeriod", "type": 4, "value": "0x004e0c00", "docs": [" Period in blocks where an external proposal may not be re-submitted after being vetoed."]}, {"name": "MaxVotes", "type": 4, "value": "0x64000000", "docs": [" The maximum number of votes for an account.", "", " Also used to compute weight, an overly big value can", " lead to extrinsic with very big weight: see `delegate` for instance."]}, {"name": "MaxProposals", "type": 4, "value": "0x64000000", "docs": [" The maximum number of public proposals that can exist at any time."]}, {"name": "MaxDeposits", "type": 4, "value": "0x64000000", "docs": [" The maximum number of deposits a public proposal may have at any time."]}, {"name": "MaxBlacklisted", "type": 4, "value": "0x64000000", "docs": [" The maximum number of items which can be blacklisted."]}], "errors": {"type": 624}, "index": 13}, {"name": "Council", "storage": {"prefix": "Council", "items": [{"name": "Proposals", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 625}, "fallback": "0x00", "docs": [" The hashes of the active proposals."]}, {"name": "ProposalOf", "modifier": "Optional", "type": {"map": {"hashers": ["Identity"], "key": 13, "value": 141}}, "fallback": "0x00", "docs": [" Actual proposal for a given hash, if it's current."]}, {"name": "Voting", "modifier": "Optional", "type": {"map": {"hashers": ["Identity"], "key": 13, "value": 626}}, "fallback": "0x00", "docs": [" Votes on a given proposal, if it is ongoing."]}, {"name": "ProposalCount", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 4}, "fallback": "0x00000000", "docs": [" Proposals so far."]}, {"name": "Members", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 89}, "fallback": "0x00", "docs": [" The current members of the collective. This is stored sorted (just by value)."]}, {"name": "Prime", "modifier": "Optional", "type": {"plain": 0}, "fallback": "0x00", "docs": [" The prime member that helps determine the default vote behavior in case of abstentions."]}]}, "calls": {"type": 250}, "events": {"type": 59}, "constants": [{"name": "MaxProposalWeight", "type": 10, "value": "0x070010a5d4e813ffffffffffffff7f", "docs": [" The maximum weight of a dispatch call that can be proposed and executed."]}], "errors": {"type": 627}, "index": 14}, {"name": "TechnicalCommittee", "storage": {"prefix": "TechnicalCommittee", "items": [{"name": "Proposals", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 628}, "fallback": "0x00", "docs": [" The hashes of the active proposals."]}, {"name": "ProposalOf", "modifier": "Optional", "type": {"map": {"hashers": ["Identity"], "key": 13, "value": 141}}, "fallback": "0x00", "docs": [" Actual proposal for a given hash, if it's current."]}, {"name": "Voting", "modifier": "Optional", "type": {"map": {"hashers": ["Identity"], "key": 13, "value": 626}}, "fallback": "0x00", "docs": [" Votes on a given proposal, if it is ongoing."]}, {"name": "ProposalCount", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 4}, "fallback": "0x00000000", "docs": [" Proposals so far."]}, {"name": "Members", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 89}, "fallback": "0x00", "docs": [" The current members of the collective. This is stored sorted (just by value)."]}, {"name": "Prime", "modifier": "Optional", "type": {"plain": 0}, "fallback": "0x00", "docs": [" The prime member that helps determine the default vote behavior in case of abstentions."]}]}, "calls": {"type": 251}, "events": {"type": 60}, "constants": [{"name": "MaxProposalWeight", "type": 10, "value": "0x070010a5d4e813ffffffffffffff7f", "docs": [" The maximum weight of a dispatch call that can be proposed and executed."]}], "errors": {"type": 629}, "index": 15}, {"name": "Elections", "storage": {"prefix": "Elections", "items": [{"name": "Members", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 630}, "fallback": "0x00", "docs": [" The current elected members.", "", " Invariant: Always sorted based on account id."]}, {"name": "RunnersUp", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 630}, "fallback": "0x00", "docs": [" The current reserved runners-up.", "", " Invariant: Always sorted based on rank (worse to best). Upon removal of a member, the", " last (i.e. _best_) runner-up will be replaced."]}, {"name": "Candidates", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 62}, "fallback": "0x00", "docs": [" The present candidate list. A current member or runner-up can never enter this vector", " and is always implicitly assumed to be a candidate.", "", " Second element is the deposit.", "", " Invariant: Always sorted based on account id."]}, {"name": "ElectionRounds", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 4}, "fallback": "0x00000000", "docs": [" The total number of vote rounds that have happened, excluding the upcoming one."]}, {"name": "Voting", "modifier": "<PERSON><PERSON><PERSON>", "type": {"map": {"hashers": ["Twox64Concat"], "key": 0, "value": 632}}, "fallback": "0x************************************************************000000", "docs": [" Votes and locked stake of a particular voter.", "", " TWOX-NOTE: SAFE as `AccountId` is a crypto hash."]}]}, "calls": {"type": 252}, "events": {"type": 61}, "constants": [{"name": "PalletId", "type": 287, "value": "0x706872656c656374", "docs": [" Identifier for the elections-phragmen pallet's lock"]}, {"name": "CandidacyBond", "type": 6, "value": "0x0080c6a47e8d03***************000", "docs": [" How much should be locked up in order to submit one's candidacy."]}, {"name": "VotingBondBase", "type": 6, "value": "0x00f0436de36a01***************000", "docs": [" Base deposit associated with voting.", "", " This should be sensibly high to economically ensure the pallet cannot be attacked by", " creating a gigantic number of votes."]}, {"name": "VotingBondFactor", "type": 6, "value": "0x0000cc7b9fae***************00000", "docs": [" The amount of bond that need to be locked for each vote (32 bytes)."]}, {"name": "DesiredMembers", "type": 4, "value": "0x0d000000", "docs": [" Number of members to elect."]}, {"name": "DesiredRunnersUp", "type": 4, "value": "0x07000000", "docs": [" Number of runners_up to keep."]}, {"name": "TermDuration", "type": 4, "value": "0x80130300", "docs": [" How long each seat is kept. This defines the next block number at which an election", " round will happen. If set to zero, no elections are ever triggered and the module will", " be in passive mode."]}, {"name": "MaxCandidates", "type": 4, "value": "0x40000000", "docs": [" The maximum number of candidates in a phragmen election.", "", " Warning: This impacts the size of the election which is run onchain. <PERSON><PERSON> wisely, and", " consider how it will impact `T::WeightInfo::election_phragmen`.", "", " When this limit is reached no more candidates are accepted in the election."]}, {"name": "MaxVoters", "type": 4, "value": "0x00020000", "docs": [" The maximum number of voters to allow in a phragmen election.", "", " Warning: This impacts the size of the election which is run onchain. <PERSON><PERSON> wisely, and", " consider how it will impact `T::WeightInfo::election_phragmen`.", "", " When the limit is reached the new voters are ignored."]}, {"name": "MaxVotesPerVoter", "type": 4, "value": "0x10000000", "docs": [" Maximum numbers of votes per voter.", "", " Warning: This impacts the size of the election which is run onchain. <PERSON><PERSON> wisely, and", " consider how it will impact `T::WeightInfo::election_phragmen`."]}], "errors": {"type": 633}, "index": 16}, {"name": "TechnicalMembership", "storage": {"prefix": "TechnicalMembership", "items": [{"name": "Members", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 634}, "fallback": "0x00", "docs": [" The current membership, stored as an ordered Vec."]}, {"name": "Prime", "modifier": "Optional", "type": {"plain": 0}, "fallback": "0x00", "docs": [" The current prime member, if one exists."]}]}, "calls": {"type": 254}, "events": {"type": 64}, "constants": [], "errors": {"type": 635}, "index": 17}, {"name": "Grandpa", "storage": {"prefix": "Grandpa", "items": [{"name": "State", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 636}, "fallback": "0x00", "docs": [" State of the current authority set."]}, {"name": "PendingChange", "modifier": "Optional", "type": {"plain": 637}, "fallback": "0x00", "docs": [" Pending change: (signaled at, scheduled change)."]}, {"name": "NextForced", "modifier": "Optional", "type": {"plain": 4}, "fallback": "0x00", "docs": [" next block number where we can force a change."]}, {"name": "Stalled", "modifier": "Optional", "type": {"plain": 94}, "fallback": "0x00", "docs": [" `true` if we are currently stalled."]}, {"name": "CurrentSetId", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 12}, "fallback": "0x***************0", "docs": [" The number of changes (both in terms of keys and underlying economic responsibilities)", " in the \"set\" of <PERSON> validators from genesis."]}, {"name": "SetIdSession", "modifier": "Optional", "type": {"map": {"hashers": ["Twox64Concat"], "key": 12, "value": 4}}, "fallback": "0x00", "docs": [" A mapping from grandpa set ID to the index of the *most recent* session for which its", " members were responsible.", "", " This is only used for validating equivocation proofs. An equivocation proof must", " contains a key-ownership proof for a given session, therefore we need a way to tie", " together sessions and GRANDPA set ids, i.e. we need to validate that a validator", " was the owner of a given key on a given session, and what the active set ID was", " during that session.", "", " TWOX-NOTE: `SetId` is not under user control."]}, {"name": "Authorities", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 638}, "fallback": "0x00", "docs": [" The current list of authorities."]}]}, "calls": {"type": 255}, "events": {"type": 65}, "constants": [{"name": "MaxAuthorities", "type": 4, "value": "0x64000000", "docs": [" Max Authorities in use"]}, {"name": "MaxNominators", "type": 4, "value": "0x40000000", "docs": [" The maximum number of nominators for each validator."]}, {"name": "MaxSetIdSessionEntries", "type": 12, "value": "0xc00f000000000000", "docs": [" The maximum number of entries to keep in the set id to session index mapping.", "", " Since the `SetIdSession` map is only used for validating equivocations this", " value should relate to the bonding duration of whatever staking system is", " being used (if any). If equivocation handling is not enabled then this value", " can be zero."]}], "errors": {"type": 639}, "index": 18}, {"name": "Treasury", "storage": {"prefix": "Treasury", "items": [{"name": "ProposalCount", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 4}, "fallback": "0x00000000", "docs": [" Number of proposals that have been made."]}, {"name": "Proposals", "modifier": "Optional", "type": {"map": {"hashers": ["Twox64Concat"], "key": 4, "value": 640}}, "fallback": "0x00", "docs": [" Proposals that have been made."]}, {"name": "Deactivated", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 6}, "fallback": "0x******************************00", "docs": [" The amount which has been reported as inactive to Currency."]}, {"name": "Approvals", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 641}, "fallback": "0x00", "docs": [" Proposal indices that have been approved but not yet awarded."]}, {"name": "SpendCount", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 4}, "fallback": "0x00000000", "docs": [" The count of spends that have been made."]}, {"name": "Spends", "modifier": "Optional", "type": {"map": {"hashers": ["Twox64Concat"], "key": 4, "value": 642}}, "fallback": "0x00", "docs": [" Spends that have been approved and being processed."]}]}, "calls": {"type": 266}, "events": {"type": 69}, "constants": [{"name": "SpendPeriod", "type": 4, "value": "0x80700000", "docs": [" Period between successive spends."]}, {"name": "Burn", "type": 484, "value": "0x20a10700", "docs": [" Percentage of spare funds (if any) that are burnt per spend period."]}, {"name": "PalletId", "type": 644, "value": "0x70792f7472737279", "docs": [" The treasury's pallet id, used for deriving its sovereign account ID."]}, {"name": "MaxApprovals", "type": 4, "value": "0x64000000", "docs": [" The maximum number of approvals that can wait in the spending queue.", "", " NOTE: This parameter is also used within the Bounties Pallet extension if enabled."]}, {"name": "PayoutPeriod", "type": 4, "value": "0x002f0d00", "docs": [" The period during which an approved treasury spend has to be claimed."]}], "errors": {"type": 645}, "index": 19}, {"name": "AssetRate", "storage": {"prefix": "AssetRate", "items": [{"name": "ConversionRateToNative", "modifier": "Optional", "type": {"map": {"hashers": ["Blake2_128Concat"], "key": 4, "value": 71}}, "fallback": "0x00", "docs": [" Maps an asset to its fixed point representation in the native balance.", "", " E.g. `native_amount = asset_amount * ConversionRateToNative::<T>::get(asset_kind)`"]}]}, "calls": {"type": 267}, "events": {"type": 70}, "constants": [], "errors": {"type": 646}, "index": 20}, {"name": "Contracts", "storage": {"prefix": "Contracts", "items": [{"name": "PristineCode", "modifier": "Optional", "type": {"map": {"hashers": ["Identity"], "key": 13, "value": 647}}, "fallback": "0x00", "docs": [" A mapping from a contract's code hash to its code."]}, {"name": "CodeInfoOf", "modifier": "Optional", "type": {"map": {"hashers": ["Identity"], "key": 13, "value": 648}}, "fallback": "0x00", "docs": [" A mapping from a contract's code hash to its code info."]}, {"name": "<PERSON><PERSON>", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 12}, "fallback": "0x***************0", "docs": [" This is a **monotonic** counter incremented on contract instantiation.", "", " This is used in order to generate unique trie ids for contracts.", " The trie id of a new contract is calculated from hash(account_id, nonce).", " The nonce is required because otherwise the following sequence would lead to", " a possible collision of storage:", "", " 1. Create a new contract.", " 2. Terminate the contract.", " 3. Immediately recreate the contract with the same account_id.", "", " This is bad because the contents of a trie are deleted lazily and there might be", " storage of the old instantiation still in it when the new contract is created. Please", " note that we can't replace the counter by the block number because the sequence above", " can happen in the same block. We also can't keep the account counter in memory only", " because storage is the only way to communicate across different extrinsics in the", " same block.", "", " # Note", "", " Do not use it to determine the number of contracts. It won't be decremented if", " a contract is destroyed."]}, {"name": "ContractInfoOf", "modifier": "Optional", "type": {"map": {"hashers": ["Twox64Concat"], "key": 0, "value": 649}}, "fallback": "0x00", "docs": [" The code associated with a given account.", "", " TWOX-NOTE: SAFE since `AccountId` is a secure hash."]}, {"name": "DeletionQueue", "modifier": "Optional", "type": {"map": {"hashers": ["Twox64Concat"], "key": 4, "value": 113}}, "fallback": "0x00", "docs": [" Evicted contracts that await child trie deletion.", "", " Child trie deletion is a heavy operation depending on the amount of storage items", " stored in said trie. Therefore this operation is performed lazily in `on_idle`."]}, {"name": "Deletion<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 654}, "fallback": "0x***************0", "docs": [" A pair of monotonic counters used to track the latest contract marked for deletion", " and the latest deleted contract in queue."]}, {"name": "MigrationInProgress", "modifier": "Optional", "type": {"plain": 655}, "fallback": "0x00", "docs": [" A migration can span across multiple blocks. This storage defines a cursor to track the", " progress of the migration, enabling us to resume from the last completed position."]}]}, "calls": {"type": 268}, "events": {"type": 72}, "constants": [{"name": "Schedule", "type": 656, "value": "0x0400000010000000200000000040000000000008bd040000", "docs": [" Cost schedule and limits."]}, {"name": "DepositPerByte", "type": 6, "value": "0x0060defb7405***************00000", "docs": [" The amount of balance a caller has to pay for each byte of storage.", "", " # Note", "", " Changing this value for an existing chain might need a storage migration."]}, {"name": "DefaultDepositLimit", "type": 6, "value": "0x0000c0afbc4f8657***************0", "docs": [" Fallback value to limit the storage deposit if it's not being set by the caller."]}, {"name": "DepositPerItem", "type": 6, "value": "0x00f0ab75a40d***************00000", "docs": [" The amount of balance a caller has to pay for each storage item.", "", " # Note", "", " Changing this value for an existing chain might need a storage migration."]}, {"name": "CodeHashLockupDepositPercent", "type": 49, "value": "0x00a3e111", "docs": [" The percentage of the storage deposit that should be held for using a code hash.", " Instantiating a contract, or calling [`chain_extension::Ext::lock_delegate_dependency`]", " protects the code from being removed. In order to prevent abuse these actions are", " protected with a percentage of the code deposit."]}, {"name": "MaxCodeLen", "type": 4, "value": "0x00ec0100", "docs": [" The maximum length of a contract code in bytes.", "", " The value should be chosen carefully taking into the account the overall memory limit", " your runtime has, as well as the [maximum allowed callstack", " depth](#associatedtype.CallStack). Look into the `integrity_test()` for some insights."]}, {"name": "MaxStorageKeyLen", "type": 4, "value": "0x80000000", "docs": [" The maximum allowable length in bytes for storage keys."]}, {"name": "MaxTransientStorageSize", "type": 4, "value": "0x00001000", "docs": [" The maximum size of the transient storage in bytes.", " This includes keys, values, and previous entries used for storage rollback."]}, {"name": "MaxDelegateDependencies", "type": 4, "value": "0x20000000", "docs": [" The maximum number of delegate_dependencies that a contract can lock with", " [`chain_extension::Ext::lock_delegate_dependency`]."]}, {"name": "UnsafeUnstableInterface", "type": 8, "value": "0x00", "docs": [" Make contract callable functions marked as `#[unstable]` available.", "", " Contracts that use `#[unstable]` functions won't be able to be uploaded unless", " this is set to `true`. This is only meant for testnets and dev nodes in order to", " experiment with new features.", "", " # Warning", "", " Do **not** set to `true` on productions chains."]}, {"name": "MaxDebugBufferLen", "type": 4, "value": "0x00002000", "docs": [" The maximum length of the debug buffer in bytes."]}, {"name": "Environment", "type": 659, "value": "0x", "docs": [" Type that bundles together all the runtime configurable interface types.", "", " This is not a real config. We just mention the type here as constant so that", " its type appears in the metadata. Only valid value is `()`."]}, {"name": "ApiVersion", "type": 666, "value": "0x0400", "docs": [" The version of the HostFn APIs that are available in the runtime.", "", " Only valid value is `()`."]}], "errors": {"type": 667}, "index": 21}, {"name": "<PERSON><PERSON>", "storage": {"prefix": "<PERSON><PERSON>", "items": [{"name": "Key", "modifier": "Optional", "type": {"plain": 0}, "fallback": "0x00", "docs": [" The `AccountId` of the sudo key."]}]}, "calls": {"type": 271}, "events": {"type": 75}, "constants": [], "errors": {"type": 668}, "index": 22}, {"name": "ImOnline", "storage": {"prefix": "ImOnline", "items": [{"name": "HeartbeatAfter", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 4}, "fallback": "0x00000000", "docs": [" The block number after which it's ok to send heartbeats in the current", " session.", "", " At the beginning of each session we set this to a value that should fall", " roughly in the middle of the session duration. The idea is to first wait for", " the validators to produce a block in the current session, so that the", " heartbeat later on will not be necessary.", "", " This value will only be used as a fallback if we fail to get a proper session", " progress estimate from `NextSessionRotation`, as those estimates should be", " more accurate then the value we calculate for `HeartbeatAfter`."]}, {"name": "Keys", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 669}, "fallback": "0x00", "docs": [" The current set of keys that may issue a heartbeat."]}, {"name": "ReceivedHeartbeats", "modifier": "Optional", "type": {"map": {"hashers": ["Twox64Concat", "Twox64Concat"], "key": 94, "value": 8}}, "fallback": "0x00", "docs": [" For each session index, we keep a mapping of `SessionIndex` and `AuthIndex`."]}, {"name": "AuthoredBlocks", "modifier": "<PERSON><PERSON><PERSON>", "type": {"map": {"hashers": ["Twox64Concat", "Twox64Concat"], "key": 589, "value": 4}}, "fallback": "0x00000000", "docs": [" For each session index, we keep a mapping of `ValidatorId<T>` to the", " number of blocks authored by the given authority."]}]}, "calls": {"type": 272}, "events": {"type": 76}, "constants": [{"name": "UnsignedPriority", "type": 12, "value": "0xffffffffffffffff", "docs": [" A configuration for base priority of unsigned transactions.", "", " This is exposed so that it can be tuned for particular runtime, when", " multiple pallets send unsigned transactions."]}], "errors": {"type": 671}, "index": 23}, {"name": "AuthorityDiscovery", "storage": {"prefix": "AuthorityDiscovery", "items": [{"name": "Keys", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 672}, "fallback": "0x00", "docs": [" Keys of the current authority set."]}, {"name": "NextKeys", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 672}, "fallback": "0x00", "docs": [" Keys of the next authority set."]}]}, "calls": null, "events": null, "constants": [], "errors": null, "index": 24}, {"name": "Offences", "storage": {"prefix": "Offences", "items": [{"name": "Reports", "modifier": "Optional", "type": {"map": {"hashers": ["Twox64Concat"], "key": 13, "value": 674}}, "fallback": "0x00", "docs": [" The primary structure that holds all offence records keyed by report identifiers."]}, {"name": "ConcurrentReportsIndex", "modifier": "<PERSON><PERSON><PERSON>", "type": {"map": {"hashers": ["Twox64Concat", "Twox64Concat"], "key": 675, "value": 327}}, "fallback": "0x00", "docs": [" A vector of reports of the same kind that happened at the same time slot."]}]}, "calls": null, "events": {"type": 84}, "constants": [], "errors": null, "index": 25}, {"name": "Historical", "storage": {"prefix": "Historical", "items": [{"name": "HistoricalSessions", "modifier": "Optional", "type": {"map": {"hashers": ["Twox64Concat"], "key": 4, "value": 676}}, "fallback": "0x00", "docs": [" Mapping from historical session indices to session-data root hash and validator count."]}, {"name": "StoredRange", "modifier": "Optional", "type": {"plain": 94}, "fallback": "0x00", "docs": [" The range of historical sessions we store. [first, last)"]}]}, "calls": null, "events": null, "constants": [], "errors": null, "index": 26}, {"name": "RandomnessCollectiveFlip", "storage": {"prefix": "RandomnessCollectiveFlip", "items": [{"name": "RandomMaterial", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 677}, "fallback": "0x00", "docs": [" Series of block headers from the last 81 blocks that acts as random seed material. This", " is arranged as a ring buffer with `block_number % 81` being the index into the `Vec` of", " the oldest hash."]}]}, "calls": null, "events": null, "constants": [], "errors": null, "index": 27}, {"name": "Identity", "storage": {"prefix": "Identity", "items": [{"name": "IdentityOf", "modifier": "Optional", "type": {"map": {"hashers": ["Twox64Concat"], "key": 0, "value": 678}}, "fallback": "0x00", "docs": [" Information that is pertinent to identify the entity behind an account. First item is the", " registration, second is the account's primary username.", "", " TWOX-NOTE: OK ― `AccountId` is a secure hash."]}, {"name": "SuperOf", "modifier": "Optional", "type": {"map": {"hashers": ["Blake2_128Concat"], "key": 0, "value": 312}}, "fallback": "0x00", "docs": [" The super-identity of an alternative \"sub\" identity together with its name, within that", " context. If the account is not some other account's sub-identity, then just `None`."]}, {"name": "SubsOf", "modifier": "<PERSON><PERSON><PERSON>", "type": {"map": {"hashers": ["Twox64Concat"], "key": 0, "value": 684}}, "fallback": "0x******************************0000", "docs": [" Alternative \"sub\" identities of this account.", "", " The first item is the deposit, the second is a vector of the accounts.", "", " TWOX-NOTE: OK ― `AccountId` is a secure hash."]}, {"name": "Registrars", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 686}, "fallback": "0x00", "docs": [" The set of registrars. Not expected to get very big as can only be added through a", " special origin (likely a council motion).", "", " The index into this can be cast to `RegistrarIndex` to get a valid value."]}, {"name": "UsernameAuthorities", "modifier": "Optional", "type": {"map": {"hashers": ["Twox64Concat"], "key": 0, "value": 690}}, "fallback": "0x00", "docs": [" A map of the accounts who are authorized to grant usernames."]}, {"name": "AccountOfUsername", "modifier": "Optional", "type": {"map": {"hashers": ["Blake2_128Concat"], "key": 87, "value": 0}}, "fallback": "0x00", "docs": [" Reverse lookup from `username` to the `AccountId` that has registered it. The value should", " be a key in the `IdentityOf` map, but it may not if the user has cleared their identity.", "", " Multiple usernames may map to the same `AccountId`, but `IdentityOf` will only map to one", " primary username."]}, {"name": "PendingUsernames", "modifier": "Optional", "type": {"map": {"hashers": ["Blake2_128Concat"], "key": 87, "value": 596}}, "fallback": "0x00", "docs": [" Usernames that an authority has granted, but that the account controller has not confirmed", " that they want it. Used primarily in cases where the `AccountId` cannot provide a signature", " because they are a pure proxy, multisig, etc. In order to confirm it, they should call", " [`Call::accept_username`].", "", " First tuple item is the account and second is the acceptance deadline."]}]}, "calls": {"type": 275}, "events": {"type": 86}, "constants": [{"name": "BasicDeposit", "type": 6, "value": "0x0050702f696a***************00000", "docs": [" The amount held on deposit for a registered identity."]}, {"name": "ByteDeposit", "type": 6, "value": "0x0060defb7405***************00000", "docs": [" The amount held on deposit per encoded byte for a registered identity."]}, {"name": "SubAccountDeposit", "type": 6, "value": "0x0080f420e6b5***************00000", "docs": [" The amount held on deposit for a registered subaccount. This should account for the fact", " that one storage item's value will increase by the size of an account ID, and there will", " be another trie item whose value is the size of an account ID plus 32 bytes."]}, {"name": "MaxSubAccounts", "type": 4, "value": "0x64000000", "docs": [" The maximum number of sub-accounts allowed per identified account."]}, {"name": "MaxRegistrars", "type": 4, "value": "0x14000000", "docs": [" Maximum number of registrars allowed in the system. Needed to bound the complexity", " of, e.g., updating judgements."]}, {"name": "PendingUsernameExpiration", "type": 4, "value": "0x80130300", "docs": [" The number of blocks within which a username grant must be accepted."]}, {"name": "MaxSuffixLength", "type": 4, "value": "0x07000000", "docs": [" The maximum length of a suffix."]}, {"name": "MaxUsernameLength", "type": 4, "value": "0x20000000", "docs": [" The maximum length of a username, including its suffix and any system-added delimiters."]}], "errors": {"type": 692}, "index": 28}, {"name": "Society", "storage": {"prefix": "Society", "items": [{"name": "Parameters", "modifier": "Optional", "type": {"plain": 90}, "fallback": "0x00", "docs": [" The max number of members for the society at one time."]}, {"name": "Pot", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 6}, "fallback": "0x******************************00", "docs": [" Amount of our account balance that is specifically for the next round's bid(s)."]}, {"name": "Founder", "modifier": "Optional", "type": {"plain": 0}, "fallback": "0x00", "docs": [" The first member."]}, {"name": "Head", "modifier": "Optional", "type": {"plain": 0}, "fallback": "0x00", "docs": [" The most primary from the most recently approved rank 0 members in the society."]}, {"name": "Rules", "modifier": "Optional", "type": {"plain": 13}, "fallback": "0x00", "docs": [" A hash of the rules of this society concerning membership. Can only be set once and", " only by the founder."]}, {"name": "Members", "modifier": "Optional", "type": {"map": {"hashers": ["Twox64Concat"], "key": 0, "value": 693}}, "fallback": "0x00", "docs": [" The current members and their rank. Doesn't include `SuspendedM<PERSON>bers`."]}, {"name": "Payouts", "modifier": "<PERSON><PERSON><PERSON>", "type": {"map": {"hashers": ["Twox64Concat"], "key": 0, "value": 696}}, "fallback": "0x******************************0000", "docs": [" Information regarding rank-0 payouts, past and future."]}, {"name": "MemberCount", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 4}, "fallback": "0x00000000", "docs": [" The number of items in `Members` currently. (Doesn't include `SuspendedMembers`.)"]}, {"name": "MemberByIndex", "modifier": "Optional", "type": {"map": {"hashers": ["Twox64Concat"], "key": 4, "value": 0}}, "fallback": "0x00", "docs": [" The current items in `Members` keyed by their unique index. Keys are densely populated", " `0..MemberCount` (does not include `MemberCount`)."]}, {"name": "SuspendedMembers", "modifier": "Optional", "type": {"map": {"hashers": ["Twox64Concat"], "key": 0, "value": 693}}, "fallback": "0x00", "docs": [" The set of suspended members, with their old membership record."]}, {"name": "RoundCount", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 4}, "fallback": "0x00000000", "docs": [" The number of rounds which have passed."]}, {"name": "Bids", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 700}, "fallback": "0x00", "docs": [" The current bids, stored ordered by the value of the bid."]}, {"name": "Candidates", "modifier": "Optional", "type": {"map": {"hashers": ["Blake2_128Concat"], "key": 0, "value": 704}}, "fallback": "0x00", "docs": []}, {"name": "Skeptic", "modifier": "Optional", "type": {"plain": 0}, "fallback": "0x00", "docs": [" The current skeptic."]}, {"name": "Votes", "modifier": "Optional", "type": {"map": {"hashers": ["Twox64Concat", "Twox64Concat"], "key": 706, "value": 707}}, "fallback": "0x00", "docs": [" Double map from Candidate -> Voter -> (Maybe) Vote."]}, {"name": "VoteClearCursor", "modifier": "Optional", "type": {"map": {"hashers": ["Twox64Concat"], "key": 0, "value": 708}}, "fallback": "0x00", "docs": [" Clear-cursor for Vote, map from Candidate -> (Maybe) Cursor."]}, {"name": "NextHead", "modifier": "Optional", "type": {"plain": 709}, "fallback": "0x00", "docs": [" At the end of the claim period, this contains the most recently approved members (along with", " their bid and round ID) who is from the most recent round with the lowest bid. They will", " become the new `Head`."]}, {"name": "ChallengeRoundCount", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 4}, "fallback": "0x00000000", "docs": [" The number of challenge rounds there have been. Used to identify stale DefenderVotes."]}, {"name": "Defending", "modifier": "Optional", "type": {"plain": 710}, "fallback": "0x00", "docs": [" The defending member currently being challenged, along with a running tally of votes."]}, {"name": "DefenderVotes", "modifier": "Optional", "type": {"map": {"hashers": ["Twox64Concat", "Twox64Concat"], "key": 589, "value": 707}}, "fallback": "0x00", "docs": [" Votes for the defender, keyed by challenge round."]}]}, "calls": {"type": 317}, "events": {"type": 88}, "constants": [{"name": "PalletId", "type": 644, "value": "0x70792f736f636965", "docs": [" The societies's pallet id"]}, {"name": "GraceStrikes", "type": 4, "value": "0x0a000000", "docs": [" The maximum number of strikes before a member gets funds slashed."]}, {"name": "PeriodSpend", "type": 6, "value": "0x0000c52ebca2b1***************000", "docs": [" The amount of incentive paid within each period. Doesn't include VoterTip."]}, {"name": "VotingPeriod", "type": 4, "value": "0x00770100", "docs": [" The number of blocks on which new candidates should be voted on. Together with", " `ClaimPeriod`, this sums to the number of blocks between candidate intake periods."]}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": 4, "value": "0x00770100", "docs": [" The number of blocks on which new candidates can claim their membership and be the", " named head."]}, {"name": "MaxLockDuration", "type": 4, "value": "0x009cda01", "docs": [" The maximum duration of the payout lock."]}, {"name": "ChallengePeriod", "type": 4, "value": "0x80130300", "docs": [" The number of blocks between membership challenges."]}, {"name": "MaxPayouts", "type": 4, "value": "0x0a000000", "docs": [" The maximum number of payouts a member may have waiting unclaimed."]}, {"name": "MaxBids", "type": 4, "value": "0x0a000000", "docs": [" The maximum number of bids at once."]}], "errors": {"type": 711}, "index": 29}, {"name": "Recovery", "storage": {"prefix": "Recovery", "items": [{"name": "Recoverable", "modifier": "Optional", "type": {"map": {"hashers": ["Twox64Concat"], "key": 0, "value": 712}}, "fallback": "0x00", "docs": [" The set of recoverable accounts and their recovery configuration."]}, {"name": "ActiveRecoveries", "modifier": "Optional", "type": {"map": {"hashers": ["Twox64Concat", "Twox64Concat"], "key": 706, "value": 714}}, "fallback": "0x00", "docs": [" Active recovery attempts.", "", " First account is the account to be recovered, and the second account", " is the user trying to recover the account."]}, {"name": "Proxy", "modifier": "Optional", "type": {"map": {"hashers": ["Blake2_128Concat"], "key": 0, "value": 0}}, "fallback": "0x00", "docs": [" The list of allowed proxy accounts.", "", " Map from the user who can access it to the recovered account."]}]}, "calls": {"type": 318}, "events": {"type": 91}, "constants": [{"name": "ConfigDepositBase", "type": 6, "value": "0x00406352bfc601***************000", "docs": [" The base amount of currency needed to reserve for creating a recovery configuration.", "", " This is held for an additional storage item whose value size is", " `2 + sizeof(Block<PERSON>umber, Balance)` bytes."]}, {"name": "FriendDepositFactor", "type": 6, "value": "0x00203d88792d***************00000", "docs": [" The amount of currency needed per additional user when creating a recovery", " configuration.", "", " This is held for adding `sizeof(AccountId)` bytes more into a pre-existing storage", " value."]}, {"name": "MaxFriends", "type": 4, "value": "0x09000000", "docs": [" The maximum amount of friends allowed in a recovery configuration.", "", " NOTE: The threshold programmed in this Pallet uses u16, so it does", " not really make sense to have a limit here greater than u16::MAX.", " But also, that is a lot more than you should probably set this value", " to anyway..."]}, {"name": "RecoveryDeposit", "type": 6, "value": "0x00406352bfc601***************000", "docs": [" The base amount of currency needed to reserve for starting a recovery.", "", " This is primarily held for deterring malicious recovery attempts, and should", " have a value large enough that a bad actor would choose not to place this", " deposit. It also acts to fund additional storage item whose value size is", " `sizeof(<PERSON><PERSON><PERSON>ber, Balance + T * AccountId)` bytes. Where T is a configurable", " threshold."]}], "errors": {"type": 715}, "index": 30}, {"name": "Vesting", "storage": {"prefix": "Vesting", "items": [{"name": "Vesting", "modifier": "Optional", "type": {"map": {"hashers": ["Blake2_128Concat"], "key": 0, "value": 716}}, "fallback": "0x00", "docs": [" Information regarding the vesting of a given account."]}, {"name": "StorageVersion", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 718}, "fallback": "0x00", "docs": [" Storage version of the pallet.", "", " New networks start with latest version, as determined by the genesis build."]}]}, "calls": {"type": 319}, "events": {"type": 92}, "constants": [{"name": "MinVestedTransfer", "type": 6, "value": "0x0000c16ff28623***************000", "docs": [" The minimum amount transferred to call `vested_transfer`."]}, {"name": "MaxVestingSchedules", "type": 4, "value": "0x1c000000", "docs": []}], "errors": {"type": 719}, "index": 31}, {"name": "Scheduler", "storage": {"prefix": "Scheduler", "items": [{"name": "IncompleteSince", "modifier": "Optional", "type": {"plain": 4}, "fallback": "0x00", "docs": []}, {"name": "Agenda", "modifier": "<PERSON><PERSON><PERSON>", "type": {"map": {"hashers": ["Twox64Concat"], "key": 4, "value": 720}}, "fallback": "0x00", "docs": [" Items to be executed, indexed by the block number that they should be executed on."]}, {"name": "Retries", "modifier": "Optional", "type": {"map": {"hashers": ["Blake2_128Concat"], "key": 94, "value": 724}}, "fallback": "0x00", "docs": [" Retry configurations for items to be executed, indexed by task address."]}, {"name": "Lookup", "modifier": "Optional", "type": {"map": {"hashers": ["Twox64Concat"], "key": 1, "value": 94}}, "fallback": "0x00", "docs": [" Lookup from a name to the block number and index of the task.", "", " For v3 -> v4 the previously unbounded identities are Blake2-256 hashed to form the v4", " identities."]}]}, "calls": {"type": 321}, "events": {"type": 93}, "constants": [{"name": "MaximumWeight", "type": 10, "value": "0x0b00806e87740113cccccccccccccccc", "docs": [" The maximum weight that may be scheduled per block for any dispatchables."]}, {"name": "MaxScheduledPerBlock", "type": 4, "value": "0x32000000", "docs": [" The maximum number of scheduled calls in the queue for a single block.", "", " NOTE:", " + Dependent pallets' benchmarks might require a higher limit for the setting. Set a", " higher limit under `runtime-benchmarks` feature."]}], "errors": {"type": 725}, "index": 32}, {"name": "<PERSON><PERSON><PERSON>", "storage": {"prefix": "<PERSON><PERSON><PERSON>", "items": [{"name": "Compute", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 97}, "fallback": "0x***************0", "docs": [" The proportion of the remaining `ref_time` to consume during `on_idle`.", "", " `1.0` is mapped to `100%`. Must be at most [`crate::RESOURCE_HARD_LIMIT`]. Setting this to", " over `1.0` could stall the chain."]}, {"name": "Storage", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 97}, "fallback": "0x***************0", "docs": [" The proportion of the remaining `proof_size` to consume during `on_idle`.", "", " `1.0` is mapped to `100%`. Must be at most [`crate::RESOURCE_HARD_LIMIT`]. Setting this to", " over `1.0` could stall the chain."]}, {"name": "Length", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 97}, "fallback": "0x***************0", "docs": [" The proportion of the `block length` to consume on each block.", "", " `1.0` is mapped to `100%`. Must be at most [`crate::RESOURCE_HARD_LIMIT`]. Setting this to", " over `1.0` could stall the chain."]}, {"name": "TrashData", "modifier": "Optional", "type": {"map": {"hashers": ["Twox64Concat"], "key": 4, "value": 325}}, "fallback": "0x00", "docs": [" Storage map used for wasting proof size.", "", " It contains no meaningful data - hence the name \"Trash\". The maximal number of entries is", " set to 65k, which is just below the next jump at 16^4. This is important to reduce the proof", " size benchmarking overestimate. The assumption here is that we won't have more than 65k *", " 1KiB = 65MiB of proof size wasting in practice. However, this limit is not enforced, so the", " pallet would also work out of the box with more entries, but its benchmarked proof weight", " would possibly be underestimated in that case."]}, {"name": "TrashDataCount", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 4}, "fallback": "0x00000000", "docs": [" The current number of entries in `TrashData`."]}]}, "calls": {"type": 323}, "events": {"type": 96}, "constants": [], "errors": {"type": 726}, "index": 33}, {"name": "Preimage", "storage": {"prefix": "Preimage", "items": [{"name": "StatusFor", "modifier": "Optional", "type": {"map": {"hashers": ["Identity"], "key": 13, "value": 727}}, "fallback": "0x00", "docs": [" The request status of a given hash."]}, {"name": "RequestStatusFor", "modifier": "Optional", "type": {"map": {"hashers": ["Identity"], "key": 13, "value": 729}}, "fallback": "0x00", "docs": [" The request status of a given hash."]}, {"name": "PreimageFor", "modifier": "Optional", "type": {"map": {"hashers": ["Identity"], "key": 676, "value": 733}}, "fallback": "0x00", "docs": []}]}, "calls": {"type": 326}, "events": {"type": 98}, "constants": [], "errors": {"type": 734}, "index": 34}, {"name": "Proxy", "storage": {"prefix": "Proxy", "items": [{"name": "Proxies", "modifier": "<PERSON><PERSON><PERSON>", "type": {"map": {"hashers": ["Twox64Concat"], "key": 0, "value": 735}}, "fallback": "0x******************************0000", "docs": [" The set of account proxies. Maps the account which has delegated to the accounts", " which are being delegated to, together with the amount held on deposit."]}, {"name": "Announcements", "modifier": "<PERSON><PERSON><PERSON>", "type": {"map": {"hashers": ["Twox64Concat"], "key": 0, "value": 739}}, "fallback": "0x******************************0000", "docs": [" The announcements made by the proxy (key)."]}]}, "calls": {"type": 328}, "events": {"type": 99}, "constants": [{"name": "ProxyDepositBase", "type": 6, "value": "0x00f09e544c39***************00000", "docs": [" The base amount of currency needed to reserve for creating a proxy.", "", " This is held for an additional storage item whose value size is", " `sizeof(Balance)` bytes and whose key size is `sizeof(AccountId)` bytes."]}, {"name": "ProxyDepositFactor", "type": 6, "value": "0x0060aa7714b4***************00000", "docs": [" The amount of currency needed per proxy added.", "", " This is held for adding 32 bytes plus an instance of `ProxyType` more into a", " pre-existing storage value. Thus, when configuring `ProxyDepositFactor` one should take", " into account `32 + proxy_type.encode().len()` bytes of data."]}, {"name": "MaxProxies", "type": 4, "value": "0x20000000", "docs": [" The maximum amount of proxies allowed for a single account."]}, {"name": "MaxPending", "type": 4, "value": "0x20000000", "docs": [" The maximum amount of time-delayed announcements that are allowed to be pending."]}, {"name": "AnnouncementDepositBase", "type": 6, "value": "0x00f09e544c39***************00000", "docs": [" The base amount of currency needed to reserve for creating an announcement.", "", " This is held when a new storage item holding a `Balance` is created (typically 16", " bytes)."]}, {"name": "AnnouncementDepositFactor", "type": 6, "value": "0x00c054ef286801***************000", "docs": [" The amount of currency needed per announcement made.", "", " This is held for adding an `AccountId`, `Hash` and `BlockNumber` (typically 68 bytes)", " into a pre-existing storage value."]}], "errors": {"type": 743}, "index": 35}, {"name": "Multisig", "storage": {"prefix": "Multisig", "items": [{"name": "Multisigs", "modifier": "Optional", "type": {"map": {"hashers": ["Twox64Concat", "Blake2_128Concat"], "key": 744, "value": 745}}, "fallback": "0x00", "docs": [" The set of open multisig operations."]}]}, "calls": {"type": 330}, "events": {"type": 102}, "constants": [{"name": "DepositBase", "type": 6, "value": "0x00f01c0adbed01***************000", "docs": [" The base amount of currency needed to reserve for creating a multisig execution or to", " store a dispatch call for later.", "", " This is held for an additional storage item whose value size is", " `4 + sizeof((Block<PERSON><PERSON>ber, Balance, AccountId))` bytes and whose key size is", " `32 + sizeof(AccountId)` bytes."]}, {"name": "DepositFactor", "type": 6, "value": "0x0000cc7b9fae***************00000", "docs": [" The amount of currency needed per unit threshold when creating a multisig execution.", "", " This is held for adding 32 bytes more into a pre-existing storage value."]}, {"name": "MaxSignatories", "type": 4, "value": "0x64000000", "docs": [" The maximum amount of signatories allowed in the multisig."]}], "errors": {"type": 746}, "index": 36}, {"name": "Bounties", "storage": {"prefix": "Bounties", "items": [{"name": "BountyCount", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 4}, "fallback": "0x00000000", "docs": [" Number of bounty proposals that have been made."]}, {"name": "Bounties", "modifier": "Optional", "type": {"map": {"hashers": ["Twox64Concat"], "key": 4, "value": 747}}, "fallback": "0x00", "docs": [" Bounties that have been made."]}, {"name": "BountyDescriptions", "modifier": "Optional", "type": {"map": {"hashers": ["Twox64Concat"], "key": 4, "value": 749}}, "fallback": "0x00", "docs": [" The description of each bounty."]}, {"name": "BountyApprovals", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 641}, "fallback": "0x00", "docs": [" Bounty indices that have been approved but not yet funded."]}]}, "calls": {"type": 332}, "events": {"type": 104}, "constants": [{"name": "BountyDepositBase", "type": 6, "value": "0x00407a10f35a***************00000", "docs": [" The amount held on deposit for placing a bounty proposal."]}, {"name": "BountyDepositPayoutDelay", "type": 4, "value": "0x80700000", "docs": [" The delay period for which a bounty beneficiary need to wait before claim the payout."]}, {"name": "BountyUpdatePeriod", "type": 4, "value": "0x00270600", "docs": [" Bounty duration in blocks."]}, {"name": "CuratorDepositMultiplier", "type": 484, "value": "0x20a10700", "docs": [" The curator deposit is calculated as a percentage of the curator fee.", "", " This deposit has optional upper and lower bounds with `CuratorDepositMax` and", " `CuratorDepositMin`."]}, {"name": "CuratorDepositMax", "type": 236, "value": "0x010000c16ff28623***************000", "docs": [" Maximum amount of funds that should be placed in a deposit for making a proposal."]}, {"name": "CuratorDepositMin", "type": 236, "value": "0x0100407a10f35a***************00000", "docs": [" Minimum amount of funds that should be placed in a deposit for making a proposal."]}, {"name": "BountyValueMinimum", "type": 6, "value": "0x00406352bfc601***************000", "docs": [" Minimum value for a bounty."]}, {"name": "DataDepositPerByte", "type": 6, "value": "0x0010a5d4e8***************0000000", "docs": [" The amount held on deposit per byte within the tip report reason or bounty description."]}, {"name": "MaximumReasonLength", "type": 4, "value": "0x2c010000", "docs": [" Maximum acceptable reason length.", "", " Benchmarks depend on this value, be sure to update weights file when changing this value"]}], "errors": {"type": 750}, "index": 37}, {"name": "Tips", "storage": {"prefix": "Tips", "items": [{"name": "Tips", "modifier": "Optional", "type": {"map": {"hashers": ["Twox64Concat"], "key": 13, "value": 751}}, "fallback": "0x00", "docs": [" TipsMap that are not yet completed. Keyed by the hash of `(reason, who)` from the value.", " This has the insecure enumerable hash function since the key itself is already", " guaranteed to be a secure hash."]}, {"name": "Reasons", "modifier": "Optional", "type": {"map": {"hashers": ["Identity"], "key": 13, "value": 14}}, "fallback": "0x00", "docs": [" Simple preimage lookup from the reason's hash to the original data. Again, has an", " insecure enumerable hash since the key is guaranteed to be the result of a secure hash."]}]}, "calls": {"type": 333}, "events": {"type": 105}, "constants": [{"name": "MaximumReasonLength", "type": 4, "value": "0x2c010000", "docs": [" Maximum acceptable reason length.", "", " Benchmarks depend on this value, be sure to update weights file when changing this value"]}, {"name": "DataDepositPerByte", "type": 6, "value": "0x0010a5d4e8***************0000000", "docs": [" The amount held on deposit per byte within the tip report reason or bounty description."]}, {"name": "TipCountdown", "type": 4, "value": "0x80700000", "docs": [" The period for which a tip remains open after is has achieved threshold tippers."]}, {"name": "TipFindersFee", "type": 230, "value": "0x14", "docs": [" The percent of the final tip which goes to the original reporter of the tip."]}, {"name": "TipReportDepositBase", "type": 6, "value": "0x00407a10f35a***************00000", "docs": [" The non-zero amount held on deposit for placing a tip report."]}, {"name": "MaxTipAmount", "type": 6, "value": "0x0000c52ebca2b1***************000", "docs": [" The maximum amount for a single tip."]}], "errors": {"type": 752}, "index": 38}, {"name": "Assets", "storage": {"prefix": "Assets", "items": [{"name": "<PERSON><PERSON>", "modifier": "Optional", "type": {"map": {"hashers": ["Blake2_128Concat"], "key": 4, "value": 753}}, "fallback": "0x00", "docs": [" Details of an asset."]}, {"name": "Account", "modifier": "Optional", "type": {"map": {"hashers": ["Blake2_128Concat", "Blake2_128Concat"], "key": 589, "value": 755}}, "fallback": "0x00", "docs": [" The holdings of a specific account for a specific asset."]}, {"name": "Approvals", "modifier": "Optional", "type": {"map": {"hashers": ["Blake2_128Concat", "Blake2_128Concat", "Blake2_128Concat"], "key": 758, "value": 759}}, "fallback": "0x00", "docs": [" Approved balance transfers. First balance is the amount approved for transfer. Second", " is the amount of `T::Currency` reserved for storing this.", " First key is the asset ID, second key is the owner and third key is the delegate."]}, {"name": "<PERSON><PERSON><PERSON>", "modifier": "<PERSON><PERSON><PERSON>", "type": {"map": {"hashers": ["Blake2_128Concat"], "key": 4, "value": 760}}, "fallback": "0x******************************0000000000", "docs": [" Metadata of an asset."]}, {"name": "NextAssetId", "modifier": "Optional", "type": {"plain": 4}, "fallback": "0x00", "docs": [" The asset ID enforced for the next asset creation, if any present. Otherwise, this storage", " item has no effect.", "", " This can be useful for setting up constraints for IDs of the new assets. For example, by", " providing an initial [`NextAssetId`] and using the [`crate::AutoIncAssetId`] callback, an", " auto-increment model can be applied to all new asset IDs.", "", " The initial next asset ID can be set using the [`GenesisConfig`] or the", " [SetNextAssetId](`migration::next_asset_id::SetNextAssetId`) migration."]}]}, "calls": {"type": 334}, "events": {"type": 106}, "constants": [{"name": "RemoveItemsLimit", "type": 4, "value": "0xe8030000", "docs": [" Max number of items to destroy per `destroy_accounts` and `destroy_approvals` call.", "", " Must be configured to result in a weight that makes each call fit in a block."]}, {"name": "AssetDeposit", "type": 6, "value": "0x0000c16ff28623***************000", "docs": [" The basic amount of funds that must be reserved for an asset."]}, {"name": "AssetAccountDeposit", "type": 6, "value": "0x00407a10f35a***************00000", "docs": [" The amount of funds that must be reserved for a non-provider asset account to be", " maintained."]}, {"name": "MetadataDepositBase", "type": 6, "value": "0x0080c6a47e8d03***************000", "docs": [" The basic amount of funds that must be reserved when adding metadata to your asset."]}, {"name": "MetadataDepositPerByte", "type": 6, "value": "0x00407a10f35a***************00000", "docs": [" The additional funds that must be reserved for the number of bytes you store in your", " metadata."]}, {"name": "ApprovalDeposit", "type": 6, "value": "0x00407a10f35a***************00000", "docs": [" The amount of funds that must be reserved when creating a new approval."]}, {"name": "StringLimit", "type": 4, "value": "0x32000000", "docs": [" The maximum length of a name or symbol stored on-chain."]}], "errors": {"type": 762}, "index": 39}, {"name": "PoolAssets", "storage": {"prefix": "PoolAssets", "items": [{"name": "<PERSON><PERSON>", "modifier": "Optional", "type": {"map": {"hashers": ["Blake2_128Concat"], "key": 4, "value": 753}}, "fallback": "0x00", "docs": [" Details of an asset."]}, {"name": "Account", "modifier": "Optional", "type": {"map": {"hashers": ["Blake2_128Concat", "Blake2_128Concat"], "key": 589, "value": 755}}, "fallback": "0x00", "docs": [" The holdings of a specific account for a specific asset."]}, {"name": "Approvals", "modifier": "Optional", "type": {"map": {"hashers": ["Blake2_128Concat", "Blake2_128Concat", "Blake2_128Concat"], "key": 758, "value": 759}}, "fallback": "0x00", "docs": [" Approved balance transfers. First balance is the amount approved for transfer. Second", " is the amount of `T::Currency` reserved for storing this.", " First key is the asset ID, second key is the owner and third key is the delegate."]}, {"name": "<PERSON><PERSON><PERSON>", "modifier": "<PERSON><PERSON><PERSON>", "type": {"map": {"hashers": ["Blake2_128Concat"], "key": 4, "value": 760}}, "fallback": "0x******************************0000000000", "docs": [" Metadata of an asset."]}, {"name": "NextAssetId", "modifier": "Optional", "type": {"plain": 4}, "fallback": "0x00", "docs": [" The asset ID enforced for the next asset creation, if any present. Otherwise, this storage", " item has no effect.", "", " This can be useful for setting up constraints for IDs of the new assets. For example, by", " providing an initial [`NextAssetId`] and using the [`crate::AutoIncAssetId`] callback, an", " auto-increment model can be applied to all new asset IDs.", "", " The initial next asset ID can be set using the [`GenesisConfig`] or the", " [SetNextAssetId](`migration::next_asset_id::SetNextAssetId`) migration."]}]}, "calls": {"type": 335}, "events": {"type": 107}, "constants": [{"name": "RemoveItemsLimit", "type": 4, "value": "0xe8030000", "docs": [" Max number of items to destroy per `destroy_accounts` and `destroy_approvals` call.", "", " Must be configured to result in a weight that makes each call fit in a block."]}, {"name": "AssetDeposit", "type": 6, "value": "0x0000c16ff28623***************000", "docs": [" The basic amount of funds that must be reserved for an asset."]}, {"name": "AssetAccountDeposit", "type": 6, "value": "0x00407a10f35a***************00000", "docs": [" The amount of funds that must be reserved for a non-provider asset account to be", " maintained."]}, {"name": "MetadataDepositBase", "type": 6, "value": "0x0080c6a47e8d03***************000", "docs": [" The basic amount of funds that must be reserved when adding metadata to your asset."]}, {"name": "MetadataDepositPerByte", "type": 6, "value": "0x00407a10f35a***************00000", "docs": [" The additional funds that must be reserved for the number of bytes you store in your", " metadata."]}, {"name": "ApprovalDeposit", "type": 6, "value": "0x00407a10f35a***************00000", "docs": [" The amount of funds that must be reserved when creating a new approval."]}, {"name": "StringLimit", "type": 4, "value": "0x32000000", "docs": [" The maximum length of a name or symbol stored on-chain."]}], "errors": {"type": 763}, "index": 40}, {"name": "Beefy", "storage": {"prefix": "Beefy", "items": [{"name": "Authorities", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 764}, "fallback": "0x00", "docs": [" The current authorities set"]}, {"name": "ValidatorSetId", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 12}, "fallback": "0x***************0", "docs": [" The current validator set id"]}, {"name": "NextAuthorities", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 764}, "fallback": "0x00", "docs": [" Authorities set scheduled to be used with the next session"]}, {"name": "SetIdSession", "modifier": "Optional", "type": {"map": {"hashers": ["Twox64Concat"], "key": 12, "value": 4}}, "fallback": "0x00", "docs": [" A mapping from BEEFY set ID to the index of the *most recent* session for which its", " members were responsible.", "", " This is only used for validating equivocation proofs. An equivocation proof must", " contains a key-ownership proof for a given session, therefore we need a way to tie", " together sessions and BEEFY set ids, i.e. we need to validate that a validator", " was the owner of a given key on a given session, and what the active set ID was", " during that session.", "", " TWOX-NOTE: `ValidatorSetId` is not under user control."]}, {"name": "GenesisBlock", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 39}, "fallback": "0x00", "docs": [" Block number where BEEFY consensus is enabled/started.", " By changing this (through privileged `set_new_genesis()`), BEEFY consensus is effectively", " restarted from the newly set block number."]}]}, "calls": {"type": 336}, "events": null, "constants": [{"name": "MaxAuthorities", "type": 4, "value": "0x64000000", "docs": [" The maximum number of authorities that can be added."]}, {"name": "MaxNominators", "type": 4, "value": "0x00000000", "docs": [" The maximum number of nominators for each validator."]}, {"name": "MaxSetIdSessionEntries", "type": 12, "value": "0xc00f000000000000", "docs": [" The maximum number of entries to keep in the set id to session index mapping.", "", " Since the `SetIdSession` map is only used for validating equivocations this", " value should relate to the bonding duration of whatever staking system is", " being used (if any). If equivocation handling is not enabled then this value", " can be zero."]}], "errors": {"type": 766}, "index": 41}, {"name": "Mmr", "storage": {"prefix": "Mmr", "items": [{"name": "RootHash", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 13}, "fallback": "0x************************************************************0000", "docs": [" Latest MMR Root hash."]}, {"name": "NumberOfLeaves", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 12}, "fallback": "0x***************0", "docs": [" Current size of the MMR (number of leaves)."]}, {"name": "Nodes", "modifier": "Optional", "type": {"map": {"hashers": ["Identity"], "key": 12, "value": 13}}, "fallback": "0x00", "docs": [" Hashes of the nodes in the MMR.", "", " Note this collection only contains MMR peaks, the inner nodes (and leaves)", " are pruned and only stored in the Offchain DB."]}]}, "calls": null, "events": null, "constants": [], "errors": null, "index": 42}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "storage": {"prefix": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "items": [{"name": "BeefyAuthorities", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 767}, "fallback": "0x***************************************************************************0000000000000", "docs": [" Details of current BEEFY authority set."]}, {"name": "BeefyNextAuthorities", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 767}, "fallback": "0x***************************************************************************0000000000000", "docs": [" Details of next BEEFY authority set.", "", " This storage entry is used as cache for calls to `update_beefy_next_authority_set`."]}]}, "calls": null, "events": null, "constants": [], "errors": null, "index": 43}, {"name": "Lottery", "storage": {"prefix": "Lottery", "items": [{"name": "LotteryIndex", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 4}, "fallback": "0x00000000", "docs": []}, {"name": "Lottery", "modifier": "Optional", "type": {"plain": 768}, "fallback": "0x00", "docs": [" The configuration for the current lottery."]}, {"name": "Participants", "modifier": "<PERSON><PERSON><PERSON>", "type": {"map": {"hashers": ["Twox64Concat"], "key": 0, "value": 769}}, "fallback": "0x0000000000", "docs": [" Users who have purchased a ticket. (Lottery Index, Tickets Purchased)"]}, {"name": "TicketsCount", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 4}, "fallback": "0x00000000", "docs": [" Total number of tickets sold."]}, {"name": "Tickets", "modifier": "Optional", "type": {"map": {"hashers": ["Twox64Concat"], "key": 4, "value": 0}}, "fallback": "0x00", "docs": [" Each ticket's owner.", "", " May have residual storage from previous lotteries. Use `TicketsCount` to see which ones", " are actually valid ticket mappings."]}, {"name": "CallIndices", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 770}, "fallback": "0x00", "docs": [" The calls stored in this pallet to be used in an active lottery if configured", " by `Config::ValidateCall`."]}]}, "calls": {"type": 349}, "events": {"type": 108}, "constants": [{"name": "PalletId", "type": 644, "value": "0x70792f6c6f74746f", "docs": [" The Lottery's pallet id"]}, {"name": "MaxCalls", "type": 4, "value": "0x0a000000", "docs": [" The max number of calls available in a single lottery."]}, {"name": "MaxGenerateRandom", "type": 4, "value": "0x0a000000", "docs": [" Number of time we should try to generate a random number that has no modulo bias.", " The larger this number, the more potential computation is used for picking the winner,", " but also the more likely that the chosen winner is done fairly."]}], "errors": {"type": 772}, "index": 44}, {"name": "<PERSON><PERSON>", "storage": {"prefix": "<PERSON><PERSON>", "items": [{"name": "QueueTotals", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 773}, "fallback": "0xb104************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "docs": [" The totals of items and balances within each queue. Saves a lot of storage reads in the", " case of sparsely packed queues.", "", " The vector is indexed by duration in `Period`s, offset by one, so information on the queue", " whose duration is one `Period` would be storage `0`."]}, {"name": "Queues", "modifier": "<PERSON><PERSON><PERSON>", "type": {"map": {"hashers": ["Blake2_128Concat"], "key": 4, "value": 774}}, "fallback": "0x00", "docs": [" The queues of bids. Indexed by duration (in `Period`s)."]}, {"name": "Summary", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 777}, "fallback": "0x***************************************************************************00000", "docs": [" Summary information over the general state."]}, {"name": "Receipts", "modifier": "Optional", "type": {"map": {"hashers": ["Blake2_128Concat"], "key": 4, "value": 778}}, "fallback": "0x00", "docs": [" The currently outstanding receipts, indexed according to the order of creation."]}]}, "calls": {"type": 350}, "events": {"type": 110}, "constants": [{"name": "PalletId", "type": 644, "value": "0x70792f6e69732020", "docs": [" The treasury's pallet id, used for deriving its sovereign account ID."]}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": 4, "value": "0x2c010000", "docs": [" Number of duration queues in total. This sets the maximum duration supported, which is", " this value multiplied by `Period`."]}, {"name": "MaxQueueLen", "type": 4, "value": "0xe8030000", "docs": [" Maximum number of items that may be in each duration queue.", "", " Must be larger than zero."]}, {"name": "FifoQueueLen", "type": 4, "value": "0xf4010000", "docs": [" Portion of the queue which is free from ordering and just a FIFO.", "", " Must be no greater than `<PERSON><PERSON><PERSON>ue<PERSON><PERSON>`."]}, {"name": "BasePeriod", "type": 4, "value": "0x002f0d00", "docs": [" The base period for the duration queues. This is the common multiple across all", " supported freezing durations that can be bid upon."]}, {"name": "MinBid", "type": 6, "value": "0x0000c16ff28623***************000", "docs": [" The minimum amount of funds that may be placed in a bid. Note that this", " does not actually limit the amount which may be represented in a receipt since bids may", " be split up by the system.", "", " It should be at least big enough to ensure that there is no possible storage spam attack", " or queue-filling attack."]}, {"name": "MinReceipt", "type": 111, "value": "0x0000c16ff2862300", "docs": [" The minimum amount of funds which may intentionally be left remaining under a single", " receipt."]}, {"name": "IntakePeriod", "type": 4, "value": "0x0a000000", "docs": [" The number of blocks between consecutive attempts to dequeue bids and create receipts.", "", " A larger value results in fewer storage hits each block, but a slower period to get to", " the target."]}, {"name": "MaxIntakeWeight", "type": 10, "value": "0x0700d0ed902e139999999999999919", "docs": [" The maximum amount of bids that can consolidated into receipts in a single intake. A", " larger value here means less of the block available for transactions should there be a", " glut of bids."]}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": 779, "value": "0x0000d9e9ac2d780305000000", "docs": [" The maximum proportion which may be thawed and the period over which it is reset."]}], "errors": {"type": 780}, "index": 45}, {"name": "Uniques", "storage": {"prefix": "Uniques", "items": [{"name": "Class", "modifier": "Optional", "type": {"map": {"hashers": ["Blake2_128Concat"], "key": 4, "value": 781}}, "fallback": "0x00", "docs": [" Details of a collection."]}, {"name": "OwnershipAcceptance", "modifier": "Optional", "type": {"map": {"hashers": ["Blake2_128Concat"], "key": 0, "value": 4}}, "fallback": "0x00", "docs": [" The collection, if any, of which an account is willing to take ownership."]}, {"name": "Account", "modifier": "Optional", "type": {"map": {"hashers": ["Blake2_128Concat", "Blake2_128Concat", "Blake2_128Concat"], "key": 782, "value": 33}}, "fallback": "0x00", "docs": [" The items held by any given account; set out this way so that items owned by a single", " account can be enumerated."]}, {"name": "ClassAccount", "modifier": "Optional", "type": {"map": {"hashers": ["Blake2_128Concat", "Blake2_128Concat"], "key": 596, "value": 33}}, "fallback": "0x00", "docs": [" The collections owned by any given account; set out this way so that collections owned by", " a single account can be enumerated."]}, {"name": "<PERSON><PERSON>", "modifier": "Optional", "type": {"map": {"hashers": ["Blake2_128Concat", "Blake2_128Concat"], "key": 94, "value": 783}}, "fallback": "0x00", "docs": [" The items in existence and their ownership details."]}, {"name": "ClassMetadataOf", "modifier": "Optional", "type": {"map": {"hashers": ["Blake2_128Concat"], "key": 4, "value": 784}}, "fallback": "0x00", "docs": [" Metadata of a collection."]}, {"name": "InstanceMetadataOf", "modifier": "Optional", "type": {"map": {"hashers": ["Blake2_128Concat", "Blake2_128Concat"], "key": 94, "value": 785}}, "fallback": "0x00", "docs": [" Metadata of an item."]}, {"name": "Attribute", "modifier": "Optional", "type": {"map": {"hashers": ["Blake2_128Concat", "Blake2_128Concat", "Blake2_128Concat"], "key": 786, "value": 787}}, "fallback": "0x00", "docs": [" Attributes of a collection."]}, {"name": "ItemPriceOf", "modifier": "Optional", "type": {"map": {"hashers": ["Blake2_128Concat", "Blake2_128Concat"], "key": 94, "value": 788}}, "fallback": "0x00", "docs": [" Price of an asset instance."]}, {"name": "CollectionMaxSupply", "modifier": "Optional", "type": {"map": {"hashers": ["Blake2_128Concat"], "key": 4, "value": 4}}, "fallback": "0x00", "docs": [" Keeps track of the number of items a collection might have."]}]}, "calls": {"type": 352}, "events": {"type": 112}, "constants": [{"name": "CollectionDeposit", "type": 6, "value": "0x0000c16ff28623***************000", "docs": [" The basic amount of funds that must be reserved for collection."]}, {"name": "ItemDeposit", "type": 6, "value": "0x00407a10f35a***************00000", "docs": [" The basic amount of funds that must be reserved for an item."]}, {"name": "MetadataDepositBase", "type": 6, "value": "0x0080c6a47e8d03***************000", "docs": [" The basic amount of funds that must be reserved when adding metadata to your item."]}, {"name": "AttributeDepositBase", "type": 6, "value": "0x0080c6a47e8d03***************000", "docs": [" The basic amount of funds that must be reserved when adding an attribute to an item."]}, {"name": "DepositPerByte", "type": 6, "value": "0x00407a10f35a***************00000", "docs": [" The additional funds that must be reserved for the number of bytes store in metadata,", " either \"normal\" metadata or attribute metadata."]}, {"name": "StringLimit", "type": 4, "value": "0x80000000", "docs": [" The maximum length of data stored on-chain."]}, {"name": "KeyLimit", "type": 4, "value": "0x20000000", "docs": [" The maximum length of an attribute key."]}, {"name": "ValueLimit", "type": 4, "value": "0x40000000", "docs": [" The maximum length of an attribute value."]}], "errors": {"type": 789}, "index": 46}, {"name": "Nfts", "storage": {"prefix": "Nfts", "items": [{"name": "Collection", "modifier": "Optional", "type": {"map": {"hashers": ["Blake2_128Concat"], "key": 4, "value": 790}}, "fallback": "0x00", "docs": [" Details of a collection."]}, {"name": "OwnershipAcceptance", "modifier": "Optional", "type": {"map": {"hashers": ["Blake2_128Concat"], "key": 0, "value": 4}}, "fallback": "0x00", "docs": [" The collection, if any, of which an account is willing to take ownership."]}, {"name": "Account", "modifier": "Optional", "type": {"map": {"hashers": ["Blake2_128Concat", "Blake2_128Concat", "Blake2_128Concat"], "key": 782, "value": 33}}, "fallback": "0x00", "docs": [" The items held by any given account; set out this way so that items owned by a single", " account can be enumerated."]}, {"name": "CollectionAccount", "modifier": "Optional", "type": {"map": {"hashers": ["Blake2_128Concat", "Blake2_128Concat"], "key": 596, "value": 33}}, "fallback": "0x00", "docs": [" The collections owned by any given account; set out this way so that collections owned by", " a single account can be enumerated."]}, {"name": "CollectionRoleOf", "modifier": "Optional", "type": {"map": {"hashers": ["Blake2_128Concat", "Blake2_128Concat"], "key": 589, "value": 791}}, "fallback": "0x00", "docs": [" The items in existence and their ownership details.", " Stores collection roles as per account."]}, {"name": "<PERSON><PERSON>", "modifier": "Optional", "type": {"map": {"hashers": ["Blake2_128Concat", "Blake2_128Concat"], "key": 94, "value": 793}}, "fallback": "0x00", "docs": [" The items in existence and their ownership details."]}, {"name": "CollectionMetadataOf", "modifier": "Optional", "type": {"map": {"hashers": ["Blake2_128Concat"], "key": 4, "value": 799}}, "fallback": "0x00", "docs": [" Metadata of a collection."]}, {"name": "ItemMetadataOf", "modifier": "Optional", "type": {"map": {"hashers": ["Blake2_128Concat", "Blake2_128Concat"], "key": 94, "value": 800}}, "fallback": "0x00", "docs": [" Metadata of an item."]}, {"name": "Attribute", "modifier": "Optional", "type": {"map": {"hashers": ["Blake2_128Concat", "Blake2_128Concat", "Blake2_128Concat", "Blake2_128Concat"], "key": 802, "value": 803}}, "fallback": "0x00", "docs": [" Attributes of a collection."]}, {"name": "ItemPriceOf", "modifier": "Optional", "type": {"map": {"hashers": ["Blake2_128Concat", "Blake2_128Concat"], "key": 94, "value": 788}}, "fallback": "0x00", "docs": [" A price of an item."]}, {"name": "ItemAttributesApprovalsOf", "modifier": "<PERSON><PERSON><PERSON>", "type": {"map": {"hashers": ["Blake2_128Concat", "Blake2_128Concat"], "key": 94, "value": 805}}, "fallback": "0x00", "docs": [" Item attribute approvals."]}, {"name": "NextCollectionId", "modifier": "Optional", "type": {"plain": 4}, "fallback": "0x00", "docs": [" Stores the `CollectionId` that is going to be used for the next collection.", " This gets incremented whenever a new collection is created."]}, {"name": "PendingSwapOf", "modifier": "Optional", "type": {"map": {"hashers": ["Blake2_128Concat", "Blake2_128Concat"], "key": 94, "value": 807}}, "fallback": "0x00", "docs": [" Handles all the pending swaps."]}, {"name": "CollectionConfigOf", "modifier": "Optional", "type": {"map": {"hashers": ["Blake2_128Concat"], "key": 4, "value": 356}}, "fallback": "0x00", "docs": [" Config of a collection."]}, {"name": "ItemConfigOf", "modifier": "Optional", "type": {"map": {"hashers": ["Blake2_128Concat", "Blake2_128Concat"], "key": 94, "value": 366}}, "fallback": "0x00", "docs": [" Config of an item."]}]}, "calls": {"type": 355}, "events": {"type": 116}, "constants": [{"name": "CollectionDeposit", "type": 6, "value": "0x0000c16ff28623***************000", "docs": [" The basic amount of funds that must be reserved for collection."]}, {"name": "ItemDeposit", "type": 6, "value": "0x00407a10f35a***************00000", "docs": [" The basic amount of funds that must be reserved for an item."]}, {"name": "MetadataDepositBase", "type": 6, "value": "0x0080c6a47e8d03***************000", "docs": [" The basic amount of funds that must be reserved when adding metadata to your item."]}, {"name": "AttributeDepositBase", "type": 6, "value": "0x0080c6a47e8d03***************000", "docs": [" The basic amount of funds that must be reserved when adding an attribute to an item."]}, {"name": "DepositPerByte", "type": 6, "value": "0x00407a10f35a***************00000", "docs": [" The additional funds that must be reserved for the number of bytes store in metadata,", " either \"normal\" metadata or attribute metadata."]}, {"name": "StringLimit", "type": 4, "value": "0x00010000", "docs": [" The maximum length of data stored on-chain."]}, {"name": "KeyLimit", "type": 4, "value": "0x40000000", "docs": [" The maximum length of an attribute key."]}, {"name": "ValueLimit", "type": 4, "value": "0x00010000", "docs": [" The maximum length of an attribute value."]}, {"name": "ApprovalsLimit", "type": 4, "value": "0x14000000", "docs": [" The maximum approvals an item could have."]}, {"name": "ItemAttributesApprovalsLimit", "type": 4, "value": "0x14000000", "docs": [" The maximum attributes approvals an item could have."]}, {"name": "MaxTips", "type": 4, "value": "0x0a000000", "docs": [" The max number of tips a user could send."]}, {"name": "MaxDeadlineDuration", "type": 4, "value": "0x00349e00", "docs": [" The max duration in blocks for deadlines."]}, {"name": "MaxAttributesPerCall", "type": 4, "value": "0x0a000000", "docs": [" The max number of attributes a user could set per call."]}, {"name": "Features", "type": 808, "value": "0x***************0", "docs": [" Disables some of pallet's features."]}], "errors": {"type": 810}, "index": 47}, {"name": "NftFractionalization", "storage": {"prefix": "NftFractionalization", "items": [{"name": "NftToAsset", "modifier": "Optional", "type": {"map": {"hashers": ["Blake2_128Concat"], "key": 94, "value": 811}}, "fallback": "0x00", "docs": [" Keeps track of the corresponding NFT ID, asset ID and amount minted."]}]}, "calls": {"type": 373}, "events": {"type": 123}, "constants": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "type": 6, "value": "0x0000c16ff28623***************000", "docs": [" The deposit paid by the user locking an NFT. The deposit is returned to the original NFT", " owner when the asset is unified and the NFT is unlocked."]}, {"name": "PalletId", "type": 644, "value": "0x6672616374696f6e", "docs": [" The pallet's id, used for deriving its sovereign account ID."]}, {"name": "NewAssetSymbol", "type": 761, "value": "0x1046524143", "docs": [" The newly created asset's symbol."]}, {"name": "NewAssetName", "type": 761, "value": "0x1046726163", "docs": [" The newly created asset's name."]}, {"name": "StringLimit", "type": 4, "value": "0x32000000", "docs": [" The maximum length of a name or symbol stored on-chain."]}], "errors": {"type": 812}, "index": 48}, {"name": "Salary", "storage": {"prefix": "Salary", "items": [{"name": "Status", "modifier": "Optional", "type": {"plain": 813}, "fallback": "0x00", "docs": [" The overall status of the system."]}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "modifier": "Optional", "type": {"map": {"hashers": ["Twox64Concat"], "key": 0, "value": 814}}, "fallback": "0x00", "docs": [" The status of a claimant."]}]}, "calls": {"type": 374}, "events": {"type": 124}, "constants": [{"name": "RegistrationPeriod", "type": 4, "value": "0xc8000000", "docs": [" The number of blocks within a cycle which accounts have to register their intent to", " claim.", "", " The number of blocks between sequential payout cycles is the sum of this and", " `PayoutPeriod`."]}, {"name": "PayoutPeriod", "type": 4, "value": "0xc8000000", "docs": [" The number of blocks within a cycle which accounts have to claim the payout.", "", " The number of blocks between sequential payout cycles is the sum of this and", " `RegistrationPeriod`."]}, {"name": "Budget", "type": 6, "value": "0x000064a7b3b6e00d***************0", "docs": [" The total budget per cycle.", "", " This may change over the course of a cycle without any problem."]}], "errors": {"type": 816}, "index": 49}, {"name": "CoreFellowship", "storage": {"prefix": "CoreFellowship", "items": [{"name": "Params", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 126}, "fallback": "0x***************0", "docs": [" The overall status of the system."]}, {"name": "Member", "modifier": "Optional", "type": {"map": {"hashers": ["Twox64Concat"], "key": 0, "value": 817}}, "fallback": "0x00", "docs": [" The status of a claimant."]}, {"name": "MemberEvidence", "modifier": "Optional", "type": {"map": {"hashers": ["Twox64Concat"], "key": 0, "value": 818}}, "fallback": "0x00", "docs": [" Some evidence together with the desired outcome for which it was presented."]}]}, "calls": {"type": 375}, "events": {"type": 125}, "constants": [{"name": "EvidenceSize", "type": 4, "value": "0x00400000", "docs": [" The maximum size in bytes submitted evidence is allowed to be."]}, {"name": "MaxRank", "type": 4, "value": "0x09000000", "docs": [" Represents the highest possible rank in this pallet.", "", " Increasing this value is supported, but decreasing it may lead to a broken state."]}], "errors": {"type": 819}, "index": 50}, {"name": "TransactionStorage", "storage": {"prefix": "TransactionStorage", "items": [{"name": "Transactions", "modifier": "Optional", "type": {"map": {"hashers": ["Blake2_128Concat"], "key": 4, "value": 820}}, "fallback": "0x00", "docs": [" Collection of transaction metadata by block number."]}, {"name": "ChunkCount", "modifier": "<PERSON><PERSON><PERSON>", "type": {"map": {"hashers": ["Blake2_128Concat"], "key": 4, "value": 4}}, "fallback": "0x00000000", "docs": [" Count indexed chunks for each block."]}, {"name": "ByteFee", "modifier": "Optional", "type": {"plain": 6}, "fallback": "0x00", "docs": [" Storage fee per byte."]}, {"name": "EntryFee", "modifier": "Optional", "type": {"plain": 6}, "fallback": "0x00", "docs": [" Storage fee per transaction."]}, {"name": "StoragePeriod", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 4}, "fallback": "0x00000000", "docs": [" Storage period for data in blocks. Should match `sp_storage_proof::DEFAULT_STORAGE_PERIOD`", " for block authoring."]}, {"name": "BlockTransactions", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 820}, "fallback": "0x00", "docs": []}, {"name": "ProofChecked", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 8}, "fallback": "0x00", "docs": [" Was the proof checked in this block?"]}]}, "calls": {"type": 381}, "events": {"type": 133}, "constants": [], "errors": {"type": 823}, "index": 51}, {"name": "VoterList", "storage": {"prefix": "VoterList", "items": [{"name": "ListNodes", "modifier": "Optional", "type": {"map": {"hashers": ["Twox64Concat"], "key": 0, "value": 824}}, "fallback": "0x00", "docs": [" A single node, within some bag.", "", " Nodes store links forward and back within their respective bags."]}, {"name": "CounterForListNodes", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 4}, "fallback": "0x00000000", "docs": ["Counter for the related counted storage map"]}, {"name": "ListBags", "modifier": "Optional", "type": {"map": {"hashers": ["Twox64Concat"], "key": 12, "value": 825}}, "fallback": "0x00", "docs": [" A bag stored in storage.", "", " Stores a `Bag` struct, which stores head and tail pointers to itself."]}]}, "calls": {"type": 383}, "events": {"type": 134}, "constants": [{"name": "BagThresholds", "type": 826, "value": "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", "docs": [" The list of thresholds separating the various bags.", "", " Ids are separated into unsorted bags according to their score. This specifies the", " thresholds separating the bags. An id's bag is the largest bag for which the id's score", " is less than or equal to its upper threshold.", "", " When ids are iterated, higher bags are iterated completely before lower bags. This means", " that iteration is _semi-sorted_: ids of higher score tend to come before ids of lower", " score, but peer ids within a particular bag are sorted in insertion order.", "", " # Expressing the constant", "", " This constant must be sorted in strictly increasing order. Duplicate items are not", " permitted.", "", " There is an implied upper limit of `Score::MAX`; that value does not need to be", " specified within the bag. For any two threshold lists, if one ends with", " `Score::MAX`, the other one does not, and they are otherwise equal, the two", " lists will behave identically.", "", " # Calculation", "", " It is recommended to generate the set of thresholds in a geometric series, such that", " there exists some constant ratio such that `threshold[k + 1] == (threshold[k] *", " constant_ratio).max(threshold[k] + 1)` for all `k`.", "", " The helpers in the `/utils/frame/generate-bags` module can simplify this calculation.", "", " # Examples", "", " - If `BagThresholds::get().is_empty()`, then all ids are put into the same bag, and", "   iteration is strictly in insertion order.", " - If `BagThresholds::get().len() == 64`, and the thresholds are determined according to", "   the procedure given above, then the constant ratio is equal to 2.", " - If `BagThresholds::get().len() == 200`, and the thresholds are determined according to", "   the procedure given above, then the constant ratio is approximately equal to 1.248.", " - If the threshold list begins `[1, 2, 3, ...]`, then an id with score 0 or 1 will fall", "   into bag 0, an id with score 2 will fall into bag 1, etc.", "", " # Migration", "", " In the event that this list ever changes, a copy of the old bags list must be retained.", " With that `List::migrate` can be called, which will perform the appropriate migration."]}], "errors": {"type": 827}, "index": 52}, {"name": "StateTrieMigration", "storage": {"prefix": "StateTrieMigration", "items": [{"name": "MigrationProcess", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 387}, "fallback": "0x***************0000000000000", "docs": [" Migration progress.", "", " This stores the snapshot of the last migrated keys. It can be set into motion and move", " forward by any of the means provided by this pallet."]}, {"name": "AutoLimits", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 385}, "fallback": "0x00", "docs": [" The limits that are imposed on automatic migrations.", "", " If set to None, then no automatic migration happens."]}, {"name": "SignedMigrationMaxLimits", "modifier": "Optional", "type": {"plain": 386}, "fallback": "0x00", "docs": [" The maximum limits that the signed migration could use.", "", " If not set, no signed submission is allowed."]}]}, "calls": {"type": 384}, "events": {"type": 135}, "constants": [{"name": "MaxKeyLen", "type": 4, "value": "0x00020000", "docs": [" Maximal number of bytes that a key can have.", "", " FRAME itself does not limit the key length.", " The concrete value must therefore depend on your storage usage.", " A [`frame_support::storage::StorageNMap`] for example can have an arbitrary number of", " keys which are then hashed and concatenated, resulting in arbitrarily long keys.", "", " Use the *state migration RPC* to retrieve the length of the longest key in your", " storage: <https://github.com/paritytech/substrate/issues/11642>", "", " The migration will halt with a `Halted` event if this value is too small.", " Since there is no real penalty from over-estimating, it is advised to use a large", " value. The default is 512 byte.", "", " Some key lengths for reference:", " - [`frame_support::storage::StorageValue`]: 32 byte", " - [`frame_support::storage::StorageMap`]: 64 byte", " - [`frame_support::storage::StorageDoubleMap`]: 96 byte", "", " For more info see", " <https://www.shawntabrizi.com/blog/substrate/querying-substrate-storage-via-rpc/>"]}], "errors": {"type": 137}, "index": 53}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "storage": {"prefix": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "items": [{"name": "ChildBountyCount", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 4}, "fallback": "0x00000000", "docs": [" Number of total child bounties."]}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "modifier": "<PERSON><PERSON><PERSON>", "type": {"map": {"hashers": ["Twox64Concat"], "key": 4, "value": 4}}, "fallback": "0x00000000", "docs": [" Number of child bounties per parent bounty.", " Map of parent bounty index to number of child bounties."]}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "modifier": "Optional", "type": {"map": {"hashers": ["Twox64Concat", "Twox64Concat"], "key": 94, "value": 829}}, "fallback": "0x00", "docs": [" Child bounties that have been added."]}, {"name": "ChildBountyDescriptions", "modifier": "Optional", "type": {"map": {"hashers": ["Twox64Concat"], "key": 4, "value": 749}}, "fallback": "0x00", "docs": [" The description of each child-bounty."]}, {"name": "ChildrenCuratorFees", "modifier": "<PERSON><PERSON><PERSON>", "type": {"map": {"hashers": ["Twox64Concat"], "key": 4, "value": 6}}, "fallback": "0x******************************00", "docs": [" The cumulative child-bounty curator fee for each parent bounty."]}]}, "calls": {"type": 390}, "events": {"type": 138}, "constants": [{"name": "MaxActiveChildBountyCount", "type": 4, "value": "0x05000000", "docs": [" Maximum number of child bounties that can be added to a parent bounty."]}, {"name": "ChildBountyValueMinimum", "type": 6, "value": "0x00407a10f35a***************00000", "docs": [" Minimum value for a child-bounty."]}], "errors": {"type": 831}, "index": 54}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "storage": {"prefix": "<PERSON><PERSON><PERSON><PERSON>", "items": [{"name": "ReferendumCount", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 4}, "fallback": "0x00000000", "docs": [" The next free referendum index, aka the number of referenda started so far."]}, {"name": "ReferendumInfoFor", "modifier": "Optional", "type": {"map": {"hashers": ["Blake2_128Concat"], "key": 4, "value": 832}}, "fallback": "0x00", "docs": [" Information concerning any given referendum."]}, {"name": "TrackQueue", "modifier": "<PERSON><PERSON><PERSON>", "type": {"map": {"hashers": ["Twox64Concat"], "key": 101, "value": 840}}, "fallback": "0x00", "docs": [" The sorted list of referenda ready to be decided but not yet being decided, ordered by", " conviction-weighted approvals.", "", " This should be empty if `DecidingCount` is less than `TrackInfo::max_deciding`."]}, {"name": "DecidingCount", "modifier": "<PERSON><PERSON><PERSON>", "type": {"map": {"hashers": ["Twox64Concat"], "key": 101, "value": 4}}, "fallback": "0x00000000", "docs": [" The number of referenda being decided currently."]}, {"name": "MetadataOf", "modifier": "Optional", "type": {"map": {"hashers": ["Blake2_128Concat"], "key": 4, "value": 13}}, "fallback": "0x00", "docs": [" The metadata is a general information concerning the referendum.", " The `Hash` refers to the preimage of the `Preimages` provider which can be a JSON", " dump or IPFS hash of a JSON file.", "", " Consider a garbage collection for a metadata of finished referendums to `unrequest` (remove)", " large preimages."]}]}, "calls": {"type": 391}, "events": {"type": 139}, "constants": [{"name": "SubmissionDeposit", "type": 6, "value": "0x0000c16ff28623***************000", "docs": [" The minimum amount to be used as a deposit for a public referendum proposal."]}, {"name": "MaxQueued", "type": 4, "value": "0x64000000", "docs": [" Maximum size of the referendum queue for a single track."]}, {"name": "UndecidingTimeout", "type": 4, "value": "0x004e0c00", "docs": [" The number of blocks after submission that a referendum must begin being decided by.", " Once this passes, then anyone may cancel the referendum."]}, {"name": "AlarmInterval", "type": 4, "value": "0x01000000", "docs": [" Quantization level for the referendum wakeup scheduler. A higher number will result in", " fewer storage reads/writes needed for smaller voters, but also result in delays to the", " automatic referendum status changes. Explicit servicing instructions are unaffected."]}, {"name": "Tracks", "type": 841, "value": "0x04000010726f6f74010000000a******************************040000000400000002000000040000000000ca9a3b0065cd1d00ca9a3b0000ca9a3b0000000000ca9a3b", "docs": [" Information concerning the different referendum tracks."]}], "errors": {"type": 847}, "index": 55}, {"name": "Remark", "storage": null, "calls": {"type": 393}, "events": {"type": 467}, "constants": [], "errors": {"type": 848}, "index": 56}, {"name": "RootTesting", "storage": null, "calls": {"type": 394}, "events": {"type": 468}, "constants": [], "errors": null, "index": 57}, {"name": "ConvictionVoting", "storage": {"prefix": "ConvictionVoting", "items": [{"name": "VotingFor", "modifier": "<PERSON><PERSON><PERSON>", "type": {"map": {"hashers": ["Twox64Concat", "Twox64Concat"], "key": 849, "value": 850}}, "fallback": "0x*********************************************************************************************************000", "docs": [" All voting for a particular voter in a particular voting class. We store the balance for the", " number of votes that we have recorded."]}, {"name": "ClassLocksFor", "modifier": "<PERSON><PERSON><PERSON>", "type": {"map": {"hashers": ["Twox64Concat"], "key": 0, "value": 858}}, "fallback": "0x00", "docs": [" The voting classes which have a non-zero lock requirement and the lock amounts which they", " require. The actual amount locked on behalf of this pallet should always be the maximum of", " this list."]}]}, "calls": {"type": 395}, "events": {"type": 469}, "constants": [{"name": "MaxVotes", "type": 4, "value": "0x00020000", "docs": [" The maximum number of concurrent votes an account may have.", "", " Also used to compute weight, an overly large value can lead to extrinsics with large", " weight estimation: see `delegate` for instance."]}, {"name": "VoteLockingPeriod", "type": 4, "value": "0x002f0d00", "docs": [" The minimum period of vote locking.", "", " It should be no shorter than enactment period to ensure that in the case of an approval,", " those successful voters are locked into the consequences that their votes entail."]}], "errors": {"type": 861}, "index": 58}, {"name": "Whitelist", "storage": {"prefix": "Whitelist", "items": [{"name": "WhitelistedCall", "modifier": "Optional", "type": {"map": {"hashers": ["Twox64Concat"], "key": 13, "value": 33}}, "fallback": "0x00", "docs": []}]}, "calls": {"type": 399}, "events": {"type": 470}, "constants": [], "errors": {"type": 862}, "index": 59}, {"name": "AllianceMotion", "storage": {"prefix": "AllianceMotion", "items": [{"name": "Proposals", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 863}, "fallback": "0x00", "docs": [" The hashes of the active proposals."]}, {"name": "ProposalOf", "modifier": "Optional", "type": {"map": {"hashers": ["Identity"], "key": 13, "value": 141}}, "fallback": "0x00", "docs": [" Actual proposal for a given hash, if it's current."]}, {"name": "Voting", "modifier": "Optional", "type": {"map": {"hashers": ["Identity"], "key": 13, "value": 626}}, "fallback": "0x00", "docs": [" Votes on a given proposal, if it is ongoing."]}, {"name": "ProposalCount", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 4}, "fallback": "0x00000000", "docs": [" Proposals so far."]}, {"name": "Members", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 89}, "fallback": "0x00", "docs": [" The current members of the collective. This is stored sorted (just by value)."]}, {"name": "Prime", "modifier": "Optional", "type": {"plain": 0}, "fallback": "0x00", "docs": [" The prime member that helps determine the default vote behavior in case of abstentions."]}]}, "calls": {"type": 400}, "events": {"type": 475}, "constants": [{"name": "MaxProposalWeight", "type": 10, "value": "0x070010a5d4e813ffffffffffffff7f", "docs": [" The maximum weight of a dispatch call that can be proposed and executed."]}], "errors": {"type": 864}, "index": 60}, {"name": "Alliance", "storage": {"prefix": "Alliance", "items": [{"name": "Rule", "modifier": "Optional", "type": {"plain": 403}, "fallback": "0x00", "docs": [" The IPFS CID of the alliance rule.", " Fellows can propose a new rule with a super-majority."]}, {"name": "Announcements", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 865}, "fallback": "0x00", "docs": [" The current IPFS CIDs of any announcements."]}, {"name": "DepositOf", "modifier": "Optional", "type": {"map": {"hashers": ["Blake2_128Concat"], "key": 0, "value": 6}}, "fallback": "0x00", "docs": [" Maps members to their candidacy deposit."]}, {"name": "Members", "modifier": "<PERSON><PERSON><PERSON>", "type": {"map": {"hashers": ["Twox64Concat"], "key": 867, "value": 868}}, "fallback": "0x00", "docs": [" Maps member type to members of each type."]}, {"name": "RetiringMembers", "modifier": "Optional", "type": {"map": {"hashers": ["Blake2_128Concat"], "key": 0, "value": 4}}, "fallback": "0x00", "docs": [" A set of members who gave a retirement notice. They can retire after the end of retirement", " period stored as a future block number."]}, {"name": "UnscrupulousAccounts", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 612}, "fallback": "0x00", "docs": [" The current list of accounts deemed unscrupulous. These accounts non grata cannot submit", " candidacy."]}, {"name": "UnscrupulousWebsites", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 869}, "fallback": "0x00", "docs": [" The current list of websites deemed unscrupulous."]}]}, "calls": {"type": 401}, "events": {"type": 476}, "constants": [{"name": "MaxUnscrupulousItems", "type": 4, "value": "0x64000000", "docs": [" The maximum number of the unscrupulous items supported by the pallet."]}, {"name": "MaxWebsiteUrlLength", "type": 4, "value": "0xff000000", "docs": [" The maximum length of a website URL."]}, {"name": "AllyDeposit", "type": 6, "value": "0x0080c6a47e8d03***************000", "docs": [" The deposit required for submitting candidacy."]}, {"name": "MaxAnnouncementsCount", "type": 4, "value": "0x64000000", "docs": [" The maximum number of announcements."]}, {"name": "MaxMembersCount", "type": 4, "value": "0x64000000", "docs": [" The maximum number of members per member role."]}], "errors": {"type": 871}, "index": 61}, {"name": "NominationPools", "storage": {"prefix": "NominationPools", "items": [{"name": "TotalValueLocked", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 6}, "fallback": "0x******************************00", "docs": [" The sum of funds across all pools.", "", " This might be lower but never higher than the sum of `total_balance` of all [`PoolMembers`]", " because calling `pool_withdraw_unbonded` might decrease the total stake of the pool's", " `bonded_account` without adjusting the pallet-internal `UnbondingPool`'s."]}, {"name": "MinJoinBond", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 6}, "fallback": "0x******************************00", "docs": [" Minimum amount to bond to join a pool."]}, {"name": "MinCreateBond", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 6}, "fallback": "0x******************************00", "docs": [" Minimum bond required to create a pool.", "", " This is the amount that the depositor must put as their initial stake in the pool, as an", " indication of \"skin in the game\".", "", " This is the value that will always exist in the staking ledger of the pool bonded account", " while all other accounts leave."]}, {"name": "MaxPools", "modifier": "Optional", "type": {"plain": 4}, "fallback": "0x00", "docs": [" Maximum number of nomination pools that can exist. If `None`, then an unbounded number of", " pools can exist."]}, {"name": "MaxPoolMembers", "modifier": "Optional", "type": {"plain": 4}, "fallback": "0x00", "docs": [" Maximum number of members that can exist in the system. If `None`, then the count", " members are not bound on a system wide basis."]}, {"name": "MaxPoolMembersPerPool", "modifier": "Optional", "type": {"plain": 4}, "fallback": "0x00", "docs": [" Maximum number of members that may belong to pool. If `None`, then the count of", " members is not bound on a per pool basis."]}, {"name": "GlobalMaxCommission", "modifier": "Optional", "type": {"plain": 49}, "fallback": "0x00", "docs": [" The maximum commission that can be charged by a pool. Used on commission payouts to bound", " pool commissions that are > `GlobalMaxCommission`, necessary if a future", " `GlobalMaxCommission` is lower than some current pool commissions."]}, {"name": "PoolMembers", "modifier": "Optional", "type": {"map": {"hashers": ["Twox64Concat"], "key": 0, "value": 872}}, "fallback": "0x00", "docs": [" Active members.", "", " TWOX-NOTE: SAFE since `AccountId` is a secure hash."]}, {"name": "CounterForPoolMembers", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 4}, "fallback": "0x00000000", "docs": ["Counter for the related counted storage map"]}, {"name": "BondedPools", "modifier": "Optional", "type": {"map": {"hashers": ["Twox64Concat"], "key": 4, "value": 875}}, "fallback": "0x00", "docs": [" Storage for bonded pools."]}, {"name": "CounterForBondedPools", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 4}, "fallback": "0x00000000", "docs": ["Counter for the related counted storage map"]}, {"name": "RewardPools", "modifier": "Optional", "type": {"map": {"hashers": ["Twox64Concat"], "key": 4, "value": 880}}, "fallback": "0x00", "docs": [" Reward pools. This is where there rewards for each pool accumulate. When a members payout is", " claimed, the balance comes out of the reward pool. Keyed by the bonded pools account."]}, {"name": "CounterForRewardPools", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 4}, "fallback": "0x00000000", "docs": ["Counter for the related counted storage map"]}, {"name": "SubPoolsStorage", "modifier": "Optional", "type": {"map": {"hashers": ["Twox64Concat"], "key": 4, "value": 881}}, "fallback": "0x00", "docs": [" Groups of unbonding pools. Each group of unbonding pools belongs to a", " bonded pool, hence the name sub-pools. Keyed by the bonded pools account."]}, {"name": "CounterForSubPoolsStorage", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 4}, "fallback": "0x00000000", "docs": ["Counter for the related counted storage map"]}, {"name": "<PERSON><PERSON><PERSON>", "modifier": "<PERSON><PERSON><PERSON>", "type": {"map": {"hashers": ["Twox64Concat"], "key": 4, "value": 117}}, "fallback": "0x00", "docs": [" <PERSON><PERSON><PERSON> for the pool."]}, {"name": "CounterForMetadata", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 4}, "fallback": "0x00000000", "docs": ["Counter for the related counted storage map"]}, {"name": "LastPoolId", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 4}, "fallback": "0x00000000", "docs": [" Ever increasing number of all pools created so far."]}, {"name": "ReversePoolIdLookup", "modifier": "Optional", "type": {"map": {"hashers": ["Twox64Concat"], "key": 0, "value": 4}}, "fallback": "0x00", "docs": [" A reverse lookup from the pool's account id to its id.", "", " This is only used for slashing and on automatic withdraw update. In all other instances, the", " pool id is used, and the accounts are deterministically derived from it."]}, {"name": "CounterForReversePoolIdLookup", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 4}, "fallback": "0x00000000", "docs": ["Counter for the related counted storage map"]}, {"name": "ClaimPermissions", "modifier": "<PERSON><PERSON><PERSON>", "type": {"map": {"hashers": ["Twox64Concat"], "key": 0, "value": 417}}, "fallback": "0x02", "docs": [" Map from a pool member account to their opted claim permission."]}]}, "calls": {"type": 410}, "events": {"type": 477}, "constants": [{"name": "PalletId", "type": 644, "value": "0x70792f6e6f706c73", "docs": [" The nomination pool's pallet id."]}, {"name": "MaxPointsToBalance", "type": 2, "value": "0x0a", "docs": [" The maximum pool points-to-balance ratio that an `open` pool can have.", "", " This is important in the event slashing takes place and the pool's points-to-balance", " ratio becomes disproportional.", "", " Moreover, this relates to the `RewardCounter` type as well, as the arithmetic operations", " are a function of number of points, and by setting this value to e.g. 10, you ensure", " that the total number of points in the system are at most 10 times the total_issuance of", " the chain, in the absolute worse case.", "", " For a value of 10, the threshold would be a pool points-to-balance ratio of 10:1.", " Such a scenario would also be the equivalent of the pool being 90% slashed."]}, {"name": "MaxUnbonding", "type": 4, "value": "0x08000000", "docs": [" The maximum number of simultaneous unbonding chunks that can exist per member."]}], "errors": {"type": 887}, "index": 62}, {"name": "RankedPolls", "storage": {"prefix": "RankedPolls", "items": [{"name": "ReferendumCount", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 4}, "fallback": "0x00000000", "docs": [" The next free referendum index, aka the number of referenda started so far."]}, {"name": "ReferendumInfoFor", "modifier": "Optional", "type": {"map": {"hashers": ["Blake2_128Concat"], "key": 4, "value": 889}}, "fallback": "0x00", "docs": [" Information concerning any given referendum."]}, {"name": "TrackQueue", "modifier": "<PERSON><PERSON><PERSON>", "type": {"map": {"hashers": ["Twox64Concat"], "key": 101, "value": 891}}, "fallback": "0x00", "docs": [" The sorted list of referenda ready to be decided but not yet being decided, ordered by", " conviction-weighted approvals.", "", " This should be empty if `DecidingCount` is less than `TrackInfo::max_deciding`."]}, {"name": "DecidingCount", "modifier": "<PERSON><PERSON><PERSON>", "type": {"map": {"hashers": ["Twox64Concat"], "key": 101, "value": 4}}, "fallback": "0x00000000", "docs": [" The number of referenda being decided currently."]}, {"name": "MetadataOf", "modifier": "Optional", "type": {"map": {"hashers": ["Blake2_128Concat"], "key": 4, "value": 13}}, "fallback": "0x00", "docs": [" The metadata is a general information concerning the referendum.", " The `Hash` refers to the preimage of the `Preimages` provider which can be a JSON", " dump or IPFS hash of a JSON file.", "", " Consider a garbage collection for a metadata of finished referendums to `unrequest` (remove)", " large preimages."]}]}, "calls": {"type": 423}, "events": {"type": 478}, "constants": [{"name": "SubmissionDeposit", "type": 6, "value": "0x0000c16ff28623***************000", "docs": [" The minimum amount to be used as a deposit for a public referendum proposal."]}, {"name": "MaxQueued", "type": 4, "value": "0x64000000", "docs": [" Maximum size of the referendum queue for a single track."]}, {"name": "UndecidingTimeout", "type": 4, "value": "0x004e0c00", "docs": [" The number of blocks after submission that a referendum must begin being decided by.", " Once this passes, then anyone may cancel the referendum."]}, {"name": "AlarmInterval", "type": 4, "value": "0x01000000", "docs": [" Quantization level for the referendum wakeup scheduler. A higher number will result in", " fewer storage reads/writes needed for smaller voters, but also result in delays to the", " automatic referendum status changes. Explicit servicing instructions are unaffected."]}, {"name": "Tracks", "type": 841, "value": "0x04000010726f6f74010000000a******************************040000000400000002000000040000000000ca9a3b0065cd1d00ca9a3b0000ca9a3b0000000000ca9a3b", "docs": [" Information concerning the different referendum tracks."]}], "errors": {"type": 892}, "index": 63}, {"name": "RankedCollective", "storage": {"prefix": "RankedCollective", "items": [{"name": "MemberCount", "modifier": "<PERSON><PERSON><PERSON>", "type": {"map": {"hashers": ["Twox64Concat"], "key": 101, "value": 4}}, "fallback": "0x00000000", "docs": [" The number of members in the collective who have at least the rank according to the index", " of the vec."]}, {"name": "Members", "modifier": "Optional", "type": {"map": {"hashers": ["Twox64Concat"], "key": 0, "value": 893}}, "fallback": "0x00", "docs": [" The current members of the collective."]}, {"name": "IdToIndex", "modifier": "Optional", "type": {"map": {"hashers": ["Twox64Concat", "Twox64Concat"], "key": 894, "value": 4}}, "fallback": "0x00", "docs": [" The index of each ranks's member into the group of members who have at least that rank."]}, {"name": "IndexToId", "modifier": "Optional", "type": {"map": {"hashers": ["Twox64Concat", "Twox64Concat"], "key": 895, "value": 0}}, "fallback": "0x00", "docs": [" The members in the collective by index. All indices in the range `0..MemberCount` will", " return `Some`, however a member's index is not guaranteed to remain unchanged over time."]}, {"name": "Voting", "modifier": "Optional", "type": {"map": {"hashers": ["Blake2_128Concat", "Twox64Concat"], "key": 589, "value": 481}}, "fallback": "0x00", "docs": [" Votes on a given proposal, if it is ongoing."]}, {"name": "VotingCleanup", "modifier": "Optional", "type": {"map": {"hashers": ["Blake2_128Concat"], "key": 4, "value": 896}}, "fallback": "0x00", "docs": []}]}, "calls": {"type": 424}, "events": {"type": 480}, "constants": [], "errors": {"type": 897}, "index": 64}, {"name": "AssetConversion", "storage": {"prefix": "AssetConversion", "items": [{"name": "Pools", "modifier": "Optional", "type": {"map": {"hashers": ["Blake2_128Concat"], "key": 483, "value": 898}}, "fallback": "0x00", "docs": [" Map from `PoolAssetId` to `PoolInfo`. This establishes whether a pool has been officially", " created rather than people sending tokens directly to a pool's public account."]}, {"name": "NextPoolAssetId", "modifier": "Optional", "type": {"plain": 4}, "fallback": "0x00", "docs": [" Stores the `PoolAssetId` that is going to be used for the next lp token.", " This gets incremented whenever a new lp pool is created."]}]}, "calls": {"type": 425}, "events": {"type": 482}, "constants": [{"name": "LPFee", "type": 4, "value": "0x03000000", "docs": [" A % the liquidity providers will take of every swap. Represents 10ths of a percent."]}, {"name": "PoolSetupFee", "type": 6, "value": "0x00407a10f35a***************00000", "docs": [" A one-time fee to setup the pool."]}, {"name": "PoolSetupFeeAsset", "type": 426, "value": "0x00", "docs": [" Asset class from [`Config::Assets`] used to pay the [`Config::PoolSetupFee`]."]}, {"name": "LiquidityWithdrawalFee", "type": 484, "value": "0x00000000", "docs": [" A fee to withdraw the liquidity."]}, {"name": "MintMinLiquidity", "type": 6, "value": "0x64******************************", "docs": [" The minimum LP token amount that could be minted. Ameliorates rounding errors."]}, {"name": "MaxSwapPath<PERSON><PERSON>th", "type": 4, "value": "0x04000000", "docs": [" The max number of hops in a swap."]}, {"name": "PalletId", "type": 644, "value": "0x70792f6173636f6e", "docs": [" The pallet's id, used for deriving its sovereign account ID."]}], "errors": {"type": 899}, "index": 65}, {"name": "FastUnstake", "storage": {"prefix": "FastUnstake", "items": [{"name": "Head", "modifier": "Optional", "type": {"plain": 900}, "fallback": "0x00", "docs": [" The current \"head of the queue\" being unstaked.", "", " The head in itself can be a batch of up to [`Config::BatchSize`] stakers."]}, {"name": "Queue", "modifier": "Optional", "type": {"map": {"hashers": ["Twox64Concat"], "key": 0, "value": 6}}, "fallback": "0x00", "docs": [" The map of all accounts wishing to be unstaked.", "", " Keeps track of `AccountId` wishing to unstake and it's corresponding deposit."]}, {"name": "CounterForQueue", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 4}, "fallback": "0x00000000", "docs": ["Counter for the related counted storage map"]}, {"name": "ErasToCheckPerBlock", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 4}, "fallback": "0x00000000", "docs": [" Number of eras to check per block.", "", " If set to 0, this pallet does absolutely nothing. Cannot be set to more than", " [`Config::<PERSON><PERSON><PERSON><PERSON>oCheckPerBlock`].", "", " Based on the amount of weight available at [`Pallet::on_idle`], up to this many eras are", " checked. The checking is represented by updating [`UnstakeRequest::checked`], which is", " stored in [`Head`]."]}]}, "calls": {"type": 428}, "events": {"type": 487}, "constants": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "type": 6, "value": "0x00407a10f35a***************00000", "docs": [" Deposit to take for unstaking, to make sure we're able to slash the it in order to cover", " the costs of resources on unsuccessful unstake."]}], "errors": {"type": 903}, "index": 66}, {"name": "MessageQueue", "storage": {"prefix": "MessageQueue", "items": [{"name": "BookStateFor", "modifier": "<PERSON><PERSON><PERSON>", "type": {"map": {"hashers": ["Twox64Concat"], "key": 4, "value": 904}}, "fallback": "0x*********************************************0000000000000", "docs": [" The index of the first and last (non-empty) pages."]}, {"name": "ServiceHead", "modifier": "Optional", "type": {"plain": 4}, "fallback": "0x00", "docs": [" The origin at which we should begin servicing."]}, {"name": "Pages", "modifier": "Optional", "type": {"map": {"hashers": ["Twox64Concat", "Twox64Concat"], "key": 94, "value": 907}}, "fallback": "0x00", "docs": [" The map of page indices to pages."]}]}, "calls": {"type": 429}, "events": {"type": 488}, "constants": [{"name": "HeapSize", "type": 4, "value": "0x00000100", "docs": [" The size of the page; this implies the maximum message size which can be sent.", "", " A good value depends on the expected message sizes, their weights, the weight that is", " available for processing them and the maximal needed message size. The maximal message", " size is slightly lower than this as defined by [`MaxMessageLenOf`]."]}, {"name": "MaxStale", "type": 4, "value": "0x80000000", "docs": [" The maximum number of stale pages (i.e. of overweight messages) allowed before culling", " can happen. Once there are more stale pages than this, then historical pages may be", " dropped, even if they contain unprocessed overweight messages."]}, {"name": "ServiceWeight", "type": 473, "value": "0x010700a0db215d133333333333333333", "docs": [" The amount of weight (if any) which should be provided to the message queue for", " servicing enqueued items `on_initialize`.", "", " This may be legitimately `None` in the case that you will call", " `ServiceQueues::service_queues` manually or set [`Self::IdleMaxServiceWeight`] to have", " it run in `on_idle`."]}, {"name": "IdleMaxServiceWeight", "type": 473, "value": "0x00", "docs": [" The maximum amount of weight (if any) to be used from remaining weight `on_idle` which", " should be provided to the message queue for servicing enqueued items `on_idle`.", " Useful for parachains to process messages at the same block they are received.", "", " If `None`, it will not call `ServiceQueues::service_queues` in `on_idle`."]}], "errors": {"type": 909}, "index": 67}, {"name": "<PERSON><PERSON>", "storage": {"prefix": "<PERSON><PERSON>", "items": [{"name": "Value", "modifier": "Optional", "type": {"plain": 4}, "fallback": "0x00", "docs": []}, {"name": "Value2", "modifier": "Optional", "type": {"plain": 4}, "fallback": "0x00", "docs": []}, {"name": "UnboundedValue", "modifier": "Optional", "type": {"plain": 14}, "fallback": "0x00", "docs": [" A value without a MEL bound."]}, {"name": "BoundedValue", "modifier": "Optional", "type": {"plain": 87}, "fallback": "0x00", "docs": [" A value with a MEL bound of 32 byte."]}, {"name": "LargeValue", "modifier": "Optional", "type": {"plain": 733}, "fallback": "0x00", "docs": [" 4MiB value."]}, {"name": "LargeValue2", "modifier": "Optional", "type": {"plain": 733}, "fallback": "0x00", "docs": []}, {"name": "Map1M", "modifier": "Optional", "type": {"map": {"hashers": ["Blake2_256"], "key": 4, "value": 4}}, "fallback": "0x00", "docs": [" A map with a maximum of 1M entries."]}, {"name": "Map16M", "modifier": "Optional", "type": {"map": {"hashers": ["Blake2_256"], "key": 4, "value": 4}}, "fallback": "0x00", "docs": [" A map with a maximum of 16M entries."]}, {"name": "DoubleMap1M", "modifier": "Optional", "type": {"map": {"hashers": ["Blake2_256", "Blake2_256"], "key": 94, "value": 4}}, "fallback": "0x00", "docs": []}, {"name": "UnboundedMap", "modifier": "Optional", "type": {"map": {"hashers": ["Blake2_256"], "key": 4, "value": 114}}, "fallback": "0x00", "docs": []}, {"name": "UnboundedMap2", "modifier": "Optional", "type": {"map": {"hashers": ["Blake2_256"], "key": 4, "value": 114}}, "fallback": "0x00", "docs": []}, {"name": "UnboundedMapTwox", "modifier": "Optional", "type": {"map": {"hashers": ["Twox64Concat"], "key": 4, "value": 114}}, "fallback": "0x00", "docs": []}]}, "calls": {"type": 430}, "events": {"type": 490}, "constants": [], "errors": null, "index": 68}, {"name": "TxPause", "storage": {"prefix": "TxPause", "items": [{"name": "PausedCalls", "modifier": "Optional", "type": {"map": {"hashers": ["Blake2_128Concat"], "key": 432, "value": 33}}, "fallback": "0x00", "docs": [" The set of calls that are explicitly paused."]}]}, "calls": {"type": 431}, "events": {"type": 491}, "constants": [{"name": "MaxNameLen", "type": 4, "value": "0x00010000", "docs": [" Maximum length for pallet name and call name SCALE encoded string names.", "", " TOO LONG NAMES WILL BE TREATED AS PAUSED."]}], "errors": {"type": 910}, "index": 69}, {"name": "SafeMode", "storage": {"prefix": "SafeMode", "items": [{"name": "EnteredUntil", "modifier": "Optional", "type": {"plain": 4}, "fallback": "0x00", "docs": [" Contains the last block number that the safe-mode will remain entered in.", "", "  Set to `None` when safe-mode is exited.", "", " Safe-mode is automatically exited when the current block number exceeds this value."]}, {"name": "Deposits", "modifier": "Optional", "type": {"map": {"hashers": ["Twox64Concat", "Twox64Concat"], "key": 596, "value": 6}}, "fallback": "0x00", "docs": [" Holds the reserve that was taken from an account at a specific block number.", "", " This helps governance to have an overview of outstanding deposits that should be returned or", " slashed."]}]}, "calls": {"type": 433}, "events": {"type": 492}, "constants": [{"name": "EnterDuration", "type": 4, "value": "0xc0120000", "docs": [" For how many blocks the safe-mode will be entered by [`Pallet::enter`]."]}, {"name": "ExtendDuration", "type": 4, "value": "0x60090000", "docs": [" For how many blocks the safe-mode can be extended by each [`Pallet::extend`] call.", "", " This does not impose a hard limit as the safe-mode can be extended multiple times."]}, {"name": "EnterDepositAmount", "type": 236, "value": "0x01000020c65abc8ed70a00000000000000", "docs": [" The amount that will be reserved upon calling [`Pallet::enter`].", "", " `None` disallows permissionlessly enabling the safe-mode and is a sane default."]}, {"name": "ExtendDepositAmount", "type": 236, "value": "0x01000010632d5ec76b0500000000000000", "docs": [" The amount that will be reserved upon calling [`Pallet::extend`].", "", " `None` disallows permissionlessly extending the safe-mode and is a sane default."]}, {"name": "ReleaseDelay", "type": 39, "value": "0x0100e10000", "docs": [" The minimal duration a deposit will remain reserved after safe-mode is entered or", " extended, unless [`Pallet::force_release_deposit`] is successfully called sooner.", "", " Every deposit is tied to a specific activation or extension, thus each deposit can be", " released independently after the delay for it has passed.", "", " `None` disallows permissionlessly releasing the safe-mode deposits and is a sane", " default."]}], "errors": {"type": 911}, "index": 70}, {"name": "Statement", "storage": null, "calls": null, "events": {"type": 494}, "constants": [{"name": "StatementCost", "type": 6, "value": "0x00407a10f35a***************00000", "docs": [" Min balance for priority statements."]}, {"name": "ByteCost", "type": 6, "value": "0x00e8764817***************0000000", "docs": [" Cost of data byte used for priority calculation."]}, {"name": "MinAllowedStatements", "type": 4, "value": "0x04000000", "docs": [" Minimum number of statements allowed per account."]}, {"name": "MaxAllowedStatements", "type": 4, "value": "0x0a000000", "docs": [" Maximum number of statements allowed per account."]}, {"name": "MinAllowedBytes", "type": 4, "value": "0x00040000", "docs": [" Minimum data bytes allowed per account."]}, {"name": "MaxAllowedBytes", "type": 4, "value": "0x00100000", "docs": [" Maximum data bytes allowed per account."]}], "errors": null, "index": 71}, {"name": "MultiBlockMigrations", "storage": {"prefix": "MultiBlockMigrations", "items": [{"name": "<PERSON><PERSON><PERSON>", "modifier": "Optional", "type": {"plain": 436}, "fallback": "0x00", "docs": [" The currently active migration to run and its cursor.", "", " `None` indicates that no migration is running."]}, {"name": "Historic", "modifier": "Optional", "type": {"map": {"hashers": ["Twox64Concat"], "key": 117, "value": 33}}, "fallback": "0x00", "docs": [" Set of all successfully executed migrations.", "", " This is used as blacklist, to not re-execute migrations that have not been removed from the", " codebase yet. Governance can regularly clear this out via `clear_historic`."]}]}, "calls": {"type": 434}, "events": {"type": 499}, "constants": [{"name": "CursorMaxLen", "type": 4, "value": "0x00000100", "docs": [" The maximal length of an encoded cursor.", "", " A good default needs to selected such that no migration will ever have a cursor with MEL", " above this limit. This is statically checked in `integrity_test`."]}, {"name": "IdentifierMaxLen", "type": 4, "value": "0x00010000", "docs": [" The maximal length of an encoded identifier.", "", " A good default needs to selected such that no migration will ever have an identifier", " with MEL above this limit. This is statically checked in `integrity_test`."]}], "errors": {"type": 912}, "index": 72}, {"name": "Broker", "storage": {"prefix": "Broker", "items": [{"name": "Configuration", "modifier": "Optional", "type": {"plain": 444}, "fallback": "0x00", "docs": [" The current configuration of this pallet."]}, {"name": "Reservations", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 913}, "fallback": "0x00", "docs": [" The Polkadot Core reservations (generally tasked with the maintenance of System Chains)."]}, {"name": "Leases", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 915}, "fallback": "0x00", "docs": [" The Polkadot Core legacy leases."]}, {"name": "Status", "modifier": "Optional", "type": {"plain": 918}, "fallback": "0x00", "docs": [" The current status of miscellaneous subsystems of this pallet."]}, {"name": "SaleInfo", "modifier": "Optional", "type": {"plain": 919}, "fallback": "0x00", "docs": [" The details of the current sale, including its properties and status."]}, {"name": "PotentialRenewals", "modifier": "Optional", "type": {"map": {"hashers": ["Twox64Concat"], "key": 920, "value": 921}}, "fallback": "0x00", "docs": [" Records of potential renewals.", "", " Renewals will only actually be allowed if `CompletionStatus` is actually `Complete`."]}, {"name": "Regions", "modifier": "Optional", "type": {"map": {"hashers": ["Blake2_128Concat"], "key": 450, "value": 923}}, "fallback": "0x00", "docs": [" The current (unassigned or provisionally assigend) Regions."]}, {"name": "Workplan", "modifier": "Optional", "type": {"map": {"hashers": ["Twox64Concat"], "key": 924, "value": 445}}, "fallback": "0x00", "docs": [" The work we plan on having each core do at a particular time in the future."]}, {"name": "Workload", "modifier": "<PERSON><PERSON><PERSON>", "type": {"map": {"hashers": ["Twox64Concat"], "key": 101, "value": 445}}, "fallback": "0x00", "docs": [" The current workload of each core. This gets updated with workplan as timeslices pass."]}, {"name": "InstaPoolContribution", "modifier": "Optional", "type": {"map": {"hashers": ["Blake2_128Concat"], "key": 450, "value": 925}}, "fallback": "0x00", "docs": [" Record of a single contribution to the Instantaneous Coretime Pool."]}, {"name": "InstaPoolIo", "modifier": "<PERSON><PERSON><PERSON>", "type": {"map": {"hashers": ["Blake2_128Concat"], "key": 4, "value": 926}}, "fallback": "0x***************0", "docs": [" Record of Coretime entering or leaving the Instantaneous Coretime Pool."]}, {"name": "InstaPoolHistory", "modifier": "Optional", "type": {"map": {"hashers": ["Blake2_128Concat"], "key": 4, "value": 928}}, "fallback": "0x00", "docs": [" Total InstaPool rewards for each Timeslice and the number of core parts which contributed."]}, {"name": "CoreCountInbox", "modifier": "Optional", "type": {"plain": 101}, "fallback": "0x00", "docs": [" Received core count change from the relay chain."]}, {"name": "RevenueInbox", "modifier": "Optional", "type": {"plain": 452}, "fallback": "0x00", "docs": [" Received revenue info from the relay chain."]}]}, "calls": {"type": 443}, "events": {"type": 500}, "constants": [{"name": "PalletId", "type": 644, "value": "0x70792f62726f6b65", "docs": [" Identifier from which the internal Pot is generated."]}, {"name": "TimeslicePeriod", "type": 4, "value": "0x02000000", "docs": [" Number of Relay-chain blocks per timeslice."]}, {"name": "MaxLeasedCores", "type": 4, "value": "0x05000000", "docs": [" Maximum number of legacy leases."]}, {"name": "MaxReservedCores", "type": 4, "value": "0x05000000", "docs": [" Maximum number of system cores."]}], "errors": {"type": 929}, "index": 73}, {"name": "TasksExample", "storage": {"prefix": "TasksExample", "items": [{"name": "Total", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 94}, "fallback": "0x***************0", "docs": [" Some running total."]}, {"name": "Numbers", "modifier": "Optional", "type": {"map": {"hashers": ["Twox64Concat"], "key": 4, "value": 4}}, "fallback": "0x00", "docs": [" Numbers to be added into the total."]}]}, "calls": null, "events": null, "constants": [], "errors": {"type": 930}, "index": 74}, {"name": "Mixnet", "storage": {"prefix": "Mixnet", "items": [{"name": "CurrentSessionIndex", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 4}, "fallback": "0x00000000", "docs": [" Index of the current session. This may be offset relative to the session index tracked by", " eg `pallet_session`; mixnet session indices are independent."]}, {"name": "CurrentSessionStartBlock", "modifier": "<PERSON><PERSON><PERSON>", "type": {"plain": 4}, "fallback": "0x00000000", "docs": [" Block in which the current session started."]}, {"name": "NextAuthorityIds", "modifier": "Optional", "type": {"map": {"hashers": ["Identity"], "key": 4, "value": 244}}, "fallback": "0x00", "docs": [" Authority list for the next session."]}, {"name": "Mixnodes", "modifier": "Optional", "type": {"map": {"hashers": ["Identity", "Identity"], "key": 94, "value": 455}}, "fallback": "0x00", "docs": [" Mixnode sets by session index. Only the mixnode sets for the previous, current, and next", " sessions are kept; older sets are discarded.", "", " The mixnodes in each set are keyed by authority index so we can easily check if an", " authority has registered a mixnode. The authority indices should only be used during", " registration; the authority indices for the very first session are made up."]}]}, "calls": {"type": 453}, "events": null, "constants": [{"name": "MaxAuthorities", "type": 4, "value": "0x64000000", "docs": [" The maximum number of authorities per session."]}, {"name": "MaxExternalAddressSize", "type": 4, "value": "0x80000000", "docs": [" The maximum size of one of a mixnode's external addresses."]}, {"name": "MaxExternalAddressesPerMixnode", "type": 4, "value": "0x10000000", "docs": [" The maximum number of external addresses for a mixnode."]}, {"name": "NumCoverToCurrentBlocks", "type": 4, "value": "0x03000000", "docs": [" Length of the first phase of each session (`CoverToCurrent`), in blocks."]}, {"name": "NumRequestsToCurrentBlocks", "type": 4, "value": "0x03000000", "docs": [" Length of the second phase of each session (`RequestsToCurrent`), in blocks."]}, {"name": "NumCoverToPrevBlocks", "type": 4, "value": "0x03000000", "docs": [" Length of the third phase of each session (`CoverToPrev`), in blocks."]}, {"name": "NumRegisterStartSlackBlocks", "type": 4, "value": "0x03000000", "docs": [" The number of \"slack\" blocks at the start of each session, during which", " [`maybe_register`](<PERSON><PERSON><PERSON>::maybe_register) will not attempt to post registration", " transactions."]}, {"name": "NumRegisterEndSlackBlocks", "type": 4, "value": "0x03000000", "docs": [" The number of \"slack\" blocks at the end of each session.", " [`maybe_register`](<PERSON><PERSON><PERSON>::maybe_register) will try to register before this slack", " period, but may post registration transactions during the slack period as a last", " resort."]}, {"name": "RegistrationPriority", "type": 12, "value": "0xfeffffffffffffff", "docs": [" Priority of unsigned transactions used to register mixnodes."]}, {"name": "MinMixnodes", "type": 4, "value": "0x07000000", "docs": [" Minimum number of mixnodes. If there are fewer than this many mixnodes registered for a", " session, the mixnet will not be active during the session."]}], "errors": null, "index": 75}, {"name": "Parameters", "storage": {"prefix": "Parameters", "items": [{"name": "Parameters", "modifier": "Optional", "type": {"map": {"hashers": ["Blake2_128Concat"], "key": 506, "value": 509}}, "fallback": "0x00", "docs": [" Stored parameters."]}]}, "calls": {"type": 459}, "events": {"type": 505}, "constants": [], "errors": null, "index": 76}, {"name": "SkipFeelessPayment", "storage": null, "calls": null, "events": {"type": 511}, "constants": [], "errors": null, "index": 77}, {"name": "PalletExampleMbms", "storage": {"prefix": "PalletExampleMbms", "items": [{"name": "MyMap", "modifier": "Optional", "type": {"map": {"hashers": ["Blake2_128Concat"], "key": 4, "value": 12}}, "fallback": "0x00", "docs": [" Define a storage item to illustrate multi-block migrations."]}]}, "calls": null, "events": null, "constants": [], "errors": null, "index": 78}, {"name": "AssetConversionMigration", "storage": null, "calls": {"type": 464}, "events": {"type": 512}, "constants": [], "errors": {"type": 931}, "index": 79}], "extrinsic": {"type": 932, "version": 4, "signedExtensions": [{"identifier": "CheckNonZeroSender", "type": 934, "additionalSigned": 33}, {"identifier": "CheckSpecVersion", "type": 935, "additionalSigned": 4}, {"identifier": "CheckTxVersion", "type": 936, "additionalSigned": 4}, {"identifier": "CheckGenesis", "type": 937, "additionalSigned": 13}, {"identifier": "CheckMortality", "type": 938, "additionalSigned": 13}, {"identifier": "CheckNonce", "type": 940, "additionalSigned": 33}, {"identifier": "CheckWeight", "type": 941, "additionalSigned": 33}, {"identifier": "ChargeAssetTxPayment", "type": 942, "additionalSigned": 33}, {"identifier": "CheckMetadataHash", "type": 943, "additionalSigned": 95}]}, "type": 74}}}
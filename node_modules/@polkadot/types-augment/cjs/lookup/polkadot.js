"use strict";
/* eslint-disable */
Object.defineProperty(exports, "__esModule", { value: true });
/* eslint-disable sort-keys */
exports.default = {
    /**
     * Lookup54: sp_core::ed25519::Public
     **/
    SpCoreEd25519Public: '[u8;32]',
    /**
     * Lookup55: polkadot_runtime::pallet_im_online::pallet::Event<T>
     **/
    PolkadotRuntimePalletImOnlinePalletEvent: {
        _enum: {
            HeartbeatReceived: {
                authorityId: 'PolkadotRuntimePalletImOnlineSr25519AppSr25519Public',
            },
            AllGood: 'Null',
            SomeOffline: {
                offline: 'Vec<(AccountId32,SpStakingExposure)>'
            }
        }
    },
    /**
     * Lookup56: polkadot_runtime::pallet_im_online::sr25519::app_sr25519::Public
     **/
    PolkadotRuntimePalletImOnlineSr25519AppSr25519Public: 'SpCoreSr25519Public',
    /**
     * Lookup57: sp_core::sr25519::Public
     **/
    SpCoreSr25519Public: '[u8;32]',
    /**
     * Lookup65: polkadot_runtime_common::impls::VersionedLocatableAsset
     **/
    PolkadotRuntimeCommonImplsVersionedLocatableAsset: {
        _enum: {
            __Unused0: 'Null',
            __Unused1: 'Null',
            __Unused2: 'Null',
            V3: {
                location: 'StagingXcmV3MultiLocation',
                assetId: 'XcmV3MultiassetAssetId',
            },
            V4: {
                location: 'StagingXcmV4Location',
                assetId: 'StagingXcmV4AssetAssetId'
            }
        }
    },
    /**
     * Lookup66: staging_xcm::v3::multilocation::MultiLocation
     **/
    StagingXcmV3MultiLocation: {
        parents: 'u8',
        interior: 'XcmV3Junctions'
    },
    /**
     * Lookup67: xcm::v3::junctions::Junctions
     **/
    XcmV3Junctions: {
        _enum: {
            Here: 'Null',
            X1: 'XcmV3Junction',
            X2: '(XcmV3Junction,XcmV3Junction)',
            X3: '(XcmV3Junction,XcmV3Junction,XcmV3Junction)',
            X4: '(XcmV3Junction,XcmV3Junction,XcmV3Junction,XcmV3Junction)',
            X5: '(XcmV3Junction,XcmV3Junction,XcmV3Junction,XcmV3Junction,XcmV3Junction)',
            X6: '(XcmV3Junction,XcmV3Junction,XcmV3Junction,XcmV3Junction,XcmV3Junction,XcmV3Junction)',
            X7: '(XcmV3Junction,XcmV3Junction,XcmV3Junction,XcmV3Junction,XcmV3Junction,XcmV3Junction,XcmV3Junction)',
            X8: '(XcmV3Junction,XcmV3Junction,XcmV3Junction,XcmV3Junction,XcmV3Junction,XcmV3Junction,XcmV3Junction,XcmV3Junction)'
        }
    },
    /**
     * Lookup68: xcm::v3::junction::Junction
     **/
    XcmV3Junction: {
        _enum: {
            Parachain: 'Compact<u32>',
            AccountId32: {
                network: 'Option<XcmV3JunctionNetworkId>',
                id: '[u8;32]',
            },
            AccountIndex64: {
                network: 'Option<XcmV3JunctionNetworkId>',
                index: 'Compact<u64>',
            },
            AccountKey20: {
                network: 'Option<XcmV3JunctionNetworkId>',
                key: '[u8;20]',
            },
            PalletInstance: 'u8',
            GeneralIndex: 'Compact<u128>',
            GeneralKey: {
                length: 'u8',
                data: '[u8;32]',
            },
            OnlyChild: 'Null',
            Plurality: {
                id: 'XcmV3JunctionBodyId',
                part: 'XcmV3JunctionBodyPart',
            },
            GlobalConsensus: 'XcmV3JunctionNetworkId'
        }
    },
    /**
     * Lookup71: xcm::v3::junction::NetworkId
     **/
    XcmV3JunctionNetworkId: {
        _enum: {
            ByGenesis: '[u8;32]',
            ByFork: {
                blockNumber: 'u64',
                blockHash: '[u8;32]',
            },
            Polkadot: 'Null',
            Kusama: 'Null',
            Westend: 'Null',
            Rococo: 'Null',
            Wococo: 'Null',
            Ethereum: {
                chainId: 'Compact<u64>',
            },
            BitcoinCore: 'Null',
            BitcoinCash: 'Null',
            PolkadotBulletin: 'Null'
        }
    },
    /**
     * Lookup73: xcm::v3::junction::BodyId
     **/
    XcmV3JunctionBodyId: {
        _enum: {
            Unit: 'Null',
            Moniker: '[u8;4]',
            Index: 'Compact<u32>',
            Executive: 'Null',
            Technical: 'Null',
            Legislative: 'Null',
            Judicial: 'Null',
            Defense: 'Null',
            Administration: 'Null',
            Treasury: 'Null'
        }
    },
    /**
     * Lookup74: xcm::v3::junction::BodyPart
     **/
    XcmV3JunctionBodyPart: {
        _enum: {
            Voice: 'Null',
            Members: {
                count: 'Compact<u32>',
            },
            Fraction: {
                nom: 'Compact<u32>',
                denom: 'Compact<u32>',
            },
            AtLeastProportion: {
                nom: 'Compact<u32>',
                denom: 'Compact<u32>',
            },
            MoreThanProportion: {
                nom: 'Compact<u32>',
                denom: 'Compact<u32>'
            }
        }
    },
    /**
     * Lookup75: xcm::v3::multiasset::AssetId
     **/
    XcmV3MultiassetAssetId: {
        _enum: {
            Concrete: 'StagingXcmV3MultiLocation',
            Abstract: '[u8;32]'
        }
    },
    /**
     * Lookup76: staging_xcm::v4::location::Location
     **/
    StagingXcmV4Location: {
        parents: 'u8',
        interior: 'StagingXcmV4Junctions'
    },
    /**
     * Lookup77: staging_xcm::v4::junctions::Junctions
     **/
    StagingXcmV4Junctions: {
        _enum: {
            Here: 'Null',
            X1: '[Lookup79;1]',
            X2: '[Lookup79;2]',
            X3: '[Lookup79;3]',
            X4: '[Lookup79;4]',
            X5: '[Lookup79;5]',
            X6: '[Lookup79;6]',
            X7: '[Lookup79;7]',
            X8: '[Lookup79;8]'
        }
    },
    /**
     * Lookup79: staging_xcm::v4::junction::Junction
     **/
    StagingXcmV4Junction: {
        _enum: {
            Parachain: 'Compact<u32>',
            AccountId32: {
                network: 'Option<StagingXcmV4JunctionNetworkId>',
                id: '[u8;32]',
            },
            AccountIndex64: {
                network: 'Option<StagingXcmV4JunctionNetworkId>',
                index: 'Compact<u64>',
            },
            AccountKey20: {
                network: 'Option<StagingXcmV4JunctionNetworkId>',
                key: '[u8;20]',
            },
            PalletInstance: 'u8',
            GeneralIndex: 'Compact<u128>',
            GeneralKey: {
                length: 'u8',
                data: '[u8;32]',
            },
            OnlyChild: 'Null',
            Plurality: {
                id: 'XcmV3JunctionBodyId',
                part: 'XcmV3JunctionBodyPart',
            },
            GlobalConsensus: 'StagingXcmV4JunctionNetworkId'
        }
    },
    /**
     * Lookup81: staging_xcm::v4::junction::NetworkId
     **/
    StagingXcmV4JunctionNetworkId: {
        _enum: {
            ByGenesis: '[u8;32]',
            ByFork: {
                blockNumber: 'u64',
                blockHash: '[u8;32]',
            },
            Polkadot: 'Null',
            Kusama: 'Null',
            Westend: 'Null',
            Rococo: 'Null',
            Wococo: 'Null',
            Ethereum: {
                chainId: 'Compact<u64>',
            },
            BitcoinCore: 'Null',
            BitcoinCash: 'Null',
            PolkadotBulletin: 'Null'
        }
    },
    /**
     * Lookup89: staging_xcm::v4::asset::AssetId
     **/
    StagingXcmV4AssetAssetId: 'StagingXcmV4Location',
    /**
     * Lookup90: xcm::VersionedLocation
     **/
    XcmVersionedLocation: {
        _enum: {
            __Unused0: 'Null',
            V2: 'XcmV2MultiLocation',
            __Unused2: 'Null',
            V3: 'StagingXcmV3MultiLocation',
            V4: 'StagingXcmV4Location'
        }
    },
    /**
     * Lookup91: xcm::v2::multilocation::MultiLocation
     **/
    XcmV2MultiLocation: {
        parents: 'u8',
        interior: 'XcmV2MultilocationJunctions'
    },
    /**
     * Lookup92: xcm::v2::multilocation::Junctions
     **/
    XcmV2MultilocationJunctions: {
        _enum: {
            Here: 'Null',
            X1: 'XcmV2Junction',
            X2: '(XcmV2Junction,XcmV2Junction)',
            X3: '(XcmV2Junction,XcmV2Junction,XcmV2Junction)',
            X4: '(XcmV2Junction,XcmV2Junction,XcmV2Junction,XcmV2Junction)',
            X5: '(XcmV2Junction,XcmV2Junction,XcmV2Junction,XcmV2Junction,XcmV2Junction)',
            X6: '(XcmV2Junction,XcmV2Junction,XcmV2Junction,XcmV2Junction,XcmV2Junction,XcmV2Junction)',
            X7: '(XcmV2Junction,XcmV2Junction,XcmV2Junction,XcmV2Junction,XcmV2Junction,XcmV2Junction,XcmV2Junction)',
            X8: '(XcmV2Junction,XcmV2Junction,XcmV2Junction,XcmV2Junction,XcmV2Junction,XcmV2Junction,XcmV2Junction,XcmV2Junction)'
        }
    },
    /**
     * Lookup93: xcm::v2::junction::Junction
     **/
    XcmV2Junction: {
        _enum: {
            Parachain: 'Compact<u32>',
            AccountId32: {
                network: 'XcmV2NetworkId',
                id: '[u8;32]',
            },
            AccountIndex64: {
                network: 'XcmV2NetworkId',
                index: 'Compact<u64>',
            },
            AccountKey20: {
                network: 'XcmV2NetworkId',
                key: '[u8;20]',
            },
            PalletInstance: 'u8',
            GeneralIndex: 'Compact<u128>',
            GeneralKey: 'Bytes',
            OnlyChild: 'Null',
            Plurality: {
                id: 'XcmV2BodyId',
                part: 'XcmV2BodyPart'
            }
        }
    },
    /**
     * Lookup94: xcm::v2::NetworkId
     **/
    XcmV2NetworkId: {
        _enum: {
            Any: 'Null',
            Named: 'Bytes',
            Polkadot: 'Null',
            Kusama: 'Null'
        }
    },
    /**
     * Lookup96: xcm::v2::BodyId
     **/
    XcmV2BodyId: {
        _enum: {
            Unit: 'Null',
            Named: 'Bytes',
            Index: 'Compact<u32>',
            Executive: 'Null',
            Technical: 'Null',
            Legislative: 'Null',
            Judicial: 'Null',
            Defense: 'Null',
            Administration: 'Null',
            Treasury: 'Null'
        }
    },
    /**
     * Lookup97: xcm::v2::BodyPart
     **/
    XcmV2BodyPart: {
        _enum: {
            Voice: 'Null',
            Members: {
                count: 'Compact<u32>',
            },
            Fraction: {
                nom: 'Compact<u32>',
                denom: 'Compact<u32>',
            },
            AtLeastProportion: {
                nom: 'Compact<u32>',
                denom: 'Compact<u32>',
            },
            MoreThanProportion: {
                nom: 'Compact<u32>',
                denom: 'Compact<u32>'
            }
        }
    },
    /**
     * Lookup143: polkadot_runtime::SessionKeys
     **/
    PolkadotRuntimeSessionKeys: {
        grandpa: 'SpConsensusGrandpaAppPublic',
        babe: 'SpConsensusBabeAppPublic',
        paraValidator: 'PolkadotPrimitivesV6ValidatorAppPublic',
        paraAssignment: 'PolkadotPrimitivesV6AssignmentAppPublic',
        authorityDiscovery: 'SpAuthorityDiscoveryAppPublic',
        beefy: 'SpConsensusBeefyEcdsaCryptoPublic'
    },
    /**
     * Lookup144: polkadot_primitives::v6::validator_app::Public
     **/
    PolkadotPrimitivesV6ValidatorAppPublic: 'SpCoreSr25519Public',
    /**
     * Lookup145: polkadot_primitives::v6::assignment_app::Public
     **/
    PolkadotPrimitivesV6AssignmentAppPublic: 'SpCoreSr25519Public',
    /**
     * Lookup148: sp_core::ecdsa::Public
     **/
    SpCoreEcdsaPublic: '[u8;33]',
    /**
     * Lookup156: sp_core::ed25519::Signature
     **/
    SpCoreEd25519Signature: '[u8;64]',
    /**
     * Lookup170: polkadot_runtime::OriginCaller
     **/
    PolkadotRuntimeOriginCaller: {
        _enum: {
            system: 'FrameSupportDispatchRawOrigin',
            __Unused1: 'Null',
            __Unused2: 'Null',
            __Unused3: 'Null',
            Void: 'SpCoreVoid',
            __Unused5: 'Null',
            __Unused6: 'Null',
            __Unused7: 'Null',
            __Unused8: 'Null',
            __Unused9: 'Null',
            __Unused10: 'Null',
            __Unused11: 'Null',
            __Unused12: 'Null',
            __Unused13: 'Null',
            __Unused14: 'Null',
            __Unused15: 'Null',
            __Unused16: 'Null',
            __Unused17: 'Null',
            __Unused18: 'Null',
            __Unused19: 'Null',
            __Unused20: 'Null',
            __Unused21: 'Null',
            Origins: 'PolkadotRuntimeGovernanceOriginsPalletCustomOriginsOrigin',
            __Unused23: 'Null',
            __Unused24: 'Null',
            __Unused25: 'Null',
            __Unused26: 'Null',
            __Unused27: 'Null',
            __Unused28: 'Null',
            __Unused29: 'Null',
            __Unused30: 'Null',
            __Unused31: 'Null',
            __Unused32: 'Null',
            __Unused33: 'Null',
            __Unused34: 'Null',
            __Unused35: 'Null',
            __Unused36: 'Null',
            __Unused37: 'Null',
            __Unused38: 'Null',
            __Unused39: 'Null',
            __Unused40: 'Null',
            __Unused41: 'Null',
            __Unused42: 'Null',
            __Unused43: 'Null',
            __Unused44: 'Null',
            __Unused45: 'Null',
            __Unused46: 'Null',
            __Unused47: 'Null',
            __Unused48: 'Null',
            __Unused49: 'Null',
            ParachainsOrigin: 'PolkadotRuntimeParachainsOriginPalletOrigin',
            __Unused51: 'Null',
            __Unused52: 'Null',
            __Unused53: 'Null',
            __Unused54: 'Null',
            __Unused55: 'Null',
            __Unused56: 'Null',
            __Unused57: 'Null',
            __Unused58: 'Null',
            __Unused59: 'Null',
            __Unused60: 'Null',
            __Unused61: 'Null',
            __Unused62: 'Null',
            __Unused63: 'Null',
            __Unused64: 'Null',
            __Unused65: 'Null',
            __Unused66: 'Null',
            __Unused67: 'Null',
            __Unused68: 'Null',
            __Unused69: 'Null',
            __Unused70: 'Null',
            __Unused71: 'Null',
            __Unused72: 'Null',
            __Unused73: 'Null',
            __Unused74: 'Null',
            __Unused75: 'Null',
            __Unused76: 'Null',
            __Unused77: 'Null',
            __Unused78: 'Null',
            __Unused79: 'Null',
            __Unused80: 'Null',
            __Unused81: 'Null',
            __Unused82: 'Null',
            __Unused83: 'Null',
            __Unused84: 'Null',
            __Unused85: 'Null',
            __Unused86: 'Null',
            __Unused87: 'Null',
            __Unused88: 'Null',
            __Unused89: 'Null',
            __Unused90: 'Null',
            __Unused91: 'Null',
            __Unused92: 'Null',
            __Unused93: 'Null',
            __Unused94: 'Null',
            __Unused95: 'Null',
            __Unused96: 'Null',
            __Unused97: 'Null',
            __Unused98: 'Null',
            XcmPallet: 'PalletXcmOrigin'
        }
    },
    /**
     * Lookup172: polkadot_runtime::governance::origins::pallet_custom_origins::Origin
     **/
    PolkadotRuntimeGovernanceOriginsPalletCustomOriginsOrigin: {
        _enum: ['StakingAdmin', 'Treasurer', 'FellowshipAdmin', 'GeneralAdmin', 'AuctionAdmin', 'LeaseAdmin', 'ReferendumCanceller', 'ReferendumKiller', 'SmallTipper', 'BigTipper', 'SmallSpender', 'MediumSpender', 'BigSpender', 'WhitelistedCaller', 'WishForChange']
    },
    /**
     * Lookup173: polkadot_runtime_parachains::origin::pallet::Origin
     **/
    PolkadotRuntimeParachainsOriginPalletOrigin: {
        _enum: {
            Parachain: 'u32'
        }
    },
    /**
     * Lookup175: pallet_xcm::pallet::Origin
     **/
    PalletXcmOrigin: {
        _enum: {
            Xcm: 'StagingXcmV4Location',
            Response: 'StagingXcmV4Location'
        }
    },
    /**
     * Lookup180: polkadot_runtime_common::claims::pallet::Call<T>
     **/
    PolkadotRuntimeCommonClaimsPalletCall: {
        _enum: {
            claim: {
                dest: 'AccountId32',
                ethereumSignature: 'PolkadotRuntimeCommonClaimsEcdsaSignature',
            },
            mint_claim: {
                who: 'EthereumAddress',
                value: 'u128',
                vestingSchedule: 'Option<(u128,u128,u32)>',
                statement: 'Option<PolkadotRuntimeCommonClaimsStatementKind>',
            },
            claim_attest: {
                dest: 'AccountId32',
                ethereumSignature: 'PolkadotRuntimeCommonClaimsEcdsaSignature',
                statement: 'Bytes',
            },
            attest: {
                statement: 'Bytes',
            },
            move_claim: {
                _alias: {
                    new_: 'new',
                },
                old: 'EthereumAddress',
                new_: 'EthereumAddress',
                maybePreclaim: 'Option<AccountId32>'
            }
        }
    },
    /**
     * Lookup181: polkadot_runtime_common::claims::EcdsaSignature
     **/
    PolkadotRuntimeCommonClaimsEcdsaSignature: '[u8;65]',
    /**
     * Lookup187: polkadot_runtime_common::claims::StatementKind
     **/
    PolkadotRuntimeCommonClaimsStatementKind: {
        _enum: ['Regular', 'Saft']
    },
    /**
     * Lookup233: sp_core::sr25519::Signature
     **/
    SpCoreSr25519Signature: '[u8;64]',
    /**
     * Lookup234: sp_core::ecdsa::Signature
     **/
    SpCoreEcdsaSignature: '[u8;65]',
    /**
     * Lookup238: polkadot_runtime::ProxyType
     **/
    PolkadotRuntimeProxyType: {
        _enum: ['Any', 'NonTransfer', 'Governance', 'Staking', '__Unused4', 'IdentityJudgement', 'CancelProxy', 'Auction', 'NominationPools']
    },
    /**
     * Lookup246: polkadot_runtime::NposCompactSolution16
     **/
    PolkadotRuntimeNposCompactSolution16: {
        votes1: 'Vec<(Compact<u32>,Compact<u16>)>',
        votes2: 'Vec<(Compact<u32>,(Compact<u16>,Compact<PerU16>),Compact<u16>)>',
        votes3: 'Vec<(Compact<u32>,[(Compact<u16>,Compact<PerU16>);2],Compact<u16>)>',
        votes4: 'Vec<(Compact<u32>,[(Compact<u16>,Compact<PerU16>);3],Compact<u16>)>',
        votes5: 'Vec<(Compact<u32>,[(Compact<u16>,Compact<PerU16>);4],Compact<u16>)>',
        votes6: 'Vec<(Compact<u32>,[(Compact<u16>,Compact<PerU16>);5],Compact<u16>)>',
        votes7: 'Vec<(Compact<u32>,[(Compact<u16>,Compact<PerU16>);6],Compact<u16>)>',
        votes8: 'Vec<(Compact<u32>,[(Compact<u16>,Compact<PerU16>);7],Compact<u16>)>',
        votes9: 'Vec<(Compact<u32>,[(Compact<u16>,Compact<PerU16>);8],Compact<u16>)>',
        votes10: 'Vec<(Compact<u32>,[(Compact<u16>,Compact<PerU16>);9],Compact<u16>)>',
        votes11: 'Vec<(Compact<u32>,[(Compact<u16>,Compact<PerU16>);10],Compact<u16>)>',
        votes12: 'Vec<(Compact<u32>,[(Compact<u16>,Compact<PerU16>);11],Compact<u16>)>',
        votes13: 'Vec<(Compact<u32>,[(Compact<u16>,Compact<PerU16>);12],Compact<u16>)>',
        votes14: 'Vec<(Compact<u32>,[(Compact<u16>,Compact<PerU16>);13],Compact<u16>)>',
        votes15: 'Vec<(Compact<u32>,[(Compact<u16>,Compact<PerU16>);14],Compact<u16>)>',
        votes16: 'Vec<(Compact<u32>,[(Compact<u16>,Compact<PerU16>);15],Compact<u16>)>'
    },
    /**
     * Lookup320: polkadot_runtime_parachains::configuration::pallet::Call<T>
     **/
    PolkadotRuntimeParachainsConfigurationPalletCall: {
        _enum: {
            set_validation_upgrade_cooldown: {
                _alias: {
                    new_: 'new',
                },
                new_: 'u32',
            },
            set_validation_upgrade_delay: {
                _alias: {
                    new_: 'new',
                },
                new_: 'u32',
            },
            set_code_retention_period: {
                _alias: {
                    new_: 'new',
                },
                new_: 'u32',
            },
            set_max_code_size: {
                _alias: {
                    new_: 'new',
                },
                new_: 'u32',
            },
            set_max_pov_size: {
                _alias: {
                    new_: 'new',
                },
                new_: 'u32',
            },
            set_max_head_data_size: {
                _alias: {
                    new_: 'new',
                },
                new_: 'u32',
            },
            set_coretime_cores: {
                _alias: {
                    new_: 'new',
                },
                new_: 'u32',
            },
            set_on_demand_retries: {
                _alias: {
                    new_: 'new',
                },
                new_: 'u32',
            },
            set_group_rotation_frequency: {
                _alias: {
                    new_: 'new',
                },
                new_: 'u32',
            },
            set_paras_availability_period: {
                _alias: {
                    new_: 'new',
                },
                new_: 'u32',
            },
            __Unused10: 'Null',
            set_scheduling_lookahead: {
                _alias: {
                    new_: 'new',
                },
                new_: 'u32',
            },
            set_max_validators_per_core: {
                _alias: {
                    new_: 'new',
                },
                new_: 'Option<u32>',
            },
            set_max_validators: {
                _alias: {
                    new_: 'new',
                },
                new_: 'Option<u32>',
            },
            set_dispute_period: {
                _alias: {
                    new_: 'new',
                },
                new_: 'u32',
            },
            set_dispute_post_conclusion_acceptance_period: {
                _alias: {
                    new_: 'new',
                },
                new_: 'u32',
            },
            __Unused16: 'Null',
            __Unused17: 'Null',
            set_no_show_slots: {
                _alias: {
                    new_: 'new',
                },
                new_: 'u32',
            },
            set_n_delay_tranches: {
                _alias: {
                    new_: 'new',
                },
                new_: 'u32',
            },
            set_zeroth_delay_tranche_width: {
                _alias: {
                    new_: 'new',
                },
                new_: 'u32',
            },
            set_needed_approvals: {
                _alias: {
                    new_: 'new',
                },
                new_: 'u32',
            },
            set_relay_vrf_modulo_samples: {
                _alias: {
                    new_: 'new',
                },
                new_: 'u32',
            },
            set_max_upward_queue_count: {
                _alias: {
                    new_: 'new',
                },
                new_: 'u32',
            },
            set_max_upward_queue_size: {
                _alias: {
                    new_: 'new',
                },
                new_: 'u32',
            },
            set_max_downward_message_size: {
                _alias: {
                    new_: 'new',
                },
                new_: 'u32',
            },
            __Unused26: 'Null',
            set_max_upward_message_size: {
                _alias: {
                    new_: 'new',
                },
                new_: 'u32',
            },
            set_max_upward_message_num_per_candidate: {
                _alias: {
                    new_: 'new',
                },
                new_: 'u32',
            },
            set_hrmp_open_request_ttl: {
                _alias: {
                    new_: 'new',
                },
                new_: 'u32',
            },
            set_hrmp_sender_deposit: {
                _alias: {
                    new_: 'new',
                },
                new_: 'u128',
            },
            set_hrmp_recipient_deposit: {
                _alias: {
                    new_: 'new',
                },
                new_: 'u128',
            },
            set_hrmp_channel_max_capacity: {
                _alias: {
                    new_: 'new',
                },
                new_: 'u32',
            },
            set_hrmp_channel_max_total_size: {
                _alias: {
                    new_: 'new',
                },
                new_: 'u32',
            },
            set_hrmp_max_parachain_inbound_channels: {
                _alias: {
                    new_: 'new',
                },
                new_: 'u32',
            },
            __Unused35: 'Null',
            set_hrmp_channel_max_message_size: {
                _alias: {
                    new_: 'new',
                },
                new_: 'u32',
            },
            set_hrmp_max_parachain_outbound_channels: {
                _alias: {
                    new_: 'new',
                },
                new_: 'u32',
            },
            __Unused38: 'Null',
            set_hrmp_max_message_num_per_candidate: {
                _alias: {
                    new_: 'new',
                },
                new_: 'u32',
            },
            __Unused40: 'Null',
            __Unused41: 'Null',
            set_pvf_voting_ttl: {
                _alias: {
                    new_: 'new',
                },
                new_: 'u32',
            },
            set_minimum_validation_upgrade_delay: {
                _alias: {
                    new_: 'new',
                },
                new_: 'u32',
            },
            set_bypass_consistency_check: {
                _alias: {
                    new_: 'new',
                },
                new_: 'bool',
            },
            set_async_backing_params: {
                _alias: {
                    new_: 'new',
                },
                new_: 'PolkadotPrimitivesV6AsyncBackingAsyncBackingParams',
            },
            set_executor_params: {
                _alias: {
                    new_: 'new',
                },
                new_: 'PolkadotPrimitivesV6ExecutorParams',
            },
            set_on_demand_base_fee: {
                _alias: {
                    new_: 'new',
                },
                new_: 'u128',
            },
            set_on_demand_fee_variability: {
                _alias: {
                    new_: 'new',
                },
                new_: 'Perbill',
            },
            set_on_demand_queue_max_size: {
                _alias: {
                    new_: 'new',
                },
                new_: 'u32',
            },
            set_on_demand_target_queue_utilization: {
                _alias: {
                    new_: 'new',
                },
                new_: 'Perbill',
            },
            set_on_demand_ttl: {
                _alias: {
                    new_: 'new',
                },
                new_: 'u32',
            },
            set_minimum_backing_votes: {
                _alias: {
                    new_: 'new',
                },
                new_: 'u32',
            },
            set_node_feature: {
                index: 'u8',
                value: 'bool',
            },
            set_approval_voting_params: {
                _alias: {
                    new_: 'new',
                },
                new_: 'PolkadotPrimitivesVstagingApprovalVotingParams'
            }
        }
    },
    /**
     * Lookup321: polkadot_primitives::v6::async_backing::AsyncBackingParams
     **/
    PolkadotPrimitivesV6AsyncBackingAsyncBackingParams: {
        maxCandidateDepth: 'u32',
        allowedAncestryLen: 'u32'
    },
    /**
     * Lookup322: polkadot_primitives::v6::executor_params::ExecutorParams
     **/
    PolkadotPrimitivesV6ExecutorParams: 'Vec<PolkadotPrimitivesV6ExecutorParamsExecutorParam>',
    /**
     * Lookup324: polkadot_primitives::v6::executor_params::ExecutorParam
     **/
    PolkadotPrimitivesV6ExecutorParamsExecutorParam: {
        _enum: {
            __Unused0: 'Null',
            MaxMemoryPages: 'u32',
            StackLogicalMax: 'u32',
            StackNativeMax: 'u32',
            PrecheckingMaxMemory: 'u64',
            PvfPrepTimeout: '(PolkadotPrimitivesV6PvfPrepKind,u64)',
            PvfExecTimeout: '(PolkadotPrimitivesV6PvfExecKind,u64)',
            WasmExtBulkMemory: 'Null'
        }
    },
    /**
     * Lookup325: polkadot_primitives::v6::PvfPrepKind
     **/
    PolkadotPrimitivesV6PvfPrepKind: {
        _enum: ['Precheck', 'Prepare']
    },
    /**
     * Lookup326: polkadot_primitives::v6::PvfExecKind
     **/
    PolkadotPrimitivesV6PvfExecKind: {
        _enum: ['Backing', 'Approval']
    },
    /**
     * Lookup327: polkadot_primitives::vstaging::ApprovalVotingParams
     **/
    PolkadotPrimitivesVstagingApprovalVotingParams: {
        maxApprovalCoalesceCount: 'u32'
    },
    /**
     * Lookup328: polkadot_runtime_parachains::shared::pallet::Call<T>
     **/
    PolkadotRuntimeParachainsSharedPalletCall: 'Null',
    /**
     * Lookup329: polkadot_runtime_parachains::inclusion::pallet::Call<T>
     **/
    PolkadotRuntimeParachainsInclusionPalletCall: 'Null',
    /**
     * Lookup330: polkadot_runtime_parachains::paras_inherent::pallet::Call<T>
     **/
    PolkadotRuntimeParachainsParasInherentPalletCall: {
        _enum: {
            enter: {
                data: 'PolkadotPrimitivesV6InherentData'
            }
        }
    },
    /**
     * Lookup331: polkadot_primitives::v6::InherentData<sp_runtime::generic::header::Header<Number, Hash>>
     **/
    PolkadotPrimitivesV6InherentData: {
        bitfields: 'Vec<PolkadotPrimitivesV6SignedUncheckedSigned>',
        backedCandidates: 'Vec<PolkadotPrimitivesV6BackedCandidate>',
        disputes: 'Vec<PolkadotPrimitivesV6DisputeStatementSet>',
        parentHeader: 'SpRuntimeHeader'
    },
    /**
     * Lookup333: polkadot_primitives::v6::signed::UncheckedSigned<polkadot_primitives::v6::AvailabilityBitfield, polkadot_primitives::v6::AvailabilityBitfield>
     **/
    PolkadotPrimitivesV6SignedUncheckedSigned: {
        payload: 'BitVec',
        validatorIndex: 'u32',
        signature: 'PolkadotPrimitivesV6ValidatorAppSignature'
    },
    /**
     * Lookup336: bitvec::order::Lsb0
     **/
    BitvecOrderLsb0: 'Null',
    /**
     * Lookup338: polkadot_primitives::v6::validator_app::Signature
     **/
    PolkadotPrimitivesV6ValidatorAppSignature: 'SpCoreSr25519Signature',
    /**
     * Lookup340: polkadot_primitives::v6::BackedCandidate<primitive_types::H256>
     **/
    PolkadotPrimitivesV6BackedCandidate: {
        candidate: 'PolkadotPrimitivesV6CommittedCandidateReceipt',
        validityVotes: 'Vec<PolkadotPrimitivesV6ValidityAttestation>',
        validatorIndices: 'BitVec'
    },
    /**
     * Lookup341: polkadot_primitives::v6::CommittedCandidateReceipt<primitive_types::H256>
     **/
    PolkadotPrimitivesV6CommittedCandidateReceipt: {
        descriptor: 'PolkadotPrimitivesV6CandidateDescriptor',
        commitments: 'PolkadotPrimitivesV6CandidateCommitments'
    },
    /**
     * Lookup342: polkadot_primitives::v6::CandidateDescriptor<primitive_types::H256>
     **/
    PolkadotPrimitivesV6CandidateDescriptor: {
        paraId: 'u32',
        relayParent: 'H256',
        collator: 'PolkadotPrimitivesV6CollatorAppPublic',
        persistedValidationDataHash: 'H256',
        povHash: 'H256',
        erasureRoot: 'H256',
        signature: 'PolkadotPrimitivesV6CollatorAppSignature',
        paraHead: 'H256',
        validationCodeHash: 'H256'
    },
    /**
     * Lookup343: polkadot_primitives::v6::collator_app::Public
     **/
    PolkadotPrimitivesV6CollatorAppPublic: 'SpCoreSr25519Public',
    /**
     * Lookup344: polkadot_primitives::v6::collator_app::Signature
     **/
    PolkadotPrimitivesV6CollatorAppSignature: 'SpCoreSr25519Signature',
    /**
     * Lookup346: polkadot_primitives::v6::CandidateCommitments<N>
     **/
    PolkadotPrimitivesV6CandidateCommitments: {
        upwardMessages: 'Vec<Bytes>',
        horizontalMessages: 'Vec<PolkadotCorePrimitivesOutboundHrmpMessage>',
        newValidationCode: 'Option<Bytes>',
        headData: 'Bytes',
        processedDownwardMessages: 'u32',
        hrmpWatermark: 'u32'
    },
    /**
     * Lookup349: polkadot_core_primitives::OutboundHrmpMessage<polkadot_parachain_primitives::primitives::Id>
     **/
    PolkadotCorePrimitivesOutboundHrmpMessage: {
        recipient: 'u32',
        data: 'Bytes'
    },
    /**
     * Lookup355: polkadot_primitives::v6::ValidityAttestation
     **/
    PolkadotPrimitivesV6ValidityAttestation: {
        _enum: {
            __Unused0: 'Null',
            Implicit: 'PolkadotPrimitivesV6ValidatorAppSignature',
            Explicit: 'PolkadotPrimitivesV6ValidatorAppSignature'
        }
    },
    /**
     * Lookup357: polkadot_primitives::v6::DisputeStatementSet
     **/
    PolkadotPrimitivesV6DisputeStatementSet: {
        candidateHash: 'H256',
        session: 'u32',
        statements: 'Vec<(PolkadotPrimitivesV6DisputeStatement,u32,PolkadotPrimitivesV6ValidatorAppSignature)>'
    },
    /**
     * Lookup361: polkadot_primitives::v6::DisputeStatement
     **/
    PolkadotPrimitivesV6DisputeStatement: {
        _enum: {
            Valid: 'PolkadotPrimitivesV6ValidDisputeStatementKind',
            Invalid: 'PolkadotPrimitivesV6InvalidDisputeStatementKind'
        }
    },
    /**
     * Lookup362: polkadot_primitives::v6::ValidDisputeStatementKind
     **/
    PolkadotPrimitivesV6ValidDisputeStatementKind: {
        _enum: {
            Explicit: 'Null',
            BackingSeconded: 'H256',
            BackingValid: 'H256',
            ApprovalChecking: 'Null',
            ApprovalCheckingMultipleCandidates: 'Vec<H256>'
        }
    },
    /**
     * Lookup364: polkadot_primitives::v6::InvalidDisputeStatementKind
     **/
    PolkadotPrimitivesV6InvalidDisputeStatementKind: {
        _enum: ['Explicit']
    },
    /**
     * Lookup365: polkadot_runtime_parachains::paras::pallet::Call<T>
     **/
    PolkadotRuntimeParachainsParasPalletCall: {
        _enum: {
            force_set_current_code: {
                para: 'u32',
                newCode: 'Bytes',
            },
            force_set_current_head: {
                para: 'u32',
                newHead: 'Bytes',
            },
            force_schedule_code_upgrade: {
                para: 'u32',
                newCode: 'Bytes',
                relayParentNumber: 'u32',
            },
            force_note_new_head: {
                para: 'u32',
                newHead: 'Bytes',
            },
            force_queue_action: {
                para: 'u32',
            },
            add_trusted_validation_code: {
                validationCode: 'Bytes',
            },
            poke_unused_validation_code: {
                validationCodeHash: 'H256',
            },
            include_pvf_check_statement: {
                stmt: 'PolkadotPrimitivesV6PvfCheckStatement',
                signature: 'PolkadotPrimitivesV6ValidatorAppSignature',
            },
            force_set_most_recent_context: {
                para: 'u32',
                context: 'u32'
            }
        }
    },
    /**
     * Lookup366: polkadot_primitives::v6::PvfCheckStatement
     **/
    PolkadotPrimitivesV6PvfCheckStatement: {
        accept: 'bool',
        subject: 'H256',
        sessionIndex: 'u32',
        validatorIndex: 'u32'
    },
    /**
     * Lookup367: polkadot_runtime_parachains::initializer::pallet::Call<T>
     **/
    PolkadotRuntimeParachainsInitializerPalletCall: {
        _enum: {
            force_approve: {
                upTo: 'u32'
            }
        }
    },
    /**
     * Lookup368: polkadot_runtime_parachains::hrmp::pallet::Call<T>
     **/
    PolkadotRuntimeParachainsHrmpPalletCall: {
        _enum: {
            hrmp_init_open_channel: {
                recipient: 'u32',
                proposedMaxCapacity: 'u32',
                proposedMaxMessageSize: 'u32',
            },
            hrmp_accept_open_channel: {
                sender: 'u32',
            },
            hrmp_close_channel: {
                channelId: 'PolkadotParachainPrimitivesPrimitivesHrmpChannelId',
            },
            force_clean_hrmp: {
                para: 'u32',
                numInbound: 'u32',
                numOutbound: 'u32',
            },
            force_process_hrmp_open: {
                channels: 'u32',
            },
            force_process_hrmp_close: {
                channels: 'u32',
            },
            hrmp_cancel_open_request: {
                channelId: 'PolkadotParachainPrimitivesPrimitivesHrmpChannelId',
                openRequests: 'u32',
            },
            force_open_hrmp_channel: {
                sender: 'u32',
                recipient: 'u32',
                maxCapacity: 'u32',
                maxMessageSize: 'u32',
            },
            establish_system_channel: {
                sender: 'u32',
                recipient: 'u32',
            },
            poke_channel_deposits: {
                sender: 'u32',
                recipient: 'u32',
            },
            establish_channel_with_system: {
                targetSystemChain: 'u32'
            }
        }
    },
    /**
     * Lookup369: polkadot_parachain_primitives::primitives::HrmpChannelId
     **/
    PolkadotParachainPrimitivesPrimitivesHrmpChannelId: {
        sender: 'u32',
        recipient: 'u32'
    },
    /**
     * Lookup370: polkadot_runtime_parachains::disputes::pallet::Call<T>
     **/
    PolkadotRuntimeParachainsDisputesPalletCall: {
        _enum: ['force_unfreeze']
    },
    /**
     * Lookup371: polkadot_runtime_parachains::disputes::slashing::pallet::Call<T>
     **/
    PolkadotRuntimeParachainsDisputesSlashingPalletCall: {
        _enum: {
            report_dispute_lost_unsigned: {
                disputeProof: 'PolkadotPrimitivesV6SlashingDisputeProof',
                keyOwnerProof: 'SpSessionMembershipProof'
            }
        }
    },
    /**
     * Lookup372: polkadot_primitives::v6::slashing::DisputeProof
     **/
    PolkadotPrimitivesV6SlashingDisputeProof: {
        timeSlot: 'PolkadotPrimitivesV6SlashingDisputesTimeSlot',
        kind: 'PolkadotPrimitivesV6SlashingSlashingOffenceKind',
        validatorIndex: 'u32',
        validatorId: 'PolkadotPrimitivesV6ValidatorAppPublic'
    },
    /**
     * Lookup373: polkadot_primitives::v6::slashing::DisputesTimeSlot
     **/
    PolkadotPrimitivesV6SlashingDisputesTimeSlot: {
        sessionIndex: 'u32',
        candidateHash: 'H256'
    },
    /**
     * Lookup374: polkadot_primitives::v6::slashing::SlashingOffenceKind
     **/
    PolkadotPrimitivesV6SlashingSlashingOffenceKind: {
        _enum: ['ForInvalid', 'AgainstValid']
    },
    /**
     * Lookup375: polkadot_runtime_common::paras_registrar::pallet::Call<T>
     **/
    PolkadotRuntimeCommonParasRegistrarPalletCall: {
        _enum: {
            register: {
                id: 'u32',
                genesisHead: 'Bytes',
                validationCode: 'Bytes',
            },
            force_register: {
                who: 'AccountId32',
                deposit: 'u128',
                id: 'u32',
                genesisHead: 'Bytes',
                validationCode: 'Bytes',
            },
            deregister: {
                id: 'u32',
            },
            swap: {
                id: 'u32',
                other: 'u32',
            },
            remove_lock: {
                para: 'u32',
            },
            reserve: 'Null',
            add_lock: {
                para: 'u32',
            },
            schedule_code_upgrade: {
                para: 'u32',
                newCode: 'Bytes',
            },
            set_current_head: {
                para: 'u32',
                newHead: 'Bytes'
            }
        }
    },
    /**
     * Lookup376: polkadot_runtime_common::slots::pallet::Call<T>
     **/
    PolkadotRuntimeCommonSlotsPalletCall: {
        _enum: {
            force_lease: {
                para: 'u32',
                leaser: 'AccountId32',
                amount: 'u128',
                periodBegin: 'u32',
                periodCount: 'u32',
            },
            clear_all_leases: {
                para: 'u32',
            },
            trigger_onboard: {
                para: 'u32'
            }
        }
    },
    /**
     * Lookup377: polkadot_runtime_common::auctions::pallet::Call<T>
     **/
    PolkadotRuntimeCommonAuctionsPalletCall: {
        _enum: {
            new_auction: {
                duration: 'Compact<u32>',
                leasePeriodIndex: 'Compact<u32>',
            },
            bid: {
                para: 'Compact<u32>',
                auctionIndex: 'Compact<u32>',
                firstSlot: 'Compact<u32>',
                lastSlot: 'Compact<u32>',
                amount: 'Compact<u128>',
            },
            cancel_auction: 'Null'
        }
    },
    /**
     * Lookup379: polkadot_runtime_common::crowdloan::pallet::Call<T>
     **/
    PolkadotRuntimeCommonCrowdloanPalletCall: {
        _enum: {
            create: {
                index: 'Compact<u32>',
                cap: 'Compact<u128>',
                firstPeriod: 'Compact<u32>',
                lastPeriod: 'Compact<u32>',
                end: 'Compact<u32>',
                verifier: 'Option<SpRuntimeMultiSigner>',
            },
            contribute: {
                index: 'Compact<u32>',
                value: 'Compact<u128>',
                signature: 'Option<SpRuntimeMultiSignature>',
            },
            withdraw: {
                who: 'AccountId32',
                index: 'Compact<u32>',
            },
            refund: {
                index: 'Compact<u32>',
            },
            dissolve: {
                index: 'Compact<u32>',
            },
            edit: {
                index: 'Compact<u32>',
                cap: 'Compact<u128>',
                firstPeriod: 'Compact<u32>',
                lastPeriod: 'Compact<u32>',
                end: 'Compact<u32>',
                verifier: 'Option<SpRuntimeMultiSigner>',
            },
            add_memo: {
                index: 'u32',
                memo: 'Bytes',
            },
            poke: {
                index: 'u32',
            },
            contribute_all: {
                index: 'Compact<u32>',
                signature: 'Option<SpRuntimeMultiSignature>'
            }
        }
    },
    /**
     * Lookup381: sp_runtime::MultiSigner
     **/
    SpRuntimeMultiSigner: {
        _enum: {
            Ed25519: 'SpCoreEd25519Public',
            Sr25519: 'SpCoreSr25519Public',
            Ecdsa: 'SpCoreEcdsaPublic'
        }
    },
    /**
     * Lookup388: pallet_xcm::pallet::Call<T>
     **/
    PalletXcmCall: {
        _enum: {
            send: {
                dest: 'XcmVersionedLocation',
                message: 'XcmVersionedXcm',
            },
            teleport_assets: {
                dest: 'XcmVersionedLocation',
                beneficiary: 'XcmVersionedLocation',
                assets: 'XcmVersionedAssets',
                feeAssetItem: 'u32',
            },
            reserve_transfer_assets: {
                dest: 'XcmVersionedLocation',
                beneficiary: 'XcmVersionedLocation',
                assets: 'XcmVersionedAssets',
                feeAssetItem: 'u32',
            },
            execute: {
                message: 'XcmVersionedXcm',
                maxWeight: 'SpWeightsWeightV2Weight',
            },
            force_xcm_version: {
                location: 'StagingXcmV4Location',
                version: 'u32',
            },
            force_default_xcm_version: {
                maybeXcmVersion: 'Option<u32>',
            },
            force_subscribe_version_notify: {
                location: 'XcmVersionedLocation',
            },
            force_unsubscribe_version_notify: {
                location: 'XcmVersionedLocation',
            },
            limited_reserve_transfer_assets: {
                dest: 'XcmVersionedLocation',
                beneficiary: 'XcmVersionedLocation',
                assets: 'XcmVersionedAssets',
                feeAssetItem: 'u32',
                weightLimit: 'XcmV3WeightLimit',
            },
            limited_teleport_assets: {
                dest: 'XcmVersionedLocation',
                beneficiary: 'XcmVersionedLocation',
                assets: 'XcmVersionedAssets',
                feeAssetItem: 'u32',
                weightLimit: 'XcmV3WeightLimit',
            },
            force_suspension: {
                suspended: 'bool',
            },
            transfer_assets: {
                dest: 'XcmVersionedLocation',
                beneficiary: 'XcmVersionedLocation',
                assets: 'XcmVersionedAssets',
                feeAssetItem: 'u32',
                weightLimit: 'XcmV3WeightLimit',
            },
            claim_assets: {
                assets: 'XcmVersionedAssets',
                beneficiary: 'XcmVersionedLocation',
            },
            transfer_assets_using_type_and_then: {
                dest: 'XcmVersionedLocation',
                assets: 'XcmVersionedAssets',
                assetsTransferType: 'StagingXcmExecutorAssetTransferTransferType',
                remoteFeesId: 'XcmVersionedAssetId',
                feesTransferType: 'StagingXcmExecutorAssetTransferTransferType',
                customXcmOnDest: 'XcmVersionedXcm',
                weightLimit: 'XcmV3WeightLimit'
            }
        }
    },
    /**
     * Lookup389: xcm::VersionedXcm<RuntimeCall>
     **/
    XcmVersionedXcm: {
        _enum: {
            __Unused0: 'Null',
            __Unused1: 'Null',
            V2: 'XcmV2Xcm',
            V3: 'XcmV3Xcm',
            V4: 'StagingXcmV4Xcm'
        }
    },
    /**
     * Lookup390: xcm::v2::Xcm<RuntimeCall>
     **/
    XcmV2Xcm: 'Vec<XcmV2Instruction>',
    /**
     * Lookup392: xcm::v2::Instruction<RuntimeCall>
     **/
    XcmV2Instruction: {
        _enum: {
            WithdrawAsset: 'XcmV2MultiassetMultiAssets',
            ReserveAssetDeposited: 'XcmV2MultiassetMultiAssets',
            ReceiveTeleportedAsset: 'XcmV2MultiassetMultiAssets',
            QueryResponse: {
                queryId: 'Compact<u64>',
                response: 'XcmV2Response',
                maxWeight: 'Compact<u64>',
            },
            TransferAsset: {
                assets: 'XcmV2MultiassetMultiAssets',
                beneficiary: 'XcmV2MultiLocation',
            },
            TransferReserveAsset: {
                assets: 'XcmV2MultiassetMultiAssets',
                dest: 'XcmV2MultiLocation',
                xcm: 'XcmV2Xcm',
            },
            Transact: {
                originType: 'XcmV2OriginKind',
                requireWeightAtMost: 'Compact<u64>',
                call: 'XcmDoubleEncoded',
            },
            HrmpNewChannelOpenRequest: {
                sender: 'Compact<u32>',
                maxMessageSize: 'Compact<u32>',
                maxCapacity: 'Compact<u32>',
            },
            HrmpChannelAccepted: {
                recipient: 'Compact<u32>',
            },
            HrmpChannelClosing: {
                initiator: 'Compact<u32>',
                sender: 'Compact<u32>',
                recipient: 'Compact<u32>',
            },
            ClearOrigin: 'Null',
            DescendOrigin: 'XcmV2MultilocationJunctions',
            ReportError: {
                queryId: 'Compact<u64>',
                dest: 'XcmV2MultiLocation',
                maxResponseWeight: 'Compact<u64>',
            },
            DepositAsset: {
                assets: 'XcmV2MultiassetMultiAssetFilter',
                maxAssets: 'Compact<u32>',
                beneficiary: 'XcmV2MultiLocation',
            },
            DepositReserveAsset: {
                assets: 'XcmV2MultiassetMultiAssetFilter',
                maxAssets: 'Compact<u32>',
                dest: 'XcmV2MultiLocation',
                xcm: 'XcmV2Xcm',
            },
            ExchangeAsset: {
                give: 'XcmV2MultiassetMultiAssetFilter',
                receive: 'XcmV2MultiassetMultiAssets',
            },
            InitiateReserveWithdraw: {
                assets: 'XcmV2MultiassetMultiAssetFilter',
                reserve: 'XcmV2MultiLocation',
                xcm: 'XcmV2Xcm',
            },
            InitiateTeleport: {
                assets: 'XcmV2MultiassetMultiAssetFilter',
                dest: 'XcmV2MultiLocation',
                xcm: 'XcmV2Xcm',
            },
            QueryHolding: {
                queryId: 'Compact<u64>',
                dest: 'XcmV2MultiLocation',
                assets: 'XcmV2MultiassetMultiAssetFilter',
                maxResponseWeight: 'Compact<u64>',
            },
            BuyExecution: {
                fees: 'XcmV2MultiAsset',
                weightLimit: 'XcmV2WeightLimit',
            },
            RefundSurplus: 'Null',
            SetErrorHandler: 'XcmV2Xcm',
            SetAppendix: 'XcmV2Xcm',
            ClearError: 'Null',
            ClaimAsset: {
                assets: 'XcmV2MultiassetMultiAssets',
                ticket: 'XcmV2MultiLocation',
            },
            Trap: 'Compact<u64>',
            SubscribeVersion: {
                queryId: 'Compact<u64>',
                maxResponseWeight: 'Compact<u64>',
            },
            UnsubscribeVersion: 'Null'
        }
    },
    /**
     * Lookup393: xcm::v2::multiasset::MultiAssets
     **/
    XcmV2MultiassetMultiAssets: 'Vec<XcmV2MultiAsset>',
    /**
     * Lookup395: xcm::v2::multiasset::MultiAsset
     **/
    XcmV2MultiAsset: {
        id: 'XcmV2MultiassetAssetId',
        fun: 'XcmV2MultiassetFungibility'
    },
    /**
     * Lookup396: xcm::v2::multiasset::AssetId
     **/
    XcmV2MultiassetAssetId: {
        _enum: {
            Concrete: 'XcmV2MultiLocation',
            Abstract: 'Bytes'
        }
    },
    /**
     * Lookup397: xcm::v2::multiasset::Fungibility
     **/
    XcmV2MultiassetFungibility: {
        _enum: {
            Fungible: 'Compact<u128>',
            NonFungible: 'XcmV2MultiassetAssetInstance'
        }
    },
    /**
     * Lookup398: xcm::v2::multiasset::AssetInstance
     **/
    XcmV2MultiassetAssetInstance: {
        _enum: {
            Undefined: 'Null',
            Index: 'Compact<u128>',
            Array4: '[u8;4]',
            Array8: '[u8;8]',
            Array16: '[u8;16]',
            Array32: '[u8;32]',
            Blob: 'Bytes'
        }
    },
    /**
     * Lookup399: xcm::v2::Response
     **/
    XcmV2Response: {
        _enum: {
            Null: 'Null',
            Assets: 'XcmV2MultiassetMultiAssets',
            ExecutionResult: 'Option<(u32,XcmV2TraitsError)>',
            Version: 'u32'
        }
    },
    /**
     * Lookup402: xcm::v2::traits::Error
     **/
    XcmV2TraitsError: {
        _enum: {
            Overflow: 'Null',
            Unimplemented: 'Null',
            UntrustedReserveLocation: 'Null',
            UntrustedTeleportLocation: 'Null',
            MultiLocationFull: 'Null',
            MultiLocationNotInvertible: 'Null',
            BadOrigin: 'Null',
            InvalidLocation: 'Null',
            AssetNotFound: 'Null',
            FailedToTransactAsset: 'Null',
            NotWithdrawable: 'Null',
            LocationCannotHold: 'Null',
            ExceedsMaxMessageSize: 'Null',
            DestinationUnsupported: 'Null',
            Transport: 'Null',
            Unroutable: 'Null',
            UnknownClaim: 'Null',
            FailedToDecode: 'Null',
            MaxWeightInvalid: 'Null',
            NotHoldingFees: 'Null',
            TooExpensive: 'Null',
            Trap: 'u64',
            UnhandledXcmVersion: 'Null',
            WeightLimitReached: 'u64',
            Barrier: 'Null',
            WeightNotComputable: 'Null'
        }
    },
    /**
     * Lookup403: xcm::v2::OriginKind
     **/
    XcmV2OriginKind: {
        _enum: ['Native', 'SovereignAccount', 'Superuser', 'Xcm']
    },
    /**
     * Lookup404: xcm::double_encoded::DoubleEncoded<T>
     **/
    XcmDoubleEncoded: {
        encoded: 'Bytes'
    },
    /**
     * Lookup405: xcm::v2::multiasset::MultiAssetFilter
     **/
    XcmV2MultiassetMultiAssetFilter: {
        _enum: {
            Definite: 'XcmV2MultiassetMultiAssets',
            Wild: 'XcmV2MultiassetWildMultiAsset'
        }
    },
    /**
     * Lookup406: xcm::v2::multiasset::WildMultiAsset
     **/
    XcmV2MultiassetWildMultiAsset: {
        _enum: {
            All: 'Null',
            AllOf: {
                id: 'XcmV2MultiassetAssetId',
                fun: 'XcmV2MultiassetWildFungibility'
            }
        }
    },
    /**
     * Lookup407: xcm::v2::multiasset::WildFungibility
     **/
    XcmV2MultiassetWildFungibility: {
        _enum: ['Fungible', 'NonFungible']
    },
    /**
     * Lookup408: xcm::v2::WeightLimit
     **/
    XcmV2WeightLimit: {
        _enum: {
            Unlimited: 'Null',
            Limited: 'Compact<u64>'
        }
    },
    /**
     * Lookup409: xcm::v3::Xcm<Call>
     **/
    XcmV3Xcm: 'Vec<XcmV3Instruction>',
    /**
     * Lookup411: xcm::v3::Instruction<Call>
     **/
    XcmV3Instruction: {
        _enum: {
            WithdrawAsset: 'XcmV3MultiassetMultiAssets',
            ReserveAssetDeposited: 'XcmV3MultiassetMultiAssets',
            ReceiveTeleportedAsset: 'XcmV3MultiassetMultiAssets',
            QueryResponse: {
                queryId: 'Compact<u64>',
                response: 'XcmV3Response',
                maxWeight: 'SpWeightsWeightV2Weight',
                querier: 'Option<StagingXcmV3MultiLocation>',
            },
            TransferAsset: {
                assets: 'XcmV3MultiassetMultiAssets',
                beneficiary: 'StagingXcmV3MultiLocation',
            },
            TransferReserveAsset: {
                assets: 'XcmV3MultiassetMultiAssets',
                dest: 'StagingXcmV3MultiLocation',
                xcm: 'XcmV3Xcm',
            },
            Transact: {
                originKind: 'XcmV2OriginKind',
                requireWeightAtMost: 'SpWeightsWeightV2Weight',
                call: 'XcmDoubleEncoded',
            },
            HrmpNewChannelOpenRequest: {
                sender: 'Compact<u32>',
                maxMessageSize: 'Compact<u32>',
                maxCapacity: 'Compact<u32>',
            },
            HrmpChannelAccepted: {
                recipient: 'Compact<u32>',
            },
            HrmpChannelClosing: {
                initiator: 'Compact<u32>',
                sender: 'Compact<u32>',
                recipient: 'Compact<u32>',
            },
            ClearOrigin: 'Null',
            DescendOrigin: 'XcmV3Junctions',
            ReportError: 'XcmV3QueryResponseInfo',
            DepositAsset: {
                assets: 'XcmV3MultiassetMultiAssetFilter',
                beneficiary: 'StagingXcmV3MultiLocation',
            },
            DepositReserveAsset: {
                assets: 'XcmV3MultiassetMultiAssetFilter',
                dest: 'StagingXcmV3MultiLocation',
                xcm: 'XcmV3Xcm',
            },
            ExchangeAsset: {
                give: 'XcmV3MultiassetMultiAssetFilter',
                want: 'XcmV3MultiassetMultiAssets',
                maximal: 'bool',
            },
            InitiateReserveWithdraw: {
                assets: 'XcmV3MultiassetMultiAssetFilter',
                reserve: 'StagingXcmV3MultiLocation',
                xcm: 'XcmV3Xcm',
            },
            InitiateTeleport: {
                assets: 'XcmV3MultiassetMultiAssetFilter',
                dest: 'StagingXcmV3MultiLocation',
                xcm: 'XcmV3Xcm',
            },
            ReportHolding: {
                responseInfo: 'XcmV3QueryResponseInfo',
                assets: 'XcmV3MultiassetMultiAssetFilter',
            },
            BuyExecution: {
                fees: 'XcmV3MultiAsset',
                weightLimit: 'XcmV3WeightLimit',
            },
            RefundSurplus: 'Null',
            SetErrorHandler: 'XcmV3Xcm',
            SetAppendix: 'XcmV3Xcm',
            ClearError: 'Null',
            ClaimAsset: {
                assets: 'XcmV3MultiassetMultiAssets',
                ticket: 'StagingXcmV3MultiLocation',
            },
            Trap: 'Compact<u64>',
            SubscribeVersion: {
                queryId: 'Compact<u64>',
                maxResponseWeight: 'SpWeightsWeightV2Weight',
            },
            UnsubscribeVersion: 'Null',
            BurnAsset: 'XcmV3MultiassetMultiAssets',
            ExpectAsset: 'XcmV3MultiassetMultiAssets',
            ExpectOrigin: 'Option<StagingXcmV3MultiLocation>',
            ExpectError: 'Option<(u32,XcmV3TraitsError)>',
            ExpectTransactStatus: 'XcmV3MaybeErrorCode',
            QueryPallet: {
                moduleName: 'Bytes',
                responseInfo: 'XcmV3QueryResponseInfo',
            },
            ExpectPallet: {
                index: 'Compact<u32>',
                name: 'Bytes',
                moduleName: 'Bytes',
                crateMajor: 'Compact<u32>',
                minCrateMinor: 'Compact<u32>',
            },
            ReportTransactStatus: 'XcmV3QueryResponseInfo',
            ClearTransactStatus: 'Null',
            UniversalOrigin: 'XcmV3Junction',
            ExportMessage: {
                network: 'XcmV3JunctionNetworkId',
                destination: 'XcmV3Junctions',
                xcm: 'XcmV3Xcm',
            },
            LockAsset: {
                asset: 'XcmV3MultiAsset',
                unlocker: 'StagingXcmV3MultiLocation',
            },
            UnlockAsset: {
                asset: 'XcmV3MultiAsset',
                target: 'StagingXcmV3MultiLocation',
            },
            NoteUnlockable: {
                asset: 'XcmV3MultiAsset',
                owner: 'StagingXcmV3MultiLocation',
            },
            RequestUnlock: {
                asset: 'XcmV3MultiAsset',
                locker: 'StagingXcmV3MultiLocation',
            },
            SetFeesMode: {
                jitWithdraw: 'bool',
            },
            SetTopic: '[u8;32]',
            ClearTopic: 'Null',
            AliasOrigin: 'StagingXcmV3MultiLocation',
            UnpaidExecution: {
                weightLimit: 'XcmV3WeightLimit',
                checkOrigin: 'Option<StagingXcmV3MultiLocation>'
            }
        }
    },
    /**
     * Lookup412: xcm::v3::multiasset::MultiAssets
     **/
    XcmV3MultiassetMultiAssets: 'Vec<XcmV3MultiAsset>',
    /**
     * Lookup414: xcm::v3::multiasset::MultiAsset
     **/
    XcmV3MultiAsset: {
        id: 'XcmV3MultiassetAssetId',
        fun: 'XcmV3MultiassetFungibility'
    },
    /**
     * Lookup415: xcm::v3::multiasset::Fungibility
     **/
    XcmV3MultiassetFungibility: {
        _enum: {
            Fungible: 'Compact<u128>',
            NonFungible: 'XcmV3MultiassetAssetInstance'
        }
    },
    /**
     * Lookup416: xcm::v3::multiasset::AssetInstance
     **/
    XcmV3MultiassetAssetInstance: {
        _enum: {
            Undefined: 'Null',
            Index: 'Compact<u128>',
            Array4: '[u8;4]',
            Array8: '[u8;8]',
            Array16: '[u8;16]',
            Array32: '[u8;32]'
        }
    },
    /**
     * Lookup417: xcm::v3::Response
     **/
    XcmV3Response: {
        _enum: {
            Null: 'Null',
            Assets: 'XcmV3MultiassetMultiAssets',
            ExecutionResult: 'Option<(u32,XcmV3TraitsError)>',
            Version: 'u32',
            PalletsInfo: 'Vec<XcmV3PalletInfo>',
            DispatchResult: 'XcmV3MaybeErrorCode'
        }
    },
    /**
     * Lookup420: xcm::v3::traits::Error
     **/
    XcmV3TraitsError: {
        _enum: {
            Overflow: 'Null',
            Unimplemented: 'Null',
            UntrustedReserveLocation: 'Null',
            UntrustedTeleportLocation: 'Null',
            LocationFull: 'Null',
            LocationNotInvertible: 'Null',
            BadOrigin: 'Null',
            InvalidLocation: 'Null',
            AssetNotFound: 'Null',
            FailedToTransactAsset: 'Null',
            NotWithdrawable: 'Null',
            LocationCannotHold: 'Null',
            ExceedsMaxMessageSize: 'Null',
            DestinationUnsupported: 'Null',
            Transport: 'Null',
            Unroutable: 'Null',
            UnknownClaim: 'Null',
            FailedToDecode: 'Null',
            MaxWeightInvalid: 'Null',
            NotHoldingFees: 'Null',
            TooExpensive: 'Null',
            Trap: 'u64',
            ExpectationFalse: 'Null',
            PalletNotFound: 'Null',
            NameMismatch: 'Null',
            VersionIncompatible: 'Null',
            HoldingWouldOverflow: 'Null',
            ExportError: 'Null',
            ReanchorFailed: 'Null',
            NoDeal: 'Null',
            FeesNotMet: 'Null',
            LockError: 'Null',
            NoPermission: 'Null',
            Unanchored: 'Null',
            NotDepositable: 'Null',
            UnhandledXcmVersion: 'Null',
            WeightLimitReached: 'SpWeightsWeightV2Weight',
            Barrier: 'Null',
            WeightNotComputable: 'Null',
            ExceedsStackLimit: 'Null'
        }
    },
    /**
     * Lookup422: xcm::v3::PalletInfo
     **/
    XcmV3PalletInfo: {
        index: 'Compact<u32>',
        name: 'Bytes',
        moduleName: 'Bytes',
        major: 'Compact<u32>',
        minor: 'Compact<u32>',
        patch: 'Compact<u32>'
    },
    /**
     * Lookup425: xcm::v3::MaybeErrorCode
     **/
    XcmV3MaybeErrorCode: {
        _enum: {
            Success: 'Null',
            Error: 'Bytes',
            TruncatedError: 'Bytes'
        }
    },
    /**
     * Lookup428: xcm::v3::QueryResponseInfo
     **/
    XcmV3QueryResponseInfo: {
        destination: 'StagingXcmV3MultiLocation',
        queryId: 'Compact<u64>',
        maxWeight: 'SpWeightsWeightV2Weight'
    },
    /**
     * Lookup429: xcm::v3::multiasset::MultiAssetFilter
     **/
    XcmV3MultiassetMultiAssetFilter: {
        _enum: {
            Definite: 'XcmV3MultiassetMultiAssets',
            Wild: 'XcmV3MultiassetWildMultiAsset'
        }
    },
    /**
     * Lookup430: xcm::v3::multiasset::WildMultiAsset
     **/
    XcmV3MultiassetWildMultiAsset: {
        _enum: {
            All: 'Null',
            AllOf: {
                id: 'XcmV3MultiassetAssetId',
                fun: 'XcmV3MultiassetWildFungibility',
            },
            AllCounted: 'Compact<u32>',
            AllOfCounted: {
                id: 'XcmV3MultiassetAssetId',
                fun: 'XcmV3MultiassetWildFungibility',
                count: 'Compact<u32>'
            }
        }
    },
    /**
     * Lookup431: xcm::v3::multiasset::WildFungibility
     **/
    XcmV3MultiassetWildFungibility: {
        _enum: ['Fungible', 'NonFungible']
    },
    /**
     * Lookup432: xcm::v3::WeightLimit
     **/
    XcmV3WeightLimit: {
        _enum: {
            Unlimited: 'Null',
            Limited: 'SpWeightsWeightV2Weight'
        }
    },
    /**
     * Lookup433: staging_xcm::v4::Xcm<Call>
     **/
    StagingXcmV4Xcm: 'Vec<StagingXcmV4Instruction>',
    /**
     * Lookup435: staging_xcm::v4::Instruction<Call>
     **/
    StagingXcmV4Instruction: {
        _enum: {
            WithdrawAsset: 'StagingXcmV4AssetAssets',
            ReserveAssetDeposited: 'StagingXcmV4AssetAssets',
            ReceiveTeleportedAsset: 'StagingXcmV4AssetAssets',
            QueryResponse: {
                queryId: 'Compact<u64>',
                response: 'StagingXcmV4Response',
                maxWeight: 'SpWeightsWeightV2Weight',
                querier: 'Option<StagingXcmV4Location>',
            },
            TransferAsset: {
                assets: 'StagingXcmV4AssetAssets',
                beneficiary: 'StagingXcmV4Location',
            },
            TransferReserveAsset: {
                assets: 'StagingXcmV4AssetAssets',
                dest: 'StagingXcmV4Location',
                xcm: 'StagingXcmV4Xcm',
            },
            Transact: {
                originKind: 'XcmV2OriginKind',
                requireWeightAtMost: 'SpWeightsWeightV2Weight',
                call: 'XcmDoubleEncoded',
            },
            HrmpNewChannelOpenRequest: {
                sender: 'Compact<u32>',
                maxMessageSize: 'Compact<u32>',
                maxCapacity: 'Compact<u32>',
            },
            HrmpChannelAccepted: {
                recipient: 'Compact<u32>',
            },
            HrmpChannelClosing: {
                initiator: 'Compact<u32>',
                sender: 'Compact<u32>',
                recipient: 'Compact<u32>',
            },
            ClearOrigin: 'Null',
            DescendOrigin: 'StagingXcmV4Junctions',
            ReportError: 'StagingXcmV4QueryResponseInfo',
            DepositAsset: {
                assets: 'StagingXcmV4AssetAssetFilter',
                beneficiary: 'StagingXcmV4Location',
            },
            DepositReserveAsset: {
                assets: 'StagingXcmV4AssetAssetFilter',
                dest: 'StagingXcmV4Location',
                xcm: 'StagingXcmV4Xcm',
            },
            ExchangeAsset: {
                give: 'StagingXcmV4AssetAssetFilter',
                want: 'StagingXcmV4AssetAssets',
                maximal: 'bool',
            },
            InitiateReserveWithdraw: {
                assets: 'StagingXcmV4AssetAssetFilter',
                reserve: 'StagingXcmV4Location',
                xcm: 'StagingXcmV4Xcm',
            },
            InitiateTeleport: {
                assets: 'StagingXcmV4AssetAssetFilter',
                dest: 'StagingXcmV4Location',
                xcm: 'StagingXcmV4Xcm',
            },
            ReportHolding: {
                responseInfo: 'StagingXcmV4QueryResponseInfo',
                assets: 'StagingXcmV4AssetAssetFilter',
            },
            BuyExecution: {
                fees: 'StagingXcmV4Asset',
                weightLimit: 'XcmV3WeightLimit',
            },
            RefundSurplus: 'Null',
            SetErrorHandler: 'StagingXcmV4Xcm',
            SetAppendix: 'StagingXcmV4Xcm',
            ClearError: 'Null',
            ClaimAsset: {
                assets: 'StagingXcmV4AssetAssets',
                ticket: 'StagingXcmV4Location',
            },
            Trap: 'Compact<u64>',
            SubscribeVersion: {
                queryId: 'Compact<u64>',
                maxResponseWeight: 'SpWeightsWeightV2Weight',
            },
            UnsubscribeVersion: 'Null',
            BurnAsset: 'StagingXcmV4AssetAssets',
            ExpectAsset: 'StagingXcmV4AssetAssets',
            ExpectOrigin: 'Option<StagingXcmV4Location>',
            ExpectError: 'Option<(u32,XcmV3TraitsError)>',
            ExpectTransactStatus: 'XcmV3MaybeErrorCode',
            QueryPallet: {
                moduleName: 'Bytes',
                responseInfo: 'StagingXcmV4QueryResponseInfo',
            },
            ExpectPallet: {
                index: 'Compact<u32>',
                name: 'Bytes',
                moduleName: 'Bytes',
                crateMajor: 'Compact<u32>',
                minCrateMinor: 'Compact<u32>',
            },
            ReportTransactStatus: 'StagingXcmV4QueryResponseInfo',
            ClearTransactStatus: 'Null',
            UniversalOrigin: 'StagingXcmV4Junction',
            ExportMessage: {
                network: 'StagingXcmV4JunctionNetworkId',
                destination: 'StagingXcmV4Junctions',
                xcm: 'StagingXcmV4Xcm',
            },
            LockAsset: {
                asset: 'StagingXcmV4Asset',
                unlocker: 'StagingXcmV4Location',
            },
            UnlockAsset: {
                asset: 'StagingXcmV4Asset',
                target: 'StagingXcmV4Location',
            },
            NoteUnlockable: {
                asset: 'StagingXcmV4Asset',
                owner: 'StagingXcmV4Location',
            },
            RequestUnlock: {
                asset: 'StagingXcmV4Asset',
                locker: 'StagingXcmV4Location',
            },
            SetFeesMode: {
                jitWithdraw: 'bool',
            },
            SetTopic: '[u8;32]',
            ClearTopic: 'Null',
            AliasOrigin: 'StagingXcmV4Location',
            UnpaidExecution: {
                weightLimit: 'XcmV3WeightLimit',
                checkOrigin: 'Option<StagingXcmV4Location>'
            }
        }
    },
    /**
     * Lookup436: staging_xcm::v4::asset::Assets
     **/
    StagingXcmV4AssetAssets: 'Vec<StagingXcmV4Asset>',
    /**
     * Lookup438: staging_xcm::v4::asset::Asset
     **/
    StagingXcmV4Asset: {
        id: 'StagingXcmV4AssetAssetId',
        fun: 'StagingXcmV4AssetFungibility'
    },
    /**
     * Lookup439: staging_xcm::v4::asset::Fungibility
     **/
    StagingXcmV4AssetFungibility: {
        _enum: {
            Fungible: 'Compact<u128>',
            NonFungible: 'StagingXcmV4AssetAssetInstance'
        }
    },
    /**
     * Lookup440: staging_xcm::v4::asset::AssetInstance
     **/
    StagingXcmV4AssetAssetInstance: {
        _enum: {
            Undefined: 'Null',
            Index: 'Compact<u128>',
            Array4: '[u8;4]',
            Array8: '[u8;8]',
            Array16: '[u8;16]',
            Array32: '[u8;32]'
        }
    },
    /**
     * Lookup441: staging_xcm::v4::Response
     **/
    StagingXcmV4Response: {
        _enum: {
            Null: 'Null',
            Assets: 'StagingXcmV4AssetAssets',
            ExecutionResult: 'Option<(u32,XcmV3TraitsError)>',
            Version: 'u32',
            PalletsInfo: 'Vec<StagingXcmV4PalletInfo>',
            DispatchResult: 'XcmV3MaybeErrorCode'
        }
    },
    /**
     * Lookup443: staging_xcm::v4::PalletInfo
     **/
    StagingXcmV4PalletInfo: {
        index: 'Compact<u32>',
        name: 'Bytes',
        moduleName: 'Bytes',
        major: 'Compact<u32>',
        minor: 'Compact<u32>',
        patch: 'Compact<u32>'
    },
    /**
     * Lookup447: staging_xcm::v4::QueryResponseInfo
     **/
    StagingXcmV4QueryResponseInfo: {
        destination: 'StagingXcmV4Location',
        queryId: 'Compact<u64>',
        maxWeight: 'SpWeightsWeightV2Weight'
    },
    /**
     * Lookup448: staging_xcm::v4::asset::AssetFilter
     **/
    StagingXcmV4AssetAssetFilter: {
        _enum: {
            Definite: 'StagingXcmV4AssetAssets',
            Wild: 'StagingXcmV4AssetWildAsset'
        }
    },
    /**
     * Lookup449: staging_xcm::v4::asset::WildAsset
     **/
    StagingXcmV4AssetWildAsset: {
        _enum: {
            All: 'Null',
            AllOf: {
                id: 'StagingXcmV4AssetAssetId',
                fun: 'StagingXcmV4AssetWildFungibility',
            },
            AllCounted: 'Compact<u32>',
            AllOfCounted: {
                id: 'StagingXcmV4AssetAssetId',
                fun: 'StagingXcmV4AssetWildFungibility',
                count: 'Compact<u32>'
            }
        }
    },
    /**
     * Lookup450: staging_xcm::v4::asset::WildFungibility
     **/
    StagingXcmV4AssetWildFungibility: {
        _enum: ['Fungible', 'NonFungible']
    },
    /**
     * Lookup451: xcm::VersionedAssets
     **/
    XcmVersionedAssets: {
        _enum: {
            __Unused0: 'Null',
            V2: 'XcmV2MultiassetMultiAssets',
            __Unused2: 'Null',
            V3: 'XcmV3MultiassetMultiAssets',
            V4: 'StagingXcmV4AssetAssets'
        }
    },
    /**
     * Lookup463: staging_xcm_executor::traits::asset_transfer::TransferType
     **/
    StagingXcmExecutorAssetTransferTransferType: {
        _enum: {
            Teleport: 'Null',
            LocalReserve: 'Null',
            DestinationReserve: 'Null',
            RemoteReserve: 'XcmVersionedLocation'
        }
    },
    /**
     * Lookup464: xcm::VersionedAssetId
     **/
    XcmVersionedAssetId: {
        _enum: {
            __Unused0: 'Null',
            __Unused1: 'Null',
            __Unused2: 'Null',
            V3: 'XcmV3MultiassetAssetId',
            V4: 'StagingXcmV4AssetAssetId'
        }
    },
    /**
     * Lookup466: polkadot_runtime_parachains::inclusion::AggregateMessageOrigin
     **/
    PolkadotRuntimeParachainsInclusionAggregateMessageOrigin: {
        _enum: {
            Ump: 'PolkadotRuntimeParachainsInclusionUmpQueueId'
        }
    },
    /**
     * Lookup467: polkadot_runtime_parachains::inclusion::UmpQueueId
     **/
    PolkadotRuntimeParachainsInclusionUmpQueueId: {
        _enum: {
            Para: 'u32'
        }
    },
    /**
     * Lookup471: sp_consensus_beefy::EquivocationProof<Number, sp_consensus_beefy::ecdsa_crypto::Public, sp_consensus_beefy::ecdsa_crypto::Signature>
     **/
    SpConsensusBeefyEquivocationProof: {
        first: 'SpConsensusBeefyVoteMessage',
        second: 'SpConsensusBeefyVoteMessage'
    },
    /**
     * Lookup486: polkadot_runtime_common::claims::pallet::Event<T>
     **/
    PolkadotRuntimeCommonClaimsPalletEvent: {
        _enum: {
            Claimed: {
                who: 'AccountId32',
                ethereumAddress: 'EthereumAddress',
                amount: 'u128'
            }
        }
    },
    /**
     * Lookup501: polkadot_runtime_parachains::inclusion::pallet::Event<T>
     **/
    PolkadotRuntimeParachainsInclusionPalletEvent: {
        _enum: {
            CandidateBacked: '(PolkadotPrimitivesV6CandidateReceipt,Bytes,u32,u32)',
            CandidateIncluded: '(PolkadotPrimitivesV6CandidateReceipt,Bytes,u32,u32)',
            CandidateTimedOut: '(PolkadotPrimitivesV6CandidateReceipt,Bytes,u32)',
            UpwardMessagesReceived: {
                from: 'u32',
                count: 'u32'
            }
        }
    },
    /**
     * Lookup502: polkadot_primitives::v6::CandidateReceipt<primitive_types::H256>
     **/
    PolkadotPrimitivesV6CandidateReceipt: {
        descriptor: 'PolkadotPrimitivesV6CandidateDescriptor',
        commitmentsHash: 'H256'
    },
    /**
     * Lookup505: polkadot_runtime_parachains::paras::pallet::Event
     **/
    PolkadotRuntimeParachainsParasPalletEvent: {
        _enum: {
            CurrentCodeUpdated: 'u32',
            CurrentHeadUpdated: 'u32',
            CodeUpgradeScheduled: 'u32',
            NewHeadNoted: 'u32',
            ActionQueued: '(u32,u32)',
            PvfCheckStarted: '(H256,u32)',
            PvfCheckAccepted: '(H256,u32)',
            PvfCheckRejected: '(H256,u32)'
        }
    },
    /**
     * Lookup506: polkadot_runtime_parachains::hrmp::pallet::Event<T>
     **/
    PolkadotRuntimeParachainsHrmpPalletEvent: {
        _enum: {
            OpenChannelRequested: {
                sender: 'u32',
                recipient: 'u32',
                proposedMaxCapacity: 'u32',
                proposedMaxMessageSize: 'u32',
            },
            OpenChannelCanceled: {
                byParachain: 'u32',
                channelId: 'PolkadotParachainPrimitivesPrimitivesHrmpChannelId',
            },
            OpenChannelAccepted: {
                sender: 'u32',
                recipient: 'u32',
            },
            ChannelClosed: {
                byParachain: 'u32',
                channelId: 'PolkadotParachainPrimitivesPrimitivesHrmpChannelId',
            },
            HrmpChannelForceOpened: {
                sender: 'u32',
                recipient: 'u32',
                proposedMaxCapacity: 'u32',
                proposedMaxMessageSize: 'u32',
            },
            HrmpSystemChannelOpened: {
                sender: 'u32',
                recipient: 'u32',
                proposedMaxCapacity: 'u32',
                proposedMaxMessageSize: 'u32',
            },
            OpenChannelDepositsUpdated: {
                sender: 'u32',
                recipient: 'u32'
            }
        }
    },
    /**
     * Lookup507: polkadot_runtime_parachains::disputes::pallet::Event<T>
     **/
    PolkadotRuntimeParachainsDisputesPalletEvent: {
        _enum: {
            DisputeInitiated: '(H256,PolkadotRuntimeParachainsDisputesDisputeLocation)',
            DisputeConcluded: '(H256,PolkadotRuntimeParachainsDisputesDisputeResult)',
            Revert: 'u32'
        }
    },
    /**
     * Lookup508: polkadot_runtime_parachains::disputes::DisputeLocation
     **/
    PolkadotRuntimeParachainsDisputesDisputeLocation: {
        _enum: ['Local', 'Remote']
    },
    /**
     * Lookup509: polkadot_runtime_parachains::disputes::DisputeResult
     **/
    PolkadotRuntimeParachainsDisputesDisputeResult: {
        _enum: ['Valid', 'Invalid']
    },
    /**
     * Lookup510: polkadot_runtime_common::paras_registrar::pallet::Event<T>
     **/
    PolkadotRuntimeCommonParasRegistrarPalletEvent: {
        _enum: {
            Registered: {
                paraId: 'u32',
                manager: 'AccountId32',
            },
            Deregistered: {
                paraId: 'u32',
            },
            Reserved: {
                paraId: 'u32',
                who: 'AccountId32',
            },
            Swapped: {
                paraId: 'u32',
                otherId: 'u32'
            }
        }
    },
    /**
     * Lookup511: polkadot_runtime_common::slots::pallet::Event<T>
     **/
    PolkadotRuntimeCommonSlotsPalletEvent: {
        _enum: {
            NewLeasePeriod: {
                leasePeriod: 'u32',
            },
            Leased: {
                paraId: 'u32',
                leaser: 'AccountId32',
                periodBegin: 'u32',
                periodCount: 'u32',
                extraReserved: 'u128',
                totalAmount: 'u128'
            }
        }
    },
    /**
     * Lookup512: polkadot_runtime_common::auctions::pallet::Event<T>
     **/
    PolkadotRuntimeCommonAuctionsPalletEvent: {
        _enum: {
            AuctionStarted: {
                auctionIndex: 'u32',
                leasePeriod: 'u32',
                ending: 'u32',
            },
            AuctionClosed: {
                auctionIndex: 'u32',
            },
            Reserved: {
                bidder: 'AccountId32',
                extraReserved: 'u128',
                totalAmount: 'u128',
            },
            Unreserved: {
                bidder: 'AccountId32',
                amount: 'u128',
            },
            ReserveConfiscated: {
                paraId: 'u32',
                leaser: 'AccountId32',
                amount: 'u128',
            },
            BidAccepted: {
                bidder: 'AccountId32',
                paraId: 'u32',
                amount: 'u128',
                firstSlot: 'u32',
                lastSlot: 'u32',
            },
            WinningOffset: {
                auctionIndex: 'u32',
                blockNumber: 'u32'
            }
        }
    },
    /**
     * Lookup513: polkadot_runtime_common::crowdloan::pallet::Event<T>
     **/
    PolkadotRuntimeCommonCrowdloanPalletEvent: {
        _enum: {
            Created: {
                paraId: 'u32',
            },
            Contributed: {
                who: 'AccountId32',
                fundIndex: 'u32',
                amount: 'u128',
            },
            Withdrew: {
                who: 'AccountId32',
                fundIndex: 'u32',
                amount: 'u128',
            },
            PartiallyRefunded: {
                paraId: 'u32',
            },
            AllRefunded: {
                paraId: 'u32',
            },
            Dissolved: {
                paraId: 'u32',
            },
            HandleBidResult: {
                paraId: 'u32',
                result: 'Result<Null, SpRuntimeDispatchError>',
            },
            Edited: {
                paraId: 'u32',
            },
            MemoUpdated: {
                who: 'AccountId32',
                paraId: 'u32',
                memo: 'Bytes',
            },
            AddedToNewRaise: {
                paraId: 'u32'
            }
        }
    },
    /**
     * Lookup517: pallet_xcm::pallet::Event<T>
     **/
    PalletXcmEvent: {
        _enum: {
            Attempted: {
                outcome: 'StagingXcmV4TraitsOutcome',
            },
            Sent: {
                origin: 'StagingXcmV4Location',
                destination: 'StagingXcmV4Location',
                message: 'StagingXcmV4Xcm',
                messageId: '[u8;32]',
            },
            UnexpectedResponse: {
                origin: 'StagingXcmV4Location',
                queryId: 'u64',
            },
            ResponseReady: {
                queryId: 'u64',
                response: 'StagingXcmV4Response',
            },
            Notified: {
                queryId: 'u64',
                palletIndex: 'u8',
                callIndex: 'u8',
            },
            NotifyOverweight: {
                queryId: 'u64',
                palletIndex: 'u8',
                callIndex: 'u8',
                actualWeight: 'SpWeightsWeightV2Weight',
                maxBudgetedWeight: 'SpWeightsWeightV2Weight',
            },
            NotifyDispatchError: {
                queryId: 'u64',
                palletIndex: 'u8',
                callIndex: 'u8',
            },
            NotifyDecodeFailed: {
                queryId: 'u64',
                palletIndex: 'u8',
                callIndex: 'u8',
            },
            InvalidResponder: {
                origin: 'StagingXcmV4Location',
                queryId: 'u64',
                expectedLocation: 'Option<StagingXcmV4Location>',
            },
            InvalidResponderVersion: {
                origin: 'StagingXcmV4Location',
                queryId: 'u64',
            },
            ResponseTaken: {
                queryId: 'u64',
            },
            AssetsTrapped: {
                _alias: {
                    hash_: 'hash',
                },
                hash_: 'H256',
                origin: 'StagingXcmV4Location',
                assets: 'XcmVersionedAssets',
            },
            VersionChangeNotified: {
                destination: 'StagingXcmV4Location',
                result: 'u32',
                cost: 'StagingXcmV4AssetAssets',
                messageId: '[u8;32]',
            },
            SupportedVersionChanged: {
                location: 'StagingXcmV4Location',
                version: 'u32',
            },
            NotifyTargetSendFail: {
                location: 'StagingXcmV4Location',
                queryId: 'u64',
                error: 'XcmV3TraitsError',
            },
            NotifyTargetMigrationFail: {
                location: 'XcmVersionedLocation',
                queryId: 'u64',
            },
            InvalidQuerierVersion: {
                origin: 'StagingXcmV4Location',
                queryId: 'u64',
            },
            InvalidQuerier: {
                origin: 'StagingXcmV4Location',
                queryId: 'u64',
                expectedQuerier: 'StagingXcmV4Location',
                maybeActualQuerier: 'Option<StagingXcmV4Location>',
            },
            VersionNotifyStarted: {
                destination: 'StagingXcmV4Location',
                cost: 'StagingXcmV4AssetAssets',
                messageId: '[u8;32]',
            },
            VersionNotifyRequested: {
                destination: 'StagingXcmV4Location',
                cost: 'StagingXcmV4AssetAssets',
                messageId: '[u8;32]',
            },
            VersionNotifyUnrequested: {
                destination: 'StagingXcmV4Location',
                cost: 'StagingXcmV4AssetAssets',
                messageId: '[u8;32]',
            },
            FeesPaid: {
                paying: 'StagingXcmV4Location',
                fees: 'StagingXcmV4AssetAssets',
            },
            AssetsClaimed: {
                _alias: {
                    hash_: 'hash',
                },
                hash_: 'H256',
                origin: 'StagingXcmV4Location',
                assets: 'XcmVersionedAssets',
            },
            VersionMigrationFinished: {
                version: 'u32'
            }
        }
    },
    /**
     * Lookup518: staging_xcm::v4::traits::Outcome
     **/
    StagingXcmV4TraitsOutcome: {
        _enum: {
            Complete: {
                used: 'SpWeightsWeightV2Weight',
            },
            Incomplete: {
                used: 'SpWeightsWeightV2Weight',
                error: 'XcmV3TraitsError',
            },
            Error: {
                error: 'XcmV3TraitsError'
            }
        }
    },
    /**
     * Lookup578: pallet_balances::types::IdAmount<polkadot_runtime::RuntimeHoldReason, Balance>
     **/
    PalletBalancesIdAmountRuntimeHoldReason: {
        id: 'PolkadotRuntimeRuntimeHoldReason',
        amount: 'u128'
    },
    /**
     * Lookup579: polkadot_runtime::RuntimeHoldReason
     **/
    PolkadotRuntimeRuntimeHoldReason: {
        _enum: {
            __Unused0: 'Null',
            __Unused1: 'Null',
            __Unused2: 'Null',
            __Unused3: 'Null',
            __Unused4: 'Null',
            __Unused5: 'Null',
            __Unused6: 'Null',
            __Unused7: 'Null',
            __Unused8: 'Null',
            __Unused9: 'Null',
            Preimage: 'PalletPreimageHoldReason',
            __Unused11: 'Null',
            __Unused12: 'Null',
            __Unused13: 'Null',
            __Unused14: 'Null',
            __Unused15: 'Null',
            __Unused16: 'Null',
            __Unused17: 'Null',
            __Unused18: 'Null',
            __Unused19: 'Null',
            __Unused20: 'Null',
            __Unused21: 'Null',
            __Unused22: 'Null',
            __Unused23: 'Null',
            __Unused24: 'Null',
            __Unused25: 'Null',
            __Unused26: 'Null',
            __Unused27: 'Null',
            __Unused28: 'Null',
            __Unused29: 'Null',
            __Unused30: 'Null',
            __Unused31: 'Null',
            __Unused32: 'Null',
            __Unused33: 'Null',
            __Unused34: 'Null',
            __Unused35: 'Null',
            __Unused36: 'Null',
            __Unused37: 'Null',
            __Unused38: 'Null',
            __Unused39: 'Null',
            __Unused40: 'Null',
            __Unused41: 'Null',
            __Unused42: 'Null',
            __Unused43: 'Null',
            __Unused44: 'Null',
            __Unused45: 'Null',
            __Unused46: 'Null',
            __Unused47: 'Null',
            __Unused48: 'Null',
            __Unused49: 'Null',
            __Unused50: 'Null',
            __Unused51: 'Null',
            __Unused52: 'Null',
            __Unused53: 'Null',
            __Unused54: 'Null',
            __Unused55: 'Null',
            __Unused56: 'Null',
            __Unused57: 'Null',
            __Unused58: 'Null',
            __Unused59: 'Null',
            __Unused60: 'Null',
            __Unused61: 'Null',
            __Unused62: 'Null',
            __Unused63: 'Null',
            __Unused64: 'Null',
            __Unused65: 'Null',
            __Unused66: 'Null',
            __Unused67: 'Null',
            __Unused68: 'Null',
            __Unused69: 'Null',
            __Unused70: 'Null',
            __Unused71: 'Null',
            __Unused72: 'Null',
            __Unused73: 'Null',
            __Unused74: 'Null',
            __Unused75: 'Null',
            __Unused76: 'Null',
            __Unused77: 'Null',
            __Unused78: 'Null',
            __Unused79: 'Null',
            __Unused80: 'Null',
            __Unused81: 'Null',
            __Unused82: 'Null',
            __Unused83: 'Null',
            __Unused84: 'Null',
            __Unused85: 'Null',
            __Unused86: 'Null',
            __Unused87: 'Null',
            __Unused88: 'Null',
            __Unused89: 'Null',
            __Unused90: 'Null',
            __Unused91: 'Null',
            __Unused92: 'Null',
            __Unused93: 'Null',
            __Unused94: 'Null',
            __Unused95: 'Null',
            __Unused96: 'Null',
            __Unused97: 'Null',
            StateTrieMigration: 'PalletStateTrieMigrationHoldReason'
        }
    },
    /**
     * Lookup584: pallet_balances::types::IdAmount<polkadot_runtime::RuntimeFreezeReason, Balance>
     **/
    PalletBalancesIdAmountRuntimeFreezeReason: {
        id: 'PolkadotRuntimeRuntimeFreezeReason',
        amount: 'u128'
    },
    /**
     * Lookup585: polkadot_runtime::RuntimeFreezeReason
     **/
    PolkadotRuntimeRuntimeFreezeReason: {
        _enum: {
            __Unused0: 'Null',
            __Unused1: 'Null',
            __Unused2: 'Null',
            __Unused3: 'Null',
            __Unused4: 'Null',
            __Unused5: 'Null',
            __Unused6: 'Null',
            __Unused7: 'Null',
            __Unused8: 'Null',
            __Unused9: 'Null',
            __Unused10: 'Null',
            __Unused11: 'Null',
            __Unused12: 'Null',
            __Unused13: 'Null',
            __Unused14: 'Null',
            __Unused15: 'Null',
            __Unused16: 'Null',
            __Unused17: 'Null',
            __Unused18: 'Null',
            __Unused19: 'Null',
            __Unused20: 'Null',
            __Unused21: 'Null',
            __Unused22: 'Null',
            __Unused23: 'Null',
            __Unused24: 'Null',
            __Unused25: 'Null',
            __Unused26: 'Null',
            __Unused27: 'Null',
            __Unused28: 'Null',
            __Unused29: 'Null',
            __Unused30: 'Null',
            __Unused31: 'Null',
            __Unused32: 'Null',
            __Unused33: 'Null',
            __Unused34: 'Null',
            __Unused35: 'Null',
            __Unused36: 'Null',
            __Unused37: 'Null',
            __Unused38: 'Null',
            NominationPools: 'PalletNominationPoolsFreezeReason'
        }
    },
    /**
     * Lookup645: pallet_referenda::types::ReferendumInfo<TrackId, polkadot_runtime::OriginCaller, Moment, frame_support::traits::preimages::Bounded<polkadot_runtime::RuntimeCall, sp_runtime::traits::BlakeTwo256>, Balance, pallet_conviction_voting::types::Tally<Votes, Total>, sp_core::crypto::AccountId32, ScheduleAddress>
     **/
    PalletReferendaReferendumInfo: {
        _enum: {
            Ongoing: 'PalletReferendaReferendumStatus',
            Approved: '(u32,Option<PalletReferendaDeposit>,Option<PalletReferendaDeposit>)',
            Rejected: '(u32,Option<PalletReferendaDeposit>,Option<PalletReferendaDeposit>)',
            Cancelled: '(u32,Option<PalletReferendaDeposit>,Option<PalletReferendaDeposit>)',
            TimedOut: '(u32,Option<PalletReferendaDeposit>,Option<PalletReferendaDeposit>)',
            Killed: 'u32'
        }
    },
    /**
     * Lookup646: pallet_referenda::types::ReferendumStatus<TrackId, polkadot_runtime::OriginCaller, Moment, frame_support::traits::preimages::Bounded<polkadot_runtime::RuntimeCall, sp_runtime::traits::BlakeTwo256>, Balance, pallet_conviction_voting::types::Tally<Votes, Total>, sp_core::crypto::AccountId32, ScheduleAddress>
     **/
    PalletReferendaReferendumStatus: {
        track: 'u16',
        origin: 'PolkadotRuntimeOriginCaller',
        proposal: 'FrameSupportPreimagesBounded',
        enactment: 'FrameSupportScheduleDispatchTime',
        submitted: 'u32',
        submissionDeposit: 'PalletReferendaDeposit',
        decisionDeposit: 'Option<PalletReferendaDeposit>',
        deciding: 'Option<PalletReferendaDecidingStatus>',
        tally: 'PalletConvictionVotingTally',
        inQueue: 'bool',
        alarm: 'Option<(u32,(u32,u32))>'
    },
    /**
     * Lookup664: polkadot_runtime_common::claims::pallet::Error<T>
     **/
    PolkadotRuntimeCommonClaimsPalletError: {
        _enum: ['InvalidEthereumSignature', 'SignerHasNoClaim', 'SenderHasNoClaim', 'PotUnderflow', 'InvalidStatement', 'VestedBalanceExists']
    },
    /**
     * Lookup742: polkadot_runtime_parachains::configuration::HostConfiguration<BlockNumber>
     **/
    PolkadotRuntimeParachainsConfigurationHostConfiguration: {
        maxCodeSize: 'u32',
        maxHeadDataSize: 'u32',
        maxUpwardQueueCount: 'u32',
        maxUpwardQueueSize: 'u32',
        maxUpwardMessageSize: 'u32',
        maxUpwardMessageNumPerCandidate: 'u32',
        hrmpMaxMessageNumPerCandidate: 'u32',
        validationUpgradeCooldown: 'u32',
        validationUpgradeDelay: 'u32',
        asyncBackingParams: 'PolkadotPrimitivesV6AsyncBackingAsyncBackingParams',
        maxPovSize: 'u32',
        maxDownwardMessageSize: 'u32',
        hrmpMaxParachainOutboundChannels: 'u32',
        hrmpSenderDeposit: 'u128',
        hrmpRecipientDeposit: 'u128',
        hrmpChannelMaxCapacity: 'u32',
        hrmpChannelMaxTotalSize: 'u32',
        hrmpMaxParachainInboundChannels: 'u32',
        hrmpChannelMaxMessageSize: 'u32',
        executorParams: 'PolkadotPrimitivesV6ExecutorParams',
        codeRetentionPeriod: 'u32',
        coretimeCores: 'u32',
        onDemandRetries: 'u32',
        onDemandQueueMaxSize: 'u32',
        onDemandTargetQueueUtilization: 'Perbill',
        onDemandFeeVariability: 'Perbill',
        onDemandBaseFee: 'u128',
        onDemandTtl: 'u32',
        groupRotationFrequency: 'u32',
        parasAvailabilityPeriod: 'u32',
        schedulingLookahead: 'u32',
        maxValidatorsPerCore: 'Option<u32>',
        maxValidators: 'Option<u32>',
        disputePeriod: 'u32',
        disputePostConclusionAcceptancePeriod: 'u32',
        noShowSlots: 'u32',
        nDelayTranches: 'u32',
        zerothDelayTrancheWidth: 'u32',
        neededApprovals: 'u32',
        relayVrfModuloSamples: 'u32',
        pvfVotingTtl: 'u32',
        minimumValidationUpgradeDelay: 'u32',
        minimumBackingVotes: 'u32',
        nodeFeatures: 'BitVec',
        approvalVotingParams: 'PolkadotPrimitivesVstagingApprovalVotingParams'
    },
    /**
     * Lookup745: polkadot_runtime_parachains::configuration::pallet::Error<T>
     **/
    PolkadotRuntimeParachainsConfigurationPalletError: {
        _enum: ['InvalidNewValue']
    },
    /**
     * Lookup748: polkadot_runtime_parachains::shared::AllowedRelayParentsTracker<primitive_types::H256, BlockNumber>
     **/
    PolkadotRuntimeParachainsSharedAllowedRelayParentsTracker: {
        buffer: 'Vec<(H256,H256)>',
        latestNumber: 'u32'
    },
    /**
     * Lookup751: polkadot_runtime_parachains::inclusion::AvailabilityBitfieldRecord<N>
     **/
    PolkadotRuntimeParachainsInclusionAvailabilityBitfieldRecord: {
        bitfield: 'BitVec',
        submittedAt: 'u32'
    },
    /**
     * Lookup752: polkadot_runtime_parachains::inclusion::CandidatePendingAvailability<primitive_types::H256, N>
     **/
    PolkadotRuntimeParachainsInclusionCandidatePendingAvailability: {
        _alias: {
            hash_: 'hash'
        },
        core: 'u32',
        hash_: 'H256',
        descriptor: 'PolkadotPrimitivesV6CandidateDescriptor',
        availabilityVotes: 'BitVec',
        backers: 'BitVec',
        relayParentNumber: 'u32',
        backedInNumber: 'u32',
        backingGroup: 'u32'
    },
    /**
     * Lookup753: polkadot_runtime_parachains::inclusion::pallet::Error<T>
     **/
    PolkadotRuntimeParachainsInclusionPalletError: {
        _enum: ['UnsortedOrDuplicateValidatorIndices', 'UnsortedOrDuplicateDisputeStatementSet', 'UnsortedOrDuplicateBackedCandidates', 'UnexpectedRelayParent', 'WrongBitfieldSize', 'BitfieldAllZeros', 'BitfieldDuplicateOrUnordered', 'ValidatorIndexOutOfBounds', 'InvalidBitfieldSignature', 'UnscheduledCandidate', 'CandidateScheduledBeforeParaFree', 'ScheduledOutOfOrder', 'HeadDataTooLarge', 'PrematureCodeUpgrade', 'NewCodeTooLarge', 'DisallowedRelayParent', 'InvalidAssignment', 'InvalidGroupIndex', 'InsufficientBacking', 'InvalidBacking', 'NotCollatorSigned', 'ValidationDataHashMismatch', 'IncorrectDownwardMessageHandling', 'InvalidUpwardMessages', 'HrmpWatermarkMishandling', 'InvalidOutboundHrmp', 'InvalidValidationCodeHash', 'ParaHeadMismatch', 'BitfieldReferencesFreedCore']
    },
    /**
     * Lookup754: polkadot_primitives::v6::ScrapedOnChainVotes<primitive_types::H256>
     **/
    PolkadotPrimitivesV6ScrapedOnChainVotes: {
        session: 'u32',
        backingValidatorsPerCandidate: 'Vec<(PolkadotPrimitivesV6CandidateReceipt,Vec<(u32,PolkadotPrimitivesV6ValidityAttestation)>)>',
        disputes: 'Vec<PolkadotPrimitivesV6DisputeStatementSet>'
    },
    /**
     * Lookup759: polkadot_runtime_parachains::paras_inherent::pallet::Error<T>
     **/
    PolkadotRuntimeParachainsParasInherentPalletError: {
        _enum: ['TooManyInclusionInherents', 'InvalidParentHeader', 'CandidateConcludedInvalid', 'InherentOverweight', 'DisputeStatementsUnsortedOrDuplicates', 'DisputeInvalid', 'BackedByDisabled', 'BackedOnUnscheduledCore', 'UnscheduledCandidate']
    },
    /**
     * Lookup762: polkadot_runtime_parachains::scheduler::pallet::CoreOccupied<N>
     **/
    PolkadotRuntimeParachainsSchedulerPalletCoreOccupied: {
        _enum: {
            Free: 'Null',
            Paras: 'PolkadotRuntimeParachainsSchedulerPalletParasEntry'
        }
    },
    /**
     * Lookup763: polkadot_runtime_parachains::scheduler::pallet::ParasEntry<N>
     **/
    PolkadotRuntimeParachainsSchedulerPalletParasEntry: {
        assignment: 'PolkadotRuntimeParachainsSchedulerCommonAssignment',
        availabilityTimeouts: 'u32',
        ttl: 'u32'
    },
    /**
     * Lookup764: polkadot_runtime_parachains::scheduler::common::Assignment
     **/
    PolkadotRuntimeParachainsSchedulerCommonAssignment: {
        _enum: {
            Pool: {
                paraId: 'u32',
                coreIndex: 'u32',
            },
            Bulk: 'u32'
        }
    },
    /**
     * Lookup769: polkadot_runtime_parachains::paras::PvfCheckActiveVoteState<BlockNumber>
     **/
    PolkadotRuntimeParachainsParasPvfCheckActiveVoteState: {
        votesAccept: 'BitVec',
        votesReject: 'BitVec',
        age: 'u32',
        createdAt: 'u32',
        causes: 'Vec<PolkadotRuntimeParachainsParasPvfCheckCause>'
    },
    /**
     * Lookup771: polkadot_runtime_parachains::paras::PvfCheckCause<BlockNumber>
     **/
    PolkadotRuntimeParachainsParasPvfCheckCause: {
        _enum: {
            Onboarding: 'u32',
            Upgrade: {
                id: 'u32',
                includedAt: 'u32',
                setGoAhead: 'PolkadotRuntimeParachainsParasSetGoAhead'
            }
        }
    },
    /**
     * Lookup772: polkadot_runtime_parachains::paras::SetGoAhead
     **/
    PolkadotRuntimeParachainsParasSetGoAhead: {
        _enum: ['Yes', 'No']
    },
    /**
     * Lookup775: polkadot_runtime_parachains::paras::ParaLifecycle
     **/
    PolkadotRuntimeParachainsParasParaLifecycle: {
        _enum: ['Onboarding', 'Parathread', 'Parachain', 'UpgradingParathread', 'DowngradingParachain', 'OffboardingParathread', 'OffboardingParachain']
    },
    /**
     * Lookup777: polkadot_runtime_parachains::paras::ParaPastCodeMeta<N>
     **/
    PolkadotRuntimeParachainsParasParaPastCodeMeta: {
        upgradeTimes: 'Vec<PolkadotRuntimeParachainsParasReplacementTimes>',
        lastPruned: 'Option<u32>'
    },
    /**
     * Lookup779: polkadot_runtime_parachains::paras::ReplacementTimes<N>
     **/
    PolkadotRuntimeParachainsParasReplacementTimes: {
        expectedAt: 'u32',
        activatedAt: 'u32'
    },
    /**
     * Lookup781: polkadot_primitives::v6::UpgradeGoAhead
     **/
    PolkadotPrimitivesV6UpgradeGoAhead: {
        _enum: ['Abort', 'GoAhead']
    },
    /**
     * Lookup782: polkadot_primitives::v6::UpgradeRestriction
     **/
    PolkadotPrimitivesV6UpgradeRestriction: {
        _enum: ['Present']
    },
    /**
     * Lookup783: polkadot_runtime_parachains::paras::ParaGenesisArgs
     **/
    PolkadotRuntimeParachainsParasParaGenesisArgs: {
        genesisHead: 'Bytes',
        validationCode: 'Bytes',
        paraKind: 'bool'
    },
    /**
     * Lookup784: polkadot_runtime_parachains::paras::pallet::Error<T>
     **/
    PolkadotRuntimeParachainsParasPalletError: {
        _enum: ['NotRegistered', 'CannotOnboard', 'CannotOffboard', 'CannotUpgrade', 'CannotDowngrade', 'PvfCheckStatementStale', 'PvfCheckStatementFuture', 'PvfCheckValidatorIndexOutOfBounds', 'PvfCheckInvalidSignature', 'PvfCheckDoubleVote', 'PvfCheckSubjectInvalid', 'CannotUpgradeCode']
    },
    /**
     * Lookup786: polkadot_runtime_parachains::initializer::BufferedSessionChange
     **/
    PolkadotRuntimeParachainsInitializerBufferedSessionChange: {
        validators: 'Vec<PolkadotPrimitivesV6ValidatorAppPublic>',
        queued: 'Vec<PolkadotPrimitivesV6ValidatorAppPublic>',
        sessionIndex: 'u32'
    },
    /**
     * Lookup788: polkadot_core_primitives::InboundDownwardMessage<BlockNumber>
     **/
    PolkadotCorePrimitivesInboundDownwardMessage: {
        sentAt: 'u32',
        msg: 'Bytes'
    },
    /**
     * Lookup789: polkadot_runtime_parachains::hrmp::HrmpOpenChannelRequest
     **/
    PolkadotRuntimeParachainsHrmpHrmpOpenChannelRequest: {
        confirmed: 'bool',
        age: 'u32',
        senderDeposit: 'u128',
        maxMessageSize: 'u32',
        maxCapacity: 'u32',
        maxTotalSize: 'u32'
    },
    /**
     * Lookup791: polkadot_runtime_parachains::hrmp::HrmpChannel
     **/
    PolkadotRuntimeParachainsHrmpHrmpChannel: {
        maxCapacity: 'u32',
        maxTotalSize: 'u32',
        maxMessageSize: 'u32',
        msgCount: 'u32',
        totalSize: 'u32',
        mqcHead: 'Option<H256>',
        senderDeposit: 'u128',
        recipientDeposit: 'u128'
    },
    /**
     * Lookup793: polkadot_core_primitives::InboundHrmpMessage<BlockNumber>
     **/
    PolkadotCorePrimitivesInboundHrmpMessage: {
        sentAt: 'u32',
        data: 'Bytes'
    },
    /**
     * Lookup796: polkadot_runtime_parachains::hrmp::pallet::Error<T>
     **/
    PolkadotRuntimeParachainsHrmpPalletError: {
        _enum: ['OpenHrmpChannelToSelf', 'OpenHrmpChannelInvalidRecipient', 'OpenHrmpChannelZeroCapacity', 'OpenHrmpChannelCapacityExceedsLimit', 'OpenHrmpChannelZeroMessageSize', 'OpenHrmpChannelMessageSizeExceedsLimit', 'OpenHrmpChannelAlreadyExists', 'OpenHrmpChannelAlreadyRequested', 'OpenHrmpChannelLimitExceeded', 'AcceptHrmpChannelDoesntExist', 'AcceptHrmpChannelAlreadyConfirmed', 'AcceptHrmpChannelLimitExceeded', 'CloseHrmpChannelUnauthorized', 'CloseHrmpChannelDoesntExist', 'CloseHrmpChannelAlreadyUnderway', 'CancelHrmpOpenChannelUnauthorized', 'OpenHrmpChannelDoesntExist', 'OpenHrmpChannelAlreadyConfirmed', 'WrongWitness', 'ChannelCreationNotAuthorized']
    },
    /**
     * Lookup798: polkadot_primitives::v6::SessionInfo
     **/
    PolkadotPrimitivesV6SessionInfo: {
        activeValidatorIndices: 'Vec<u32>',
        randomSeed: '[u8;32]',
        disputePeriod: 'u32',
        validators: 'PolkadotPrimitivesV6IndexedVecValidatorIndex',
        discoveryKeys: 'Vec<SpAuthorityDiscoveryAppPublic>',
        assignmentKeys: 'Vec<PolkadotPrimitivesV6AssignmentAppPublic>',
        validatorGroups: 'PolkadotPrimitivesV6IndexedVecGroupIndex',
        nCores: 'u32',
        zerothDelayTrancheWidth: 'u32',
        relayVrfModuloSamples: 'u32',
        nDelayTranches: 'u32',
        noShowSlots: 'u32',
        neededApprovals: 'u32'
    },
    /**
     * Lookup799: polkadot_primitives::v6::IndexedVec<polkadot_primitives::v6::ValidatorIndex, polkadot_primitives::v6::validator_app::Public>
     **/
    PolkadotPrimitivesV6IndexedVecValidatorIndex: 'Vec<PolkadotPrimitivesV6ValidatorAppPublic>',
    /**
     * Lookup800: polkadot_primitives::v6::IndexedVec<polkadot_primitives::v6::GroupIndex, V>
     **/
    PolkadotPrimitivesV6IndexedVecGroupIndex: 'Vec<Vec<u32>>',
    /**
     * Lookup802: polkadot_primitives::v6::DisputeState<N>
     **/
    PolkadotPrimitivesV6DisputeState: {
        validatorsFor: 'BitVec',
        validatorsAgainst: 'BitVec',
        start: 'u32',
        concludedAt: 'Option<u32>'
    },
    /**
     * Lookup804: polkadot_runtime_parachains::disputes::pallet::Error<T>
     **/
    PolkadotRuntimeParachainsDisputesPalletError: {
        _enum: ['DuplicateDisputeStatementSets', 'AncientDisputeStatement', 'ValidatorIndexOutOfBounds', 'InvalidSignature', 'DuplicateStatement', 'SingleSidedDispute', 'MaliciousBacker', 'MissingBackingVotes', 'UnconfirmedDispute']
    },
    /**
     * Lookup805: polkadot_primitives::v6::slashing::PendingSlashes
     **/
    PolkadotPrimitivesV6SlashingPendingSlashes: {
        _alias: {
            keys_: 'keys'
        },
        keys_: 'BTreeMap<u32, PolkadotPrimitivesV6ValidatorAppPublic>',
        kind: 'PolkadotPrimitivesV6SlashingSlashingOffenceKind'
    },
    /**
     * Lookup809: polkadot_runtime_parachains::disputes::slashing::pallet::Error<T>
     **/
    PolkadotRuntimeParachainsDisputesSlashingPalletError: {
        _enum: ['InvalidKeyOwnershipProof', 'InvalidSessionIndex', 'InvalidCandidateHash', 'InvalidValidatorIndex', 'ValidatorIndexIdMismatch', 'DuplicateSlashingReport']
    },
    /**
     * Lookup810: polkadot_runtime_common::paras_registrar::ParaInfo<sp_core::crypto::AccountId32, Balance>
     **/
    PolkadotRuntimeCommonParasRegistrarParaInfo: {
        manager: 'AccountId32',
        deposit: 'u128',
        locked: 'Option<bool>'
    },
    /**
     * Lookup812: polkadot_runtime_common::paras_registrar::pallet::Error<T>
     **/
    PolkadotRuntimeCommonParasRegistrarPalletError: {
        _enum: ['NotRegistered', 'AlreadyRegistered', 'NotOwner', 'CodeTooLarge', 'HeadDataTooLarge', 'NotParachain', 'NotParathread', 'CannotDeregister', 'CannotDowngrade', 'CannotUpgrade', 'ParaLocked', 'NotReserved', 'EmptyCode', 'CannotSwap']
    },
    /**
     * Lookup814: polkadot_runtime_common::slots::pallet::Error<T>
     **/
    PolkadotRuntimeCommonSlotsPalletError: {
        _enum: ['ParaNotOnboarding', 'LeaseError']
    },
    /**
     * Lookup819: polkadot_runtime_common::auctions::pallet::Error<T>
     **/
    PolkadotRuntimeCommonAuctionsPalletError: {
        _enum: ['AuctionInProgress', 'LeasePeriodInPast', 'ParaNotRegistered', 'NotCurrentAuction', 'NotAuction', 'AuctionEnded', 'AlreadyLeasedOut']
    },
    /**
     * Lookup820: polkadot_runtime_common::crowdloan::FundInfo<sp_core::crypto::AccountId32, Balance, BlockNumber, LeasePeriod>
     **/
    PolkadotRuntimeCommonCrowdloanFundInfo: {
        depositor: 'AccountId32',
        verifier: 'Option<SpRuntimeMultiSigner>',
        deposit: 'u128',
        raised: 'u128',
        end: 'u32',
        cap: 'u128',
        lastContribution: 'PolkadotRuntimeCommonCrowdloanLastContribution',
        firstPeriod: 'u32',
        lastPeriod: 'u32',
        fundIndex: 'u32'
    },
    /**
     * Lookup821: polkadot_runtime_common::crowdloan::LastContribution<BlockNumber>
     **/
    PolkadotRuntimeCommonCrowdloanLastContribution: {
        _enum: {
            Never: 'Null',
            PreEnding: 'u32',
            Ending: 'u32'
        }
    },
    /**
     * Lookup822: polkadot_runtime_common::crowdloan::pallet::Error<T>
     **/
    PolkadotRuntimeCommonCrowdloanPalletError: {
        _enum: ['FirstPeriodInPast', 'FirstPeriodTooFarInFuture', 'LastPeriodBeforeFirstPeriod', 'LastPeriodTooFarInFuture', 'CannotEndInPast', 'EndTooFarInFuture', 'Overflow', 'ContributionTooSmall', 'InvalidParaId', 'CapExceeded', 'ContributionPeriodOver', 'InvalidOrigin', 'NotParachain', 'LeaseActive', 'BidOrLeaseActive', 'FundNotEnded', 'NoContributions', 'NotReadyToDissolve', 'InvalidSignature', 'MemoTooLarge', 'AlreadyInNewRaise', 'VrfDelayInProgress', 'NoLeasePeriod']
    },
    /**
     * Lookup823: pallet_xcm::pallet::QueryStatus<BlockNumber>
     **/
    PalletXcmQueryStatus: {
        _enum: {
            Pending: {
                responder: 'XcmVersionedLocation',
                maybeMatchQuerier: 'Option<XcmVersionedLocation>',
                maybeNotify: 'Option<(u8,u8)>',
                timeout: 'u32',
            },
            VersionNotifier: {
                origin: 'XcmVersionedLocation',
                isActive: 'bool',
            },
            Ready: {
                response: 'XcmVersionedResponse',
                at: 'u32'
            }
        }
    },
    /**
     * Lookup827: xcm::VersionedResponse
     **/
    XcmVersionedResponse: {
        _enum: {
            __Unused0: 'Null',
            __Unused1: 'Null',
            V2: 'XcmV2Response',
            V3: 'XcmV3Response',
            V4: 'StagingXcmV4Response'
        }
    },
    /**
     * Lookup833: pallet_xcm::pallet::VersionMigrationStage
     **/
    PalletXcmVersionMigrationStage: {
        _enum: {
            MigrateSupportedVersion: 'Null',
            MigrateVersionNotifiers: 'Null',
            NotifyCurrentTargets: 'Option<Bytes>',
            MigrateAndNotifyOldTargets: 'Null'
        }
    },
    /**
     * Lookup836: pallet_xcm::pallet::RemoteLockedFungibleRecord<ConsumerIdentifier, MaxConsumers>
     **/
    PalletXcmRemoteLockedFungibleRecord: {
        amount: 'u128',
        owner: 'XcmVersionedLocation',
        locker: 'XcmVersionedLocation',
        consumers: 'Vec<(Null,u128)>'
    },
    /**
     * Lookup843: pallet_xcm::pallet::Error<T>
     **/
    PalletXcmError: {
        _enum: ['Unreachable', 'SendFailure', 'Filtered', 'UnweighableMessage', 'DestinationNotInvertible', 'Empty', 'CannotReanchor', 'TooManyAssets', 'InvalidOrigin', 'BadVersion', 'BadLocation', 'NoSubscription', 'AlreadySubscribed', 'CannotCheckOutTeleport', 'LowBalance', 'TooManyLocks', 'AccountNotSovereign', 'FeesNotMet', 'LockNotFound', 'InUse', 'InvalidAssetNotConcrete', 'InvalidAssetUnknownReserve', 'InvalidAssetUnsupportedReserve', 'TooManyReserves', 'LocalExecutionIncomplete']
    },
    /**
     * Lookup866: pallet_transaction_payment::ChargeTransactionPayment<T>
     **/
    PalletTransactionPaymentChargeTransactionPayment: 'Compact<u128>',
    /**
     * Lookup867: polkadot_runtime_common::claims::PrevalidateAttests<T>
     **/
    PolkadotRuntimeCommonClaimsPrevalidateAttests: 'Null',
    /**
     * Lookup870: polkadot_runtime::Runtime
     **/
    PolkadotRuntimeRuntime: 'Null'
};

{"version": 3, "file": "signingstargateclient.js", "sourceRoot": "", "sources": ["../src/signingstargateclient.ts"], "names": [], "mappings": ";;;AAAA,yCAA+F;AAC/F,+CAA8C;AAC9C,uCAA6C;AAC7C,yDAU+B;AAC/B,2DAAiF;AACjF,yCAAsD;AACtD,gEAA6D;AAC7D,oEAAyF;AACzF,+DAAoF;AACpF,4EAA0E;AAC1E,0DAA0D;AAC1D,qEAA2E;AAG3E,6CAA2D;AAC3D,+BAA+C;AAC/C,uCAemB;AACnB,uCASmB;AACnB,qDAA4F;AAE/E,QAAA,oBAAoB,GAA2C;IAC1E,CAAC,2BAA2B,EAAE,WAAI,CAAC;IACnC,GAAG,oBAAU;IACb,GAAG,mBAAS;IACZ,GAAG,2BAAiB;IACpB,GAAG,uBAAa;IAChB,GAAG,kBAAQ;IACX,GAAG,oBAAU;IACb,GAAG,sBAAY;IACf,GAAG,kBAAQ;IACX,GAAG,sBAAY;CAChB,CAAC;AA0BF,SAAgB,4BAA4B;IAC1C,OAAO;QACL,GAAG,IAAA,oCAA0B,GAAE;QAC/B,GAAG,IAAA,mCAAyB,GAAE;QAC9B,GAAG,IAAA,2CAAiC,GAAE;QACtC,GAAG,IAAA,kCAAwB,GAAE;QAC7B,GAAG,IAAA,sCAA4B,GAAE;QACjC,GAAG,IAAA,kCAAwB,GAAE;QAC7B,GAAG,IAAA,uCAA6B,GAAE;QAClC,GAAG,IAAA,sCAA4B,GAAE;KAClC,CAAC;AACJ,CAAC;AAXD,oEAWC;AAED,MAAa,qBAAsB,SAAQ,+BAAc;IAYvD;;;;;OAKG;IACI,MAAM,CAAC,KAAK,CAAC,iBAAiB,CACnC,QAA+B,EAC/B,MAAqB,EACrB,UAAwC,EAAE;QAE1C,MAAM,WAAW,GAAG,MAAM,IAAA,6BAAY,EAAC,QAAQ,CAAC,CAAC;QACjD,OAAO,qBAAqB,CAAC,gBAAgB,CAAC,WAAW,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;IAC9E,CAAC;IAED;;;OAGG;IACI,MAAM,CAAC,KAAK,CAAC,gBAAgB,CAClC,WAAwB,EACxB,MAAqB,EACrB,UAAwC,EAAE;QAE1C,OAAO,IAAI,qBAAqB,CAAC,WAAW,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;IACjE,CAAC;IAED;;;;;;;;OAQG;IACI,MAAM,CAAC,KAAK,CAAC,OAAO,CACzB,MAAqB,EACrB,UAAwC,EAAE;QAE1C,OAAO,IAAI,qBAAqB,CAAC,SAAS,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;IAC/D,CAAC;IAED,YACE,WAAoC,EACpC,MAAqB,EACrB,OAAqC;QAErC,KAAK,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;QApD9B,sFAAsF;QACtF,yDAAyD;QACxC,yBAAoB,GAAG,GAAG,CAAC;QAmD1C,MAAM,EACJ,QAAQ,GAAG,IAAI,wBAAQ,CAAC,4BAAoB,CAAC,EAC7C,UAAU,GAAG,IAAI,uBAAU,CAAC,4BAA4B,EAAE,CAAC,GAC5D,GAAG,OAAO,CAAC;QACZ,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,kBAAkB,GAAG,OAAO,CAAC,kBAAkB,CAAC;QACrD,IAAI,CAAC,uBAAuB,GAAG,OAAO,CAAC,uBAAuB,CAAC;QAC/D,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;IACnC,CAAC;IAEM,KAAK,CAAC,QAAQ,CACnB,aAAqB,EACrB,QAAiC,EACjC,IAAwB;QAExB,MAAM,OAAO,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;QAClE,MAAM,iBAAiB,GAAG,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAC9D,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,OAAO,KAAK,aAAa,CAC/C,CAAC;QACF,IAAI,CAAC,iBAAiB,EAAE;YACtB,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;SAC3D;QACD,MAAM,MAAM,GAAG,IAAA,6BAAqB,EAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;QAC/D,MAAM,EAAE,QAAQ,EAAE,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC;QAC3D,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC,EAAE,CAAC,QAAQ,CAAC,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC;QAClG,IAAA,qBAAa,EAAC,OAAO,CAAC,CAAC;QACvB,OAAO,aAAM,CAAC,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC;IAClE,CAAC;IAEM,KAAK,CAAC,UAAU,CACrB,aAAqB,EACrB,gBAAwB,EACxB,MAAuB,EACvB,GAA6B,EAC7B,IAAI,GAAG,EAAE;QAET,MAAM,OAAO,GAAwB;YACnC,OAAO,EAAE,8BAA8B;YACvC,KAAK,EAAE;gBACL,WAAW,EAAE,aAAa;gBAC1B,SAAS,EAAE,gBAAgB;gBAC3B,MAAM,EAAE,CAAC,GAAG,MAAM,CAAC;aACpB;SACF,CAAC;QACF,OAAO,IAAI,CAAC,gBAAgB,CAAC,aAAa,EAAE,CAAC,OAAO,CAAC,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC;IACpE,CAAC;IAEM,KAAK,CAAC,cAAc,CACzB,gBAAwB,EACxB,gBAAwB,EACxB,MAAY,EACZ,GAA6B,EAC7B,IAAI,GAAG,EAAE;QAET,MAAM,WAAW,GAA4B;YAC3C,OAAO,EAAE,qCAAqC;YAC9C,KAAK,EAAE,gBAAW,CAAC,WAAW,CAAC;gBAC7B,gBAAgB,EAAE,gBAAgB;gBAClC,gBAAgB,EAAE,gBAAgB;gBAClC,MAAM,EAAE,MAAM;aACf,CAAC;SACH,CAAC;QACF,OAAO,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,EAAE,CAAC,WAAW,CAAC,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC;IAC3E,CAAC;IAEM,KAAK,CAAC,gBAAgB,CAC3B,gBAAwB,EACxB,gBAAwB,EACxB,MAAY,EACZ,GAA6B,EAC7B,IAAI,GAAG,EAAE;QAET,MAAM,aAAa,GAA8B;YAC/C,OAAO,EAAE,uCAAuC;YAChD,KAAK,EAAE,kBAAa,CAAC,WAAW,CAAC;gBAC/B,gBAAgB,EAAE,gBAAgB;gBAClC,gBAAgB,EAAE,gBAAgB;gBAClC,MAAM,EAAE,MAAM;aACf,CAAC;SACH,CAAC;QACF,OAAO,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,EAAE,CAAC,aAAa,CAAC,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC;IAC7E,CAAC;IAEM,KAAK,CAAC,eAAe,CAC1B,gBAAwB,EACxB,gBAAwB,EACxB,GAA6B,EAC7B,IAAI,GAAG,EAAE;QAET,MAAM,WAAW,GAA2C;YAC1D,OAAO,EAAE,yDAAyD;YAClE,KAAK,EAAE,+BAA0B,CAAC,WAAW,CAAC;gBAC5C,gBAAgB,EAAE,gBAAgB;gBAClC,gBAAgB,EAAE,gBAAgB;aACnC,CAAC;SACH,CAAC;QACF,OAAO,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,EAAE,CAAC,WAAW,CAAC,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC;IAC3E,CAAC;IAED;;;;;OAKG;IACI,KAAK,CAAC,aAAa,CACxB,aAAqB,EACrB,gBAAwB,EACxB,cAAoB,EACpB,UAAkB,EAClB,aAAqB,EACrB,aAAiC;IACjC,yBAAyB;IACzB,gBAAoC,EACpC,GAA6B,EAC7B,IAAI,GAAG,EAAE;QAET,MAAM,2BAA2B,GAAG,gBAAgB;YAClD,CAAC,CAAC,MAAM,CAAC,gBAAgB,CAAC,GAAG,MAAM,CAAC,UAAa,CAAC;YAClD,CAAC,CAAC,SAAS,CAAC;QACd,MAAM,WAAW,GAA4B;YAC3C,OAAO,EAAE,2CAA2C;YACpD,KAAK,EAAE,gBAAW,CAAC,WAAW,CAAC;gBAC7B,UAAU,EAAE,UAAU;gBACtB,aAAa,EAAE,aAAa;gBAC5B,MAAM,EAAE,aAAa;gBACrB,QAAQ,EAAE,gBAAgB;gBAC1B,KAAK,EAAE,cAAc;gBACrB,aAAa,EAAE,aAAa;gBAC5B,gBAAgB,EAAE,2BAA2B;aAC9C,CAAC;SACH,CAAC;QACF,OAAO,IAAI,CAAC,gBAAgB,CAAC,aAAa,EAAE,CAAC,WAAW,CAAC,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC;IACxE,CAAC;IAEM,KAAK,CAAC,gBAAgB,CAC3B,aAAqB,EACrB,QAAiC,EACjC,GAA6B,EAC7B,IAAI,GAAG,EAAE,EACT,aAAsB;QAEtB,IAAI,OAAe,CAAC;QACpB,IAAI,GAAG,IAAI,MAAM,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;YAC5C,IAAA,qBAAa,EAAC,IAAI,CAAC,QAAQ,EAAE,oEAAoE,CAAC,CAAC;YACnG,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,aAAa,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC;YACzE,MAAM,UAAU,GAAG,OAAO,GAAG,KAAK,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC;YAC7E,OAAO,GAAG,IAAA,kBAAY,EAAC,IAAI,CAAC,KAAK,CAAC,aAAa,GAAG,UAAU,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;SAC/E;aAAM;YACL,OAAO,GAAG,GAAG,CAAC;SACf;QACD,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,QAAQ,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,aAAa,CAAC,CAAC;QAChG,MAAM,OAAO,GAAG,UAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,MAAM,EAAE,CAAC;QAC7C,OAAO,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,uBAAuB,CAAC,CAAC;IAC1F,CAAC;IAED;;;;;OAKG;IACI,KAAK,CAAC,oBAAoB,CAC/B,aAAqB,EACrB,QAAiC,EACjC,GAA6B,EAC7B,IAAI,GAAG,EAAE,EACT,aAAsB;QAEtB,IAAI,OAAe,CAAC;QACpB,IAAI,GAAG,IAAI,MAAM,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;YAC5C,IAAA,qBAAa,EAAC,IAAI,CAAC,QAAQ,EAAE,oEAAoE,CAAC,CAAC;YACnG,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,aAAa,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC;YACzE,MAAM,UAAU,GAAG,OAAO,GAAG,KAAK,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC;YAC7E,OAAO,GAAG,IAAA,kBAAY,EAAC,IAAI,CAAC,KAAK,CAAC,aAAa,GAAG,UAAU,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;SAC/E;aAAM;YACL,OAAO,GAAG,GAAG,CAAC;SACf;QACD,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,QAAQ,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,aAAa,CAAC,CAAC;QAChG,MAAM,OAAO,GAAG,UAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,MAAM,EAAE,CAAC;QAC7C,OAAO,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;IACvC,CAAC;IAED;;;;;;;;;OASG;IACI,KAAK,CAAC,IAAI,CACf,aAAqB,EACrB,QAAiC,EACjC,GAAW,EACX,IAAY,EACZ,kBAA+B,EAC/B,aAAsB;QAEtB,IAAI,UAAsB,CAAC;QAC3B,IAAI,kBAAkB,EAAE;YACtB,UAAU,GAAG,kBAAkB,CAAC;SACjC;aAAM;YACL,MAAM,EAAE,aAAa,EAAE,QAAQ,EAAE,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC;YAC1E,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;YACxC,UAAU,GAAG;gBACX,aAAa,EAAE,aAAa;gBAC5B,QAAQ,EAAE,QAAQ;gBAClB,OAAO,EAAE,OAAO;aACjB,CAAC;SACH;QAED,OAAO,IAAA,qCAAqB,EAAC,IAAI,CAAC,MAAM,CAAC;YACvC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE,QAAQ,EAAE,GAAG,EAAE,IAAI,EAAE,UAAU,EAAE,aAAa,CAAC;YAChF,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,aAAa,EAAE,QAAQ,EAAE,GAAG,EAAE,IAAI,EAAE,UAAU,EAAE,aAAa,CAAC,CAAC;IACpF,CAAC;IAEO,KAAK,CAAC,SAAS,CACrB,aAAqB,EACrB,QAAiC,EACjC,GAAW,EACX,IAAY,EACZ,EAAE,aAAa,EAAE,QAAQ,EAAE,OAAO,EAAc,EAChD,aAAsB;QAEtB,IAAA,cAAM,EAAC,CAAC,IAAA,qCAAqB,EAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;QAC5C,MAAM,iBAAiB,GAAG,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAC9D,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,OAAO,KAAK,aAAa,CAC/C,CAAC;QACF,IAAI,CAAC,iBAAiB,EAAE;YACtB,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;SAC3D;QACD,MAAM,MAAM,GAAG,IAAA,4BAAY,EAAC,IAAA,6BAAqB,EAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC,CAAC;QAC7E,MAAM,QAAQ,GAAG,kBAAQ,CAAC,2BAA2B,CAAC;QACtD,MAAM,IAAI,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC;QACjE,MAAM,OAAO,GAAG,IAAA,mBAAgB,EAAC,IAAI,EAAE,GAAG,EAAE,OAAO,EAAE,IAAI,EAAE,aAAa,EAAE,QAAQ,EAAE,aAAa,CAAC,CAAC;QACnG,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;QAClF,MAAM,YAAY,GAAG;YACnB,QAAQ,EAAE,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;YAClE,IAAI,EAAE,MAAM,CAAC,IAAI;YACjB,aAAa,EAAE,aAAa;SAC7B,CAAC;QACF,MAAM,wBAAwB,GAAuB;YACnD,OAAO,EAAE,2BAA2B;YACpC,KAAK,EAAE,YAAY;SACpB,CAAC;QACF,MAAM,iBAAiB,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,wBAAwB,CAAC,CAAC;QACzE,MAAM,cAAc,GAAG,YAAK,CAAC,UAAU,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAC;QACnE,MAAM,cAAc,GAAG,YAAK,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,QAAQ,EAAE,CAAC;QACpE,MAAM,mBAAmB,GAAG,IAAA,iCAAiB,EAC3C,CAAC,EAAE,MAAM,EAAE,QAAQ,EAAE,cAAc,EAAE,CAAC,EACtC,MAAM,CAAC,GAAG,CAAC,MAAM,EACjB,cAAc,EACd,MAAM,CAAC,GAAG,CAAC,OAAO,EAClB,MAAM,CAAC,GAAG,CAAC,KAAK,EAChB,QAAQ,CACT,CAAC;QACF,OAAO,UAAK,CAAC,WAAW,CAAC;YACvB,SAAS,EAAE,iBAAiB;YAC5B,aAAa,EAAE,mBAAmB;YAClC,UAAU,EAAE,CAAC,IAAA,qBAAU,EAAC,SAAS,CAAC,SAAS,CAAC,CAAC;SAC9C,CAAC,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,UAAU,CACtB,aAAqB,EACrB,QAAiC,EACjC,GAAW,EACX,IAAY,EACZ,EAAE,aAAa,EAAE,QAAQ,EAAE,OAAO,EAAc,EAChD,aAAsB;QAEtB,IAAA,cAAM,EAAC,IAAA,qCAAqB,EAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;QAC3C,MAAM,iBAAiB,GAAG,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAC9D,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,OAAO,KAAK,aAAa,CAC/C,CAAC;QACF,IAAI,CAAC,iBAAiB,EAAE;YACtB,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;SAC3D;QACD,MAAM,MAAM,GAAG,IAAA,4BAAY,EAAC,IAAA,6BAAqB,EAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC,CAAC;QAC7E,MAAM,kBAAkB,GAAuB;YAC7C,OAAO,EAAE,2BAA2B;YACpC,KAAK,EAAE;gBACL,QAAQ,EAAE,QAAQ;gBAClB,IAAI,EAAE,IAAI;gBACV,aAAa,EAAE,aAAa;aAC7B;SACF,CAAC;QACF,MAAM,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC;QAC7D,MAAM,QAAQ,GAAG,YAAK,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAC;QACtD,MAAM,aAAa,GAAG,IAAA,iCAAiB,EACrC,CAAC,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC,EACtB,GAAG,CAAC,MAAM,EACV,QAAQ,EACR,GAAG,CAAC,OAAO,EACX,GAAG,CAAC,KAAK,CACV,CAAC;QACF,MAAM,OAAO,GAAG,IAAA,2BAAW,EAAC,WAAW,EAAE,aAAa,EAAE,OAAO,EAAE,aAAa,CAAC,CAAC;QAChF,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;QACnF,OAAO,UAAK,CAAC,WAAW,CAAC;YACvB,SAAS,EAAE,MAAM,CAAC,SAAS;YAC3B,aAAa,EAAE,MAAM,CAAC,aAAa;YACnC,UAAU,EAAE,CAAC,IAAA,qBAAU,EAAC,SAAS,CAAC,SAAS,CAAC,CAAC;SAC9C,CAAC,CAAC;IACL,CAAC;CACF;AAlXD,sDAkXC"}
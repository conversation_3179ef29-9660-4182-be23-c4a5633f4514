{"version": 3, "file": "sha3-addons.d.ts", "sourceRoot": "", "sources": ["../src/sha3-addons.ts"], "names": [], "mappings": "AACA,OAAO,EACL,KAAK,EAIL,IAAI,EACJ,OAAO,EAER,MAAM,YAAY,CAAC;AACpB,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,WAAW,CAAC;AAyB9C,MAAM,MAAM,UAAU,GAAG,SAAS,GAAG;IAAE,eAAe,CAAC,EAAE,KAAK,CAAC;IAAC,MAAM,CAAC,EAAE,KAAK,CAAA;CAAE,CAAC;AAyBjF,eAAO,MAAM,SAAS;;;;;CAA0D,CAAC;AACjF,eAAO,MAAM,SAAS;;;;;CAA0D,CAAC;AAEjF,qBAAa,IAAK,SAAQ,MAAO,YAAW,OAAO,CAAC,IAAI,CAAC;gBAErD,QAAQ,EAAE,MAAM,EAChB,SAAS,EAAE,MAAM,EACjB,SAAS,EAAE,OAAO,EAClB,GAAG,EAAE,KAAK,EACV,IAAI,GAAE,UAAe;IAYvB,SAAS,CAAC,MAAM;IAIhB,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI,GAAG,IAAI;IAW3B,KAAK,IAAI,IAAI;CAGd;AAUD,eAAO,MAAM,OAAO;UAPC,KAAK,WAAW,KAAK,SAAS,UAAU,GAAG,UAAU;gBAEpD,KAAK,SAAQ,UAAU;CAKyB,CAAC;AACvE,eAAO,MAAM,OAAO;UARC,KAAK,WAAW,KAAK,SAAS,UAAU,GAAG,UAAU;gBAEpD,KAAK,SAAQ,UAAU;CAMyB,CAAC;AACvE,eAAO,MAAM,UAAU;UATF,KAAK,WAAW,KAAK,SAAS,UAAU,GAAG,UAAU;gBAEpD,KAAK,SAAQ,UAAU;CAOkC,CAAC;AAChF,eAAO,MAAM,UAAU;UAVF,KAAK,WAAW,KAAK,SAAS,UAAU,GAAG,UAAU;gBAEpD,KAAK,SAAQ,UAAU;CAQkC,CAAC;AAIhF,qBAAa,SAAU,SAAQ,MAAO,YAAW,OAAO,CAAC,SAAS,CAAC;gBACrD,QAAQ,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,GAAE,UAAe;IAW1F,SAAS,CAAC,MAAM;IAIhB,UAAU,CAAC,EAAE,CAAC,EAAE,SAAS,GAAG,SAAS;IAIrC,KAAK,IAAI,SAAS;CAGnB;AAaD,eAAO,MAAM,YAAY;eAVE,KAAK,EAAE,SAAS,UAAU,GAAG,UAAU;kBAK1C,UAAU;CAK0C,CAAC;AAC7E,eAAO,MAAM,YAAY;eAXE,KAAK,EAAE,SAAS,UAAU,GAAG,UAAU;kBAK1C,UAAU;CAM0C,CAAC;AAC7E,eAAO,MAAM,eAAe;eAZD,KAAK,EAAE,SAAS,UAAU,GAAG,UAAU;kBAK1C,UAAU;CAOmD,CAAC;AACtF,eAAO,MAAM,eAAe;eAbD,KAAK,EAAE,SAAS,UAAU,GAAG,UAAU;kBAK1C,UAAU;CAQmD,CAAC;AAGtF,KAAK,YAAY,GAAG,UAAU,GAAG;IAAE,QAAQ,CAAC,EAAE,MAAM,CAAA;CAAE,CAAC;AAEvD,qBAAa,YAAa,SAAQ,MAAO,YAAW,OAAO,CAAC,YAAY,CAAC;IAQrE,SAAS,CAAC,QAAQ,EAAE,MAAM,IAAI,CAAC,MAAM,CAAC;IAPxC,OAAO,CAAC,QAAQ,CAAC,CAAe;IAChC,OAAO,CAAC,QAAQ,CAAK;IACrB,OAAO,CAAC,UAAU,CAAK;IACvB,OAAO,CAAC,QAAQ,CAAS;gBAEvB,QAAQ,EAAE,MAAM,EAChB,SAAS,EAAE,MAAM,EACP,QAAQ,EAAE,MAAM,IAAI,CAAC,MAAM,CAAC,EACtC,SAAS,EAAE,OAAO,EAClB,IAAI,GAAE,YAAiB;IA8BzB,SAAS,CAAC,MAAM;IAUhB,UAAU,CAAC,EAAE,CAAC,EAAE,YAAY,GAAG,YAAY;IAQ3C,OAAO;IAIP,KAAK,IAAI,YAAY;CAGtB;AAqBD,eAAO,MAAM,eAAe;cAbC,KAAK,SAAS,YAAY,GAAG,UAAU;kBAEzC,YAAY;CAWiD,CAAC;AACzF,eAAO,MAAM,eAAe;cAdC,KAAK,SAAS,YAAY,GAAG,UAAU;kBAEzC,YAAY;CAYiD,CAAC;AACzF,eAAO,MAAM,kBAAkB;cAfF,KAAK,SAAS,YAAY,GAAG,UAAU;kBAEzC,YAAY;CAa0D,CAAC;AAClG,eAAO,MAAM,kBAAkB;cAhBF,KAAK,SAAS,YAAY,GAAG,UAAU;kBAEzC,YAAY;CAc0D,CAAC;AAGlG,MAAM,MAAM,cAAc,GAAG,SAAS,GAAG;IACvC,CAAC,CAAC,EAAE,MAAM,CAAC;CACZ,CAAC;AAWF,eAAO,MAAM,aAAa;;;;;CAA8C,CAAC;AACzE,eAAO,MAAM,aAAa;;;;;CAA8C,CAAC;AAWzE,MAAM,MAAM,YAAY,GAAG;IAAE,KAAK,CAAC,EAAE,MAAM,CAAC;IAAC,eAAe,CAAC,EAAE,KAAK,CAAA;CAAE,CAAC;AAGvE,qBAAa,cAAe,SAAQ,MAAO,YAAW,OAAO,CAAC,cAAc,CAAC;IAQzE,SAAS,CAAC,OAAO,EAAE,MAAM;IAP3B,QAAQ,CAAC,QAAQ,QAAQ;IACzB,OAAO,CAAC,QAAQ,CAAC,CAAS;IAC1B,OAAO,CAAC,eAAe,CAAa;IACpC,OAAO,CAAC,QAAQ,CAAK;IACrB,OAAO,CAAC,UAAU,CAAK;gBAErB,QAAQ,EAAE,MAAM,EACN,OAAO,EAAE,MAAM,EACzB,SAAS,EAAE,MAAM,EACjB,MAAM,EAAE,MAAM,EACd,IAAI,EAAE,YAAY;IAMpB,MAAM,CAAC,IAAI,EAAE,KAAK;IAuBlB,SAAS,CAAC,MAAM;IAYhB,OAAO;IAMP,UAAU,CAAC,EAAE,CAAC,EAAE,cAAc,GAAG,cAAc;IAW/C,KAAK,IAAI,cAAc;CAGxB;AAED,eAAO,MAAM,GAAG;;;;;CAGV,CAAC;AAEP,eAAO,MAAM,GAAG;;;;;CAGV,CAAC;AAIP,qBAAa,SAAU,SAAQ,MAAM;IACnC,SAAS,CAAC,IAAI,EAAE,MAAM,CAAC;gBACX,QAAQ,EAAE,MAAM;IAU5B,MAAM;IAQN,MAAM,CAAC,IAAI,EAAE,KAAK;IAKlB,IAAI,CAAC,IAAI,EAAE,KAAK;IAGhB,SAAS,CAAC,MAAM;IAChB,UAAU,CAAC,IAAI,EAAE,UAAU,GAAG,UAAU;IAGxC,KAAK,CAAC,KAAK,EAAE,MAAM,GAAG,UAAU;IAIhC,MAAM;IAQN,UAAU,CAAC,EAAE,CAAC,EAAE,SAAS,GAAG,SAAS;IAOrC,KAAK,IAAI,SAAS;CAGnB;AAED,eAAO,MAAM,SAAS,kCAA8C,CAAC"}
{"version": 3, "file": "sha512.d.ts", "sourceRoot": "", "sources": ["../src/sha512.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,EAAE,MAAM,UAAU,CAAC;AAgClC,qBAAa,MAAO,SAAQ,MAAM,CAAC,MAAM,CAAC;IAKxC,EAAE,SAAkB;IACpB,EAAE,SAAkB;IACpB,EAAE,SAAkB;IACpB,EAAE,SAAkB;IACpB,EAAE,SAAkB;IACpB,EAAE,SAAkB;IACpB,EAAE,SAAkB;IACpB,EAAE,SAAkB;IACpB,EAAE,SAAkB;IACpB,EAAE,SAAkB;IACpB,EAAE,SAAkB;IACpB,EAAE,SAAkB;IACpB,EAAE,SAAkB;IACpB,EAAE,SAAkB;IACpB,EAAE,SAAkB;IACpB,EAAE,SAAkB;;IAMpB,SAAS,CAAC,GAAG,IAAI;QACf,MAAM;QAAE,MAAM;QAAE,MAAM;QAAE,MAAM;QAAE,MAAM;QAAE,MAAM;QAAE,MAAM;QAAE,MAAM;QAC9D,MAAM;QAAE,MAAM;QAAE,MAAM;QAAE,MAAM;QAAE,MAAM;QAAE,MAAM;QAAE,MAAM;QAAE,MAAM;KAC/D;IAKD,SAAS,CAAC,GAAG,CACX,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAC9F,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM;IAmBhG,SAAS,CAAC,OAAO,CAAC,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM;IAsEhD,SAAS,CAAC,UAAU;IAIpB,OAAO;CAIR;AAED,qBAAa,UAAW,SAAQ,MAAM;IAEpC,EAAE,SAAkB;IACpB,EAAE,SAAkB;IACpB,EAAE,SAAkB;IACpB,EAAE,SAAkB;IACpB,EAAE,SAAkB;IACpB,EAAE,SAAkB;IACpB,EAAE,SAAkB;IACpB,EAAE,SAAkB;IACpB,EAAE,SAAkB;IACpB,EAAE,SAAkB;IACpB,EAAE,SAAkB;IACpB,EAAE,SAAkB;IACpB,EAAE,SAAkB;IACpB,EAAE,SAAkB;IACpB,EAAE,SAAkB;IACpB,EAAE,SAAkB;;CAMrB;AAED,qBAAa,UAAW,SAAQ,MAAM;IAEpC,EAAE,SAAkB;IACpB,EAAE,SAAkB;IACpB,EAAE,SAAkB;IACpB,EAAE,SAAkB;IACpB,EAAE,SAAkB;IACpB,EAAE,SAAkB;IACpB,EAAE,SAAkB;IACpB,EAAE,SAAkB;IACpB,EAAE,SAAkB;IACpB,EAAE,SAAkB;IACpB,EAAE,SAAkB;IACpB,EAAE,SAAkB;IACpB,EAAE,SAAkB;IACpB,EAAE,SAAkB;IACpB,EAAE,SAAkB;IACpB,EAAE,SAAkB;;CAMrB;AAED,qBAAa,MAAO,SAAQ,MAAM;IAEhC,EAAE,SAAkB;IACpB,EAAE,SAAkB;IACpB,EAAE,SAAkB;IACpB,EAAE,SAAkB;IACpB,EAAE,SAAkB;IACpB,EAAE,SAAkB;IACpB,EAAE,SAAkB;IACpB,EAAE,SAAkB;IACpB,EAAE,SAAkB;IACpB,EAAE,SAAkB;IACpB,EAAE,SAAkB;IACpB,EAAE,SAAkB;IACpB,EAAE,SAAkB;IACpB,EAAE,SAAkB;IACpB,EAAE,SAAkB;IACpB,EAAE,SAAkB;;CAMrB;AAED,eAAO,MAAM,MAAM;;;;;CAAsD,CAAC;AAC1E,eAAO,MAAM,UAAU;;;;;CAA0D,CAAC;AAClF,eAAO,MAAM,UAAU;;;;;CAA0D,CAAC;AAClF,eAAO,MAAM,MAAM;;;;;CAAsD,CAAC"}
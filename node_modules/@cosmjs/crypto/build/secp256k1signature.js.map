{"version": 3, "file": "secp256k1signature.js", "sourceRoot": "", "sources": ["../src/secp256k1signature.ts"], "names": [], "mappings": ";;;AAAA,SAAS,oBAAoB,CAAC,MAAkB;IAC9C,IAAI,wBAAwB,GAAG,CAAC,CAAC;IACjC,KAAK,MAAM,IAAI,IAAI,MAAM,EAAE;QACzB,IAAI,IAAI,KAAK,IAAI,EAAE;YACjB,wBAAwB,EAAE,CAAC;SAC5B;aAAM;YACL,MAAM;SACP;KACF;IACD,OAAO,MAAM,CAAC,KAAK,CAAC,wBAAwB,CAAC,CAAC;AAChD,CAAC;AAED,MAAM,aAAa,GAAG,IAAI,CAAC;AAE3B,MAAa,kBAAkB;IAC7B;;;;;;OAMG;IACI,MAAM,CAAC,eAAe,CAAC,IAAgB;QAC5C,IAAI,IAAI,CAAC,MAAM,KAAK,EAAE,EAAE;YACtB,MAAM,IAAI,KAAK,CAAC,4BAA4B,IAAI,CAAC,MAAM,4CAA4C,CAAC,CAAC;SACtG;QACD,OAAO,IAAI,kBAAkB,CAC3B,oBAAoB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EACvC,oBAAoB,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CACzC,CAAC;IACJ,CAAC;IAEM,MAAM,CAAC,OAAO,CAAC,IAAgB;QACpC,IAAI,GAAG,GAAG,CAAC,CAAC;QAEZ,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC,KAAK,IAAI,EAAE;YACxB,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;SACzC;QAED,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;QAC/B,IAAI,IAAI,CAAC,MAAM,GAAG,GAAG,KAAK,UAAU,EAAE;YACpC,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;SAClD;QAED,IAAI;QACJ,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;QACzB,IAAI,IAAI,KAAK,aAAa,EAAE;YAC1B,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;SACzC;QACD,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;QAC5B,IAAI,OAAO,IAAI,IAAI,EAAE;YACnB,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;SACnE;QACD,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,GAAG,OAAO,CAAC,CAAC;QAC7C,GAAG,IAAI,OAAO,CAAC;QAEf,IAAI;QACJ,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;QACzB,IAAI,IAAI,KAAK,aAAa,EAAE;YAC1B,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;SACzC;QACD,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;QAC5B,IAAI,OAAO,IAAI,IAAI,EAAE;YACnB,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;SACnE;QACD,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,GAAG,OAAO,CAAC,CAAC;QAC7C,GAAG,IAAI,OAAO,CAAC;QAEf,OAAO,IAAI,kBAAkB;QAC3B,qFAAqF;QACrF,oBAAoB,CAAC,KAAK,CAAC,EAC3B,oBAAoB,CAAC,KAAK,CAAC,CAC5B,CAAC;IACJ,CAAC;IAOD,YAAmB,CAAa,EAAE,CAAa;QAC7C,IAAI,CAAC,CAAC,MAAM,GAAG,EAAE,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE;YACpD,MAAM,IAAI,KAAK,CAAC,4DAA4D,CAAC,CAAC;SAC/E;QAED,IAAI,CAAC,CAAC,MAAM,GAAG,EAAE,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE;YACpD,MAAM,IAAI,KAAK,CAAC,4DAA4D,CAAC,CAAC;SAC/E;QAED,IAAI,CAAC,IAAI,GAAG;YACV,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,CAAC;SACL,CAAC;IACJ,CAAC;IAEM,CAAC,CAAC,MAAe;QACtB,IAAI,MAAM,KAAK,SAAS,EAAE;YACxB,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;SACpB;aAAM;YACL,MAAM,aAAa,GAAG,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC;YAClD,IAAI,aAAa,GAAG,CAAC,EAAE;gBACrB,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;aACzD;YACD,MAAM,OAAO,GAAG,IAAI,UAAU,CAAC,aAAa,CAAC,CAAC;YAC9C,OAAO,IAAI,UAAU,CAAC,CAAC,GAAG,OAAO,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;SACrD;IACH,CAAC;IAEM,CAAC,CAAC,MAAe;QACtB,IAAI,MAAM,KAAK,SAAS,EAAE;YACxB,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;SACpB;aAAM;YACL,MAAM,aAAa,GAAG,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC;YAClD,IAAI,aAAa,GAAG,CAAC,EAAE;gBACrB,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;aACzD;YACD,MAAM,OAAO,GAAG,IAAI,UAAU,CAAC,aAAa,CAAC,CAAC;YAC9C,OAAO,IAAI,UAAU,CAAC,CAAC,GAAG,OAAO,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;SACrD;IACH,CAAC;IAEM,aAAa;QAClB,OAAO,IAAI,UAAU,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACxD,CAAC;IAEM,KAAK;QACV,mFAAmF;QACnF,+EAA+E;QAC/E,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAC5F,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAE5F,MAAM,OAAO,GAAG,QAAQ,CAAC,MAAM,CAAC;QAChC,MAAM,OAAO,GAAG,QAAQ,CAAC,MAAM,CAAC;QAChC,MAAM,IAAI,GAAG,IAAI,UAAU,CAAC,CAAC,aAAa,EAAE,OAAO,EAAE,GAAG,QAAQ,EAAE,aAAa,EAAE,OAAO,EAAE,GAAG,QAAQ,CAAC,CAAC,CAAC;QAExG,OAAO,IAAI,UAAU,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;IACtD,CAAC;CACF;AA3HD,gDA2HC;AAED;;GAEG;AACH,MAAa,0BAA2B,SAAQ,kBAAkB;IAChE;;;OAGG;IACI,MAAM,CAAU,eAAe,CAAC,IAAgB;QACrD,IAAI,IAAI,CAAC,MAAM,KAAK,EAAE,EAAE;YACtB,MAAM,IAAI,KAAK,CAAC,2BAA2B,IAAI,CAAC,MAAM,wBAAwB,CAAC,CAAC;SACjF;QACD,OAAO,IAAI,0BAA0B,CACnC,oBAAoB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EACvC,oBAAoB,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EACxC,IAAI,CAAC,EAAE,CAAC,CACT,CAAC;IACJ,CAAC;IAID,YAAmB,CAAa,EAAE,CAAa,EAAE,QAAgB;QAC/D,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAEZ,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,EAAE;YAC/B,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC;SAC/D;QAED,IAAI,QAAQ,GAAG,CAAC,IAAI,QAAQ,GAAG,CAAC,EAAE;YAChC,MAAM,IAAI,KAAK,CAAC,mDAAmD,CAAC,CAAC;SACtE;QAED,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;IAC3B,CAAC;IAED;;;;OAIG;IACa,aAAa;QAC3B,OAAO,IAAI,UAAU,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;IACvE,CAAC;CACF;AAxCD,gEAwCC"}
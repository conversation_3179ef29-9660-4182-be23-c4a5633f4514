{"version": 3, "file": "keccak.js", "sourceRoot": "", "sources": ["../src/keccak.ts"], "names": [], "mappings": ";;;AAAA,6CAAgD;AAGhD,mCAA2C;AAE3C,MAAa,SAAS;IAKpB,YAAmB,SAAsB;QAJzB,cAAS,GAAG,GAAG,GAAG,CAAC,CAAC;QAEnB,SAAI,GAAG,iBAAU,CAAC,MAAM,EAAE,CAAC;QAG1C,IAAI,SAAS,EAAE;YACb,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;SACxB;IACH,CAAC;IAEM,MAAM,CAAC,IAAgB;QAC5B,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAA,wBAAgB,EAAC,IAAI,CAAC,CAAC,CAAC;QACzC,OAAO,IAAI,CAAC;IACd,CAAC;IAEM,MAAM;QACX,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;IAC5B,CAAC;CACF;AAnBD,8BAmBC;AAED,wEAAwE;AACxE,SAAgB,SAAS,CAAC,IAAgB;IACxC,OAAO,IAAI,SAAS,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,CAAC;AACtC,CAAC;AAFD,8BAEC"}
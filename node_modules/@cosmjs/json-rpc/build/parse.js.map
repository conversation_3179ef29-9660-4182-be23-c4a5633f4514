{"version": 3, "file": "parse.js", "sourceRoot": "", "sources": ["../src/parse.ts"], "names": [], "mappings": ";;;AAAA,mDAMyB;AAUzB;;;;GAIG;AACH,SAAgB,cAAc,CAAC,IAAa;IAC1C,IAAI,CAAC,IAAA,0CAA0B,EAAC,IAAI,CAAC,EAAE;QACrC,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;KAC5D;IAED,MAAM,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC;IACnB,IAAI,OAAO,EAAE,KAAK,QAAQ,IAAI,OAAO,EAAE,KAAK,QAAQ,EAAE;QACpD,OAAO,IAAI,CAAC;KACb;IACD,OAAO,EAAE,CAAC;AACZ,CAAC;AAVD,wCAUC;AAED,SAAgB,mBAAmB,CAAC,IAAa;IAC/C,IAAI,CAAC,IAAA,0CAA0B,EAAC,IAAI,CAAC,EAAE;QACrC,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;KAC5D;IAED,IAAI,IAAI,CAAC,OAAO,KAAK,KAAK,EAAE;QAC1B,MAAM,IAAI,KAAK,CAAC,mCAAmC,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;KACpE;IAED,MAAM,EAAE,GAAG,cAAc,CAAC,IAAI,CAAC,CAAC;IAChC,IAAI,EAAE,KAAK,IAAI,EAAE;QACf,MAAM,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAC;KACrC;IAED,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;IAC3B,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;QAC9B,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;KACzC;IAED,IAAI,CAAC,IAAA,qCAAqB,EAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAA,0CAA0B,EAAC,IAAI,CAAC,MAAM,CAAC,EAAE;QACnF,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;KACzC;IAED,OAAO;QACL,OAAO,EAAE,KAAK;QACd,EAAE,EAAE,EAAE;QACN,MAAM,EAAE,MAAM;QACd,MAAM,EAAE,IAAI,CAAC,MAAM;KACpB,CAAC;AACJ,CAAC;AA7BD,kDA6BC;AAED,SAAS,UAAU,CAAC,KAA+B;IACjD,IAAI,OAAO,KAAK,CAAC,IAAI,KAAK,QAAQ,EAAE;QAClC,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;KAC1D;IAED,IAAI,OAAO,KAAK,CAAC,OAAO,KAAK,QAAQ,EAAE;QACrC,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAC;KAC7D;IAED,IAAI,kBAAmD,CAAC;IAExD,IAAI,KAAK,CAAC,IAAI,KAAK,SAAS,EAAE;QAC5B,kBAAkB,GAAG,SAAS,CAAC;KAChC;SAAM,IAAI,IAAA,qCAAqB,EAAC,KAAK,CAAC,IAAI,CAAC,EAAE;QAC5C,kBAAkB,GAAG,KAAK,CAAC,IAAI,CAAC;KACjC;SAAM;QACL,MAAM,IAAI,KAAK,CAAC,mEAAmE,CAAC,CAAC;KACtF;IAED,OAAO;QACL,IAAI,EAAE,KAAK,CAAC,IAAI;QAChB,OAAO,EAAE,KAAK,CAAC,OAAO;QACtB,GAAG,CAAC,kBAAkB,KAAK,SAAS,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,kBAAkB,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;KAC1E,CAAC;AACJ,CAAC;AAED,mDAAmD;AACnD,SAAgB,yBAAyB,CAAC,IAAa;IACrD,IAAI,CAAC,IAAA,0CAA0B,EAAC,IAAI,CAAC,EAAE;QACrC,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;KAC5D;IAED,IAAI,IAAI,CAAC,OAAO,KAAK,KAAK,EAAE;QAC1B,MAAM,IAAI,KAAK,CAAC,mCAAmC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;KAC5E;IAED,MAAM,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC;IACnB,IAAI,OAAO,EAAE,KAAK,QAAQ,IAAI,OAAO,EAAE,KAAK,QAAQ,IAAI,EAAE,KAAK,IAAI,EAAE;QACnE,MAAM,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAC;KACrC;IAED,IAAI,OAAO,IAAI,CAAC,KAAK,KAAK,WAAW,IAAI,CAAC,IAAA,0CAA0B,EAAC,IAAI,CAAC,KAAK,CAAC,EAAE;QAChF,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;KACxC;IAED,OAAO;QACL,OAAO,EAAE,KAAK;QACd,EAAE,EAAE,EAAE;QACN,KAAK,EAAE,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC;KAC9B,CAAC;AACJ,CAAC;AAvBD,8DAuBC;AAED,qDAAqD;AACrD,SAAgB,2BAA2B,CAAC,IAAa;IACvD,IAAI,CAAC,IAAA,0CAA0B,EAAC,IAAI,CAAC,EAAE;QACrC,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;KAC5D;IAED,IAAI,IAAI,CAAC,OAAO,KAAK,KAAK,EAAE;QAC1B,MAAM,IAAI,KAAK,CAAC,mCAAmC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;KAC5E;IAED,MAAM,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC;IACnB,IAAI,OAAO,EAAE,KAAK,QAAQ,IAAI,OAAO,EAAE,KAAK,QAAQ,EAAE;QACpD,MAAM,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAC;KACrC;IAED,IAAI,OAAO,IAAI,CAAC,MAAM,KAAK,WAAW,EAAE;QACtC,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;KACzC;IAED,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;IAE3B,OAAO;QACL,OAAO,EAAE,KAAK;QACd,EAAE,EAAE,EAAE;QACN,MAAM,EAAE,MAAM;KACf,CAAC;AACJ,CAAC;AAzBD,kEAyBC;AAED;;;GAGG;AACH,SAAgB,oBAAoB,CAAC,IAAa;IAChD,IAAI,QAAyB,CAAC;IAC9B,IAAI;QACF,QAAQ,GAAG,yBAAyB,CAAC,IAAI,CAAC,CAAC;KAC5C;IAAC,OAAO,CAAC,EAAE;QACV,QAAQ,GAAG,2BAA2B,CAAC,IAAI,CAAC,CAAC;KAC9C;IACD,OAAO,QAAQ,CAAC;AAClB,CAAC;AARD,oDAQC"}
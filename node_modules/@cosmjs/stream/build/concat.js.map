{"version": 3, "file": "concat.js", "sourceRoot": "", "sources": ["../src/concat.ts"], "names": [], "mappings": ";;;AAAA,qCAAyD;AAEzD;;;;;;;;;;;;;;;;GAgBG;AACH,SAAgB,MAAM,CAAI,GAAG,OAAyB;IACpD,MAAM,aAAa,GAAG,IAAI,KAAK,EAAgB,CAAC;IAChD,MAAM,MAAM,GAAG,IAAI,KAAK,EAAO,CAAC,CAAC,uBAAuB;IACxD,MAAM,gBAAgB,GAAG,IAAI,GAAG,EAAU,CAAC;IAC3C,IAAI,iBAAiB,GAAG,CAAC,CAAC;IAE1B,SAAS,KAAK;QACZ,OAAO,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE;YAC/B,oEAAoE;YACpE,MAAM,YAAY,GAAG,aAAa,CAAC,KAAK,EAAG,CAAC;YAC5C,YAAY,CAAC,WAAW,EAAE,CAAC;SAC5B;QAED,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;QAClB,gBAAgB,CAAC,KAAK,EAAE,CAAC;QACzB,iBAAiB,GAAG,CAAC,CAAC;IACxB,CAAC;IAED,MAAM,QAAQ,GAAgB;QAC5B,KAAK,EAAE,CAAC,QAAQ,EAAE,EAAE;YAClB,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;YAExC,SAAS,mBAAmB,CAAC,WAAmB;gBAC9C,iDAAiD;gBACjD,OAAO,IAAI,EAAE;oBACX,MAAM,OAAO,GAAG,MAAM,CAAC,WAAW,CAAC,CAAC,KAAK,EAAE,CAAC;oBAC5C,IAAI,OAAO,KAAK,SAAS,EAAE;wBACzB,OAAO;qBACR;oBACD,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;iBACxB;YACH,CAAC;YAED,SAAS,MAAM;gBACb,OAAO,iBAAiB,IAAI,OAAO,CAAC,MAAM,CAAC;YAC7C,CAAC;YAED,IAAI,MAAM,EAAE,EAAE;gBACZ,QAAQ,CAAC,QAAQ,EAAE,CAAC;gBACpB,OAAO;aACR;YAED,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;gBAChC,aAAa,CAAC,IAAI,CAChB,MAAM,CAAC,SAAS,CAAC;oBACf,IAAI,EAAE,CAAC,KAAK,EAAE,EAAE;wBACd,IAAI,KAAK,KAAK,iBAAiB,EAAE;4BAC/B,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;yBACtB;6BAAM;4BACL,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;yBAC3B;oBACH,CAAC;oBACD,QAAQ,EAAE,GAAG,EAAE;wBACb,gBAAgB,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;wBAE5B,OAAO,gBAAgB,CAAC,GAAG,CAAC,iBAAiB,CAAC,EAAE;4BAC9C,8CAA8C;4BAC9C,mBAAmB,CAAC,iBAAiB,CAAC,CAAC;4BACvC,iBAAiB,EAAE,CAAC;yBACrB;wBAED,IAAI,MAAM,EAAE,EAAE;4BACZ,QAAQ,CAAC,QAAQ,EAAE,CAAC;yBACrB;6BAAM;4BACL,yEAAyE;4BACzE,mBAAmB,CAAC,iBAAiB,CAAC,CAAC;yBACxC;oBACH,CAAC;oBACD,KAAK,EAAE,CAAC,KAAK,EAAE,EAAE;wBACf,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;wBACtB,KAAK,EAAE,CAAC;oBACV,CAAC;iBACF,CAAC,CACH,CAAC;YACJ,CAAC,CAAC,CAAC;QACL,CAAC;QACD,IAAI,EAAE,GAAG,EAAE;YACT,KAAK,EAAE,CAAC;QACV,CAAC;KACF,CAAC;IAEF,OAAO,gBAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;AACjC,CAAC;AAlFD,wBAkFC"}
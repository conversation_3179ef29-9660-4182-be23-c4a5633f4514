import { MemoryStream } from "xstream";
import { DefaultValueProducer } from "./defaultvalueproducer";
export type SearchFunction<T> = (value: T) => boolean;
/**
 * A read only wrapper around DefaultValueProducer that allows
 * to synchronously get the current value using the .value property
 * and listen to to updates by suscribing to the .updates stream
 */
export declare class ValueAndUpdates<T> {
    readonly updates: MemoryStream<T>;
    get value(): T;
    private readonly producer;
    constructor(producer: DefaultValueProducer<T>);
    /**
     * Resolves as soon as search value is found.
     *
     * @param search either a value or a function that must return true when found
     * @returns the value of the update that caused the search match
     */
    waitFor(search: SearchFunction<T> | T): Promise<T>;
}

{"version": 3, "file": "signdoc.js", "sourceRoot": "", "sources": ["../src/signdoc.ts"], "names": [], "mappings": ";;;AAAA,yDAAyD;AACzD,+CAA0C;AAC1C,uCAAsC;AAiCtC,SAAS,YAAY,CAAC,GAAQ;IAC5B,IAAI,OAAO,GAAG,KAAK,QAAQ,IAAI,GAAG,KAAK,IAAI,EAAE;QAC3C,OAAO,GAAG,CAAC;KACZ;IACD,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;QACtB,OAAO,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;KAC9B;IACD,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC;IAC3C,MAAM,MAAM,GAAwB,EAAE,CAAC;IACvC,sFAAsF;IACtF,UAAU,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,EAAE;QACzB,MAAM,CAAC,GAAG,CAAC,GAAG,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;IACvC,CAAC,CAAC,CAAC;IACH,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,uDAAuD;AACvD,6EAA6E;AAC7E,SAAgB,mBAAmB,CAAC,GAAQ;IAC1C,OAAO,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC;AAC3C,CAAC;AAFD,kDAEC;AAED,SAAgB,WAAW,CACzB,IAAyB,EACzB,GAAW,EACX,OAAe,EACf,IAAwB,EACxB,aAA8B,EAC9B,QAAyB,EACzB,cAAuB;IAEvB,OAAO;QACL,QAAQ,EAAE,OAAO;QACjB,cAAc,EAAE,aAAM,CAAC,UAAU,CAAC,aAAa,CAAC,QAAQ,EAAE,CAAC,CAAC,QAAQ,EAAE;QACtE,QAAQ,EAAE,aAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC,CAAC,QAAQ,EAAE;QAC3D,GAAG,EAAE,GAAG;QACR,IAAI,EAAE,IAAI;QACV,IAAI,EAAE,IAAI,IAAI,EAAE;QAChB,GAAG,CAAC,cAAc,IAAI,EAAE,cAAc,EAAE,cAAc,CAAC,QAAQ,EAAE,EAAE,CAAC;KACrE,CAAC;AACJ,CAAC;AAlBD,kCAkBC;AAED;;;;;;;;;;;GAWG;AACH,SAAgB,gBAAgB,CAAC,KAAa;IAC5C,+FAA+F;IAC/F,qGAAqG;IACrG,MAAM,GAAG,GAAG,IAAI,CAAC;IACjB,MAAM,EAAE,GAAG,IAAI,CAAC;IAChB,MAAM,EAAE,GAAG,IAAI,CAAC;IAChB,OAAO,KAAK,CAAC,OAAO,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC,OAAO,CAAC,EAAE,EAAE,SAAS,CAAC,CAAC,OAAO,CAAC,EAAE,EAAE,SAAS,CAAC,CAAC;AACrF,CAAC;AAPD,4CAOC;AAED,SAAgB,gBAAgB,CAAC,OAAmB;IAClD,MAAM,UAAU,GAAG,gBAAgB,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC,CAAC;IAClE,OAAO,IAAA,iBAAM,EAAC,UAAU,CAAC,CAAC;AAC5B,CAAC;AAHD,4CAGC"}
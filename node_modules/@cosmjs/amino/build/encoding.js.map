{"version": 3, "file": "encoding.js", "sourceRoot": "", "sources": ["../src/encoding.ts"], "names": [], "mappings": ";;;AAAA,+CAA8F;AAC9F,uCAAsC;AACtC,yCAAuD;AAEvD,uCASmB;AAEnB;;;GAGG;AACH,SAAgB,qBAAqB,CAAC,MAAkB;IACtD,IAAI,MAAM,CAAC,MAAM,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,IAAI,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,EAAE;QACtE,MAAM,IAAI,KAAK,CAAC,mFAAmF,CAAC,CAAC;KACtG;IACD,OAAO;QACL,IAAI,EAAE,oBAAU,CAAC,SAAS;QAC1B,KAAK,EAAE,IAAA,mBAAQ,EAAC,MAAM,CAAC;KACxB,CAAC;AACJ,CAAC;AARD,sDAQC;AAED;;;GAGG;AACH,SAAgB,mBAAmB,CAAC,MAAkB;IACpD,IAAI,MAAM,CAAC,MAAM,KAAK,EAAE,EAAE;QACxB,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAC;KAC7D;IACD,OAAO;QACL,IAAI,EAAE,oBAAU,CAAC,OAAO;QACxB,KAAK,EAAE,IAAA,mBAAQ,EAAC,MAAM,CAAC;KACxB,CAAC;AACJ,CAAC;AARD,kDAQC;AAED,6EAA6E;AAC7E,wKAAwK;AACxK,6CAA6C;AAC7C,MAAM,0BAA0B,GAAG,IAAA,kBAAO,EAAC,UAAU,GAAG,IAAI,CAAC,kBAAkB,CAAC,CAAC;AACjF,MAAM,wBAAwB,GAAG,IAAA,kBAAO,EAAC,UAAU,GAAG,IAAI,CAAC,kBAAkB,CAAC,CAAC;AAC/E,MAAM,wBAAwB,GAAG,IAAA,kBAAO,EAAC,UAAU,GAAG,IAAI,CAAC,kBAAkB,CAAC,CAAC;AAC/E,mGAAmG;AACnG,MAAM,kCAAkC,GAAG,IAAA,kBAAO,EAAC,UAAU,CAAC,kCAAkC,CAAC,CAAC;AAElG;;GAEG;AACH,SAAgB,iBAAiB,CAAC,IAAgB;IAChD,IAAI,IAAA,8BAAsB,EAAC,IAAI,EAAE,0BAA0B,CAAC,EAAE;QAC5D,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,0BAA0B,CAAC,MAAM,CAAC,CAAC;QAC3D,IAAI,IAAI,CAAC,MAAM,KAAK,EAAE,EAAE;YACtB,MAAM,IAAI,KAAK,CAAC,4EAA4E,CAAC,CAAC;SAC/F;QACD,OAAO;YACL,IAAI,EAAE,oBAAU,CAAC,SAAS;YAC1B,KAAK,EAAE,IAAA,mBAAQ,EAAC,IAAI,CAAC;SACtB,CAAC;KACH;SAAM,IAAI,IAAA,8BAAsB,EAAC,IAAI,EAAE,wBAAwB,CAAC,EAAE;QACjE,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,wBAAwB,CAAC,MAAM,CAAC,CAAC;QACzD,IAAI,IAAI,CAAC,MAAM,KAAK,EAAE,EAAE;YACtB,MAAM,IAAI,KAAK,CAAC,+DAA+D,CAAC,CAAC;SAClF;QACD,OAAO;YACL,IAAI,EAAE,oBAAU,CAAC,OAAO;YACxB,KAAK,EAAE,IAAA,mBAAQ,EAAC,IAAI,CAAC;SACtB,CAAC;KACH;SAAM,IAAI,IAAA,8BAAsB,EAAC,IAAI,EAAE,wBAAwB,CAAC,EAAE;QACjE,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,wBAAwB,CAAC,MAAM,CAAC,CAAC;QACzD,IAAI,IAAI,CAAC,MAAM,KAAK,EAAE,EAAE;YACtB,MAAM,IAAI,KAAK,CAAC,+DAA+D,CAAC,CAAC;SAClF;QACD,OAAO;YACL,IAAI,EAAE,oBAAU,CAAC,OAAO;YACxB,KAAK,EAAE,IAAA,mBAAQ,EAAC,IAAI,CAAC;SACtB,CAAC;KACH;SAAM,IAAI,IAAA,8BAAsB,EAAC,IAAI,EAAE,kCAAkC,CAAC,EAAE;QAC3E,mEAAmE;QACnE,OAAO,oBAAoB,CAAC,IAAI,CAAC,CAAC;KACnC;SAAM;QACL,MAAM,IAAI,KAAK,CAAC,uDAAuD,GAAG,IAAA,gBAAK,EAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;KACpG;AACH,CAAC;AAlCD,8CAkCC;AAED;;;;;GAKG;AACH,SAAgB,kBAAkB,CAAC,WAAmB;IACpD,MAAM,EAAE,IAAI,EAAE,GAAG,IAAA,qBAAU,EAAC,WAAW,CAAC,CAAC;IACzC,OAAO,iBAAiB,CAAC,IAAI,CAAC,CAAC;AACjC,CAAC;AAHD,gDAGC;AAED;;;;GAIG;AACH,SAAS,aAAa,CAAC,MAAgB;IACrC,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;QACrB,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;KAC7C;IACD,IAAI,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG,EAAE;QACnB,MAAM,IAAI,KAAK,CACb,qLAAqL,CACtL,CAAC;KACH;IACD,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACxB,CAAC;AAED;;;;;GAKG;AACH,SAAS,oBAAoB,CAAC,IAAgB;IAC5C,MAAM,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAEhC,gCAAgC;IAChC,MAAM,gBAAgB,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,kCAAkC,CAAC,MAAM,CAAC,CAAC;IACrF,IAAI,CAAC,IAAA,8BAAsB,EAAC,gBAAgB,EAAE,kCAAkC,CAAC,EAAE;QACjF,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;KAC7C;IAED,gCAAgC;IAChC,IAAI,MAAM,CAAC,KAAK,EAAE,IAAI,IAAI,EAAE;QAC1B,MAAM,IAAI,KAAK,CAAC,gEAAgE,CAAC,CAAC;KACnF;IAED,iBAAiB;IACjB,MAAM,CAAC,SAAS,EAAE,oBAAoB,CAAC,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC;IAChE,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,oBAAoB,CAAC,CAAC;IAEvC,4BAA4B;IAC5B,MAAM,OAAO,GAAG,EAAE,CAAC;IACnB,OAAO,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;QACxB,gCAAgC;QAChC,IAAI,MAAM,CAAC,KAAK,EAAE,IAAI,IAAI,EAAE;YAC1B,MAAM,IAAI,KAAK,CAAC,gFAAgF,CAAC,CAAC;SACnG;QAED,qBAAqB;QACrB,MAAM,CAAC,YAAY,EAAE,qBAAqB,CAAC,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC;QACpE,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,qBAAqB,CAAC,CAAC;QAExC,iCAAiC;QACjC,IAAI,MAAM,CAAC,MAAM,GAAG,YAAY,EAAE;YAChC,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;SAClD;QAED,qCAAqC;QACrC,MAAM,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC;QACrD,MAAM,MAAM,GAAG,iBAAiB,CAAC,UAAU,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC;QACjE,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;KACtB;IAED,OAAO;QACL,IAAI,EAAE,oBAAU,CAAC,iBAAiB;QAClC,KAAK,EAAE;YACL,SAAS,EAAE,SAAS,CAAC,QAAQ,EAAE;YAC/B,OAAO,EAAE,OAAO;SACjB;KACF,CAAC;AACJ,CAAC;AAED;;;;;GAKG;AACH,SAAS,aAAa,CAAC,KAAsB;IAC3C,MAAM,OAAO,GAAG,aAAM,CAAC,UAAU,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC;IAC/D,IAAI,OAAO,GAAG,GAAG,EAAE;QACjB,MAAM,IAAI,KAAK,CACb,yLAAyL,CAC1L,CAAC;KACH;IACD,OAAO,CAAC,OAAO,CAAC,CAAC;AACnB,CAAC;AAED;;GAEG;AACH,SAAgB,iBAAiB,CAAC,MAAc;IAC9C,IAAI,IAAA,mCAAyB,EAAC,MAAM,CAAC,EAAE;QACrC,MAAM,GAAG,GAAG,KAAK,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;QAC3D,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,sBAAsB;QACtC,GAAG,CAAC,IAAI,CAAC,GAAG,aAAa,CAAC,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC;QACnD,KAAK,MAAM,UAAU,IAAI,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,EAAE;YAC9E,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,sBAAsB;YACtC,GAAG,CAAC,IAAI,CAAC,GAAG,aAAa,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;YAC9C,GAAG,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC,CAAC;SACzB;QACD,OAAO,IAAI,UAAU,CAAC,GAAG,CAAC,CAAC;KAC5B;SAAM,IAAI,IAAA,yBAAe,EAAC,MAAM,CAAC,EAAE;QAClC,OAAO,IAAI,UAAU,CAAC,CAAC,GAAG,wBAAwB,EAAE,GAAG,IAAA,qBAAU,EAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;KACnF;SAAM,IAAI,IAAA,2BAAiB,EAAC,MAAM,CAAC,EAAE;QACpC,OAAO,IAAI,UAAU,CAAC,CAAC,GAAG,0BAA0B,EAAE,GAAG,IAAA,qBAAU,EAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;KACrF;SAAM;QACL,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;KAC5C;AACH,CAAC;AAlBD,8CAkBC;AAED;;;;;GAKG;AACH,SAAgB,kBAAkB,CAAC,MAAc,EAAE,MAAc;IAC/D,OAAO,IAAA,mBAAQ,EAAC,MAAM,EAAE,iBAAiB,CAAC,MAAM,CAAC,CAAC,CAAC;AACrD,CAAC;AAFD,gDAEC"}
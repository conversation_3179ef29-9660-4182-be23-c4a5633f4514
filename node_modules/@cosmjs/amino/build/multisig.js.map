{"version": 3, "file": "multisig.js", "sourceRoot": "", "sources": ["../src/multisig.ts"], "names": [], "mappings": ";;;AAAA,+CAAyC;AACzC,uCAAsC;AAEtC,2CAAiD;AAGjD;;;;;;GAMG;AACH,SAAgB,aAAa,CAAC,CAAa,EAAE,CAAa;IACxD,MAAM,IAAI,GAAG,IAAA,gBAAK,EAAC,CAAC,CAAC,CAAC;IACtB,MAAM,IAAI,GAAG,IAAA,gBAAK,EAAC,CAAC,CAAC,CAAC;IACtB,OAAO,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAClD,CAAC;AAJD,sCAIC;AAED,SAAgB,6BAA6B,CAC3C,OAAgC,EAChC,SAAiB,EACjB,MAAM,GAAG,KAAK;IAEd,MAAM,aAAa,GAAG,IAAI,aAAM,CAAC,SAAS,CAAC,CAAC;IAC5C,IAAI,aAAa,CAAC,QAAQ,EAAE,GAAG,OAAO,CAAC,MAAM,EAAE;QAC7C,MAAM,IAAI,KAAK,CAAC,iBAAiB,aAAa,CAAC,QAAQ,EAAE,+BAA+B,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;KAC3G;IAED,MAAM,UAAU,GAAG,MAAM;QACvB,CAAC,CAAC,OAAO;QACT,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;YACpC,iFAAiF;YACjF,MAAM,UAAU,GAAG,IAAA,8BAAkB,EAAC,GAAG,CAAC,CAAC;YAC3C,MAAM,UAAU,GAAG,IAAA,8BAAkB,EAAC,GAAG,CAAC,CAAC;YAC3C,OAAO,aAAa,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;IACP,OAAO;QACL,IAAI,EAAE,oCAAoC;QAC1C,KAAK,EAAE;YACL,SAAS,EAAE,aAAa,CAAC,QAAQ,EAAE;YACnC,OAAO,EAAE,UAAU;SACpB;KACF,CAAC;AACJ,CAAC;AAzBD,sEAyBC"}
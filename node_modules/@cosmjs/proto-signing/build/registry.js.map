{"version": 3, "file": "registry.js", "sourceRoot": "", "sources": ["../src/registry.ts"], "names": [], "mappings": ";;;AAEA,4DAA8D;AAC9D,gEAA6D;AAC7D,0DAA2D;AAC3D,0DAAuD;AA+CvD,SAAgB,wBAAwB,CAAC,IAAmB;IAC1D,MAAM,MAAM,GAAG,IAA8B,CAAC;IAC9C,OAAO,OAAO,MAAM,CAAC,WAAW,KAAK,UAAU,IAAI,OAAO,MAAM,CAAC,OAAO,IAAI,QAAQ,CAAC;AACvF,CAAC;AAHD,4DAGC;AAED,SAAgB,sBAAsB,CAAC,IAAmB;IACxD,OAAO,OAAQ,IAA6B,CAAC,WAAW,KAAK,UAAU,CAAC;AAC1E,CAAC;AAFD,wDAEC;AAED,SAAgB,mBAAmB,CAAC,IAAmB;IACrD,OAAO,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC;AACvC,CAAC;AAFD,kDAEC;AAED,MAAM,eAAe,GAAG;IACtB,UAAU,EAAE,2BAA2B;IACvC,aAAa,EAAE,8BAA8B;IAC7C,YAAY,EAAE,2BAA2B;IACzC,SAAS,EAAE,sBAAsB;CAClC,CAAC;AAyBF,SAAgB,oBAAoB,CAAC,YAA0B;IAC7D,OAAQ,YAAmC,CAAC,OAAO,KAAK,2BAA2B,CAAC;AACtF,CAAC;AAFD,oDAEC;AAED,MAAa,QAAQ;IAGnB;;;;;;;;;;;OAWG;IACH,YAAmB,WAA+C;QAChE,MAAM,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG,eAAe,CAAC;QACtD,IAAI,CAAC,KAAK,GAAG,WAAW;YACtB,CAAC,CAAC,IAAI,GAAG,CAAwB,CAAC,GAAG,WAAW,CAAC,CAAC;YAClD,CAAC,CAAC,IAAI,GAAG,CAAwB;gBAC7B,CAAC,UAAU,EAAE,WAAI,CAAC;gBAClB,CAAC,aAAa,EAAE,YAAO,CAAC;aACzB,CAAC,CAAC;IACT,CAAC;IAEM,QAAQ,CAAC,OAAe,EAAE,IAAmB;QAClD,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;IAChC,CAAC;IAED;;;;;;;;;;;;;;;OAeG;IACI,UAAU,CAAC,OAAe;QAC/B,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;IACjC,CAAC;IAEO,mBAAmB,CAAC,OAAe;QACzC,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QACtC,IAAI,CAAC,IAAI,EAAE;YACT,MAAM,IAAI,KAAK,CAAC,0BAA0B,OAAO,EAAE,CAAC,CAAC;SACtD;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;;;OAMG;IACI,MAAM,CAAC,YAA0B;QACtC,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,YAAY,CAAC;QACxC,IAAI,oBAAoB,CAAC,YAAY,CAAC,EAAE;YACtC,OAAO,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;SACjC;QACD,MAAM,IAAI,GAAG,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;QAC/C,MAAM,QAAQ,GACZ,wBAAwB,CAAC,IAAI,CAAC,IAAI,sBAAsB,CAAC,IAAI,CAAC;YAC5D,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC;YACzB,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QACzB,OAAO,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,MAAM,EAAE,CAAC;IACxC,CAAC;IAED;;;OAGG;IACI,WAAW,CAAC,YAA0B;QAC3C,MAAM,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;QAC9C,OAAO,SAAG,CAAC,WAAW,CAAC;YACrB,OAAO,EAAE,YAAY,CAAC,OAAO;YAC7B,KAAK,EAAE,WAAW;SACnB,CAAC,CAAC;IACL,CAAC;IAEM,YAAY,CAAC,YAAyB;QAC3C,MAAM,eAAe,GAAG,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC;QAC1F,MAAM,MAAM,GAAG,WAAM,CAAC,WAAW,CAAC;YAChC,GAAG,YAAY;YACf,aAAa,EAAE,MAAM,CAAC,YAAY,CAAC,aAAa,EAAE,QAAQ,EAAE,IAAI,GAAG,CAAC;YACpE,QAAQ,EAAE,eAAe;SAC1B,CAAC,CAAC;QACH,OAAO,WAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,CAAC;IACxC,CAAC;IAEM,MAAM,CAAC,EAAE,OAAO,EAAE,KAAK,EAAgB;QAC5C,IAAI,OAAO,KAAK,eAAe,CAAC,YAAY,EAAE;YAC5C,OAAO,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;SACjC;QACD,MAAM,IAAI,GAAG,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;QAC/C,MAAM,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QACnC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,CAAgB,EAAE,EAAE;YAC5D,IAAI,OAAO,MAAM,KAAK,WAAW,IAAI,OAAO,MAAM,CAAC,QAAQ,KAAK,WAAW,IAAI,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;gBACnG,OAAO,CAAC,GAAG,CAAC,GAAG,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;aACrC;QACH,CAAC,CAAC,CAAC;QACH,OAAO,OAAO,CAAC;IACjB,CAAC;IAEM,YAAY,CAAC,MAAkB;QACpC,MAAM,aAAa,GAAG,WAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QAE5C,OAAO;YACL,GAAG,aAAa;YAChB,QAAQ,EAAE,aAAa,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAO,EAAE,EAAE;gBACxE,IAAI,CAAC,OAAO,EAAE;oBACZ,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;iBAC5C;gBACD,IAAI,CAAC,KAAK,EAAE;oBACV,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;iBACzC;gBACD,OAAO,IAAI,CAAC,MAAM,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,CAAC;YACzC,CAAC,CAAC;SACH,CAAC;IACJ,CAAC;CACF;AAjID,4BAiIC"}
{"version": 3, "file": "queueingstreamingsocket.js", "sourceRoot": "", "sources": ["../src/queueingstreamingsocket.ts"], "names": [], "mappings": ";;;AAAA,2CAAuE;AACvE,qCAAqD;AAGrD,uDAAoD;AAEpD,IAAY,gBAKX;AALD,WAAY,gBAAgB;IAC1B,qEAAW,CAAA;IACX,mEAAU,CAAA;IACV,iEAAS,CAAA;IACT,uEAAY,CAAA;AACd,CAAC,EALW,gBAAgB,GAAhB,wBAAgB,KAAhB,wBAAgB,QAK3B;AAED;;GAEG;AACH,MAAa,uBAAuB;IAalC,YAAmB,GAAW,EAAE,OAAO,GAAG,KAAM,EAAE,kBAA+B;QAPhE,UAAK,GAAa,EAAE,CAAC;QAE9B,sBAAiB,GAAG,KAAK,CAAC;QAMhC,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;QACf,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,kBAAkB,GAAG,kBAAkB,CAAC;QAE7C,MAAM,aAAa,GAAkB;YACnC,KAAK,EAAE,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,qBAAqB,GAAG,QAAQ,CAAC;YAC5D,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC,IAAI,CAAC,qBAAqB,GAAG,SAAS,CAAC;SACrD,CAAC;QACF,IAAI,CAAC,MAAM,GAAG,gBAAM,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;QAC3C,IAAI,CAAC,wBAAwB,GAAG,IAAI,6BAAoB,CAAmB,gBAAgB,CAAC,WAAW,CAAC,CAAC;QACzG,IAAI,CAAC,gBAAgB,GAAG,IAAI,wBAAe,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;QAE3E,IAAI,CAAC,MAAM,GAAG,IAAI,iCAAe,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QAC1D,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC;YAC3B,IAAI,EAAE,CAAC,KAAK,EAAE,EAAE;gBACd,IAAI,CAAC,IAAI,CAAC,qBAAqB;oBAAE,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;gBACnF,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACzC,CAAC;YACD,KAAK,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,wBAAwB,CAAC,MAAM,CAAC,gBAAgB,CAAC,YAAY,CAAC;SACjF,CAAC,CAAC;IACL,CAAC;IAEM,OAAO;QACZ,IAAI,CAAC,wBAAwB,CAAC,MAAM,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC;QAClE,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CACxB,KAAK,IAAI,EAAE;YACT,IAAI,CAAC,wBAAwB,CAAC,MAAM,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;YACjE,OAAO,IAAI,CAAC,YAAY,EAAE,CAAC;QAC7B,CAAC,EACD,GAAG,EAAE,CAAC,IAAI,CAAC,wBAAwB,CAAC,MAAM,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAC1E,CAAC;QACF,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;IACxB,CAAC;IAEM,UAAU;QACf,IAAI,CAAC,wBAAwB,CAAC,MAAM,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC;QACpE,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;IAC3B,CAAC;IAEM,SAAS;QACd,IAAI,CAAC,MAAM,GAAG,IAAI,iCAAe,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QAC1D,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC;YAC3B,IAAI,EAAE,CAAC,KAAK,EAAE,EAAE;gBACd,IAAI,CAAC,IAAI,CAAC,qBAAqB;oBAAE,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;gBACnF,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACzC,CAAC;YACD,KAAK,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,wBAAwB,CAAC,MAAM,CAAC,gBAAgB,CAAC,YAAY,CAAC;SACjF,CAAC,CAAC;QACH,mEAAmE;QACnE,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,EAAE;YAC9B,IAAI,IAAI,CAAC,kBAAkB,EAAE;gBAC3B,IAAI,CAAC,kBAAkB,EAAE,CAAC;aAC3B;QACH,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,OAAO,EAAE,CAAC;IACjB,CAAC;IAEM,cAAc;QACnB,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;IAC3B,CAAC;IAEM,YAAY,CAAC,OAAe;QACjC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACzB,uDAAuD;QACvD,mEAAmE;QACnE,IAAI,CAAC,YAAY,EAAE,CAAC;IACtB,CAAC;IAEO,KAAK,CAAC,YAAY;QACxB,IAAI,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,gBAAgB,CAAC,KAAK,KAAK,gBAAgB,CAAC,SAAS,EAAE;YACxF,OAAO;SACR;QACD,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;QAE9B,IAAI,OAA2B,CAAC;QAChC,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE;YACrC,IAAI;gBACF,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBAChC,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC;aAChC;YAAC,OAAO,KAAK,EAAE;gBACd,kFAAkF;gBAClF,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;gBAC5B,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC;gBAC/B,OAAO;aACR;SACF;IACH,CAAC;CACF;AArGD,0DAqGC"}
{"version": 3, "file": "httpclient.js", "sourceRoot": "", "sources": ["../../src/rpcclients/httpclient.ts"], "names": [], "mappings": ";;;AAAA,+CAK0B;AAE1B,iCAA8B;AAC9B,2CAAqD;AAgBrD,MAAa,UAAU;IAIrB,YAAmB,QAA+B;QAChD,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE;YAChC,IAAI,CAAC,IAAA,uBAAW,EAAC,QAAQ,CAAC,EAAE;gBAC1B,MAAM,IAAI,KAAK,CAAC,uEAAuE,CAAC,CAAC;aAC1F;YACD,IAAI,CAAC,GAAG,GAAG,QAAQ,CAAC;SACrB;aAAM;YACL,IAAI,CAAC,GAAG,GAAG,QAAQ,CAAC,GAAG,CAAC;YACxB,IAAI,CAAC,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC;SACjC;IACH,CAAC;IAEM,UAAU;QACf,qBAAqB;IACvB,CAAC;IAEM,KAAK,CAAC,OAAO,CAAC,OAAuB;QAC1C,MAAM,QAAQ,GAAG,IAAA,+BAAoB,EAAC,MAAM,IAAA,WAAI,EAAC,MAAM,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC;QAC3F,IAAI,IAAA,iCAAsB,EAAC,QAAQ,CAAC,EAAE;YACpC,MAAM,IAAI,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;SACjD;QACD,OAAO,QAAQ,CAAC;IAClB,CAAC;CACF;AA3BD,gCA2BC"}
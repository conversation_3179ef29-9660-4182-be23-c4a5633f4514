{"version": 3, "file": "httpbatchclient.js", "sourceRoot": "", "sources": ["../../src/rpcclients/httpbatchclient.ts"], "names": [], "mappings": ";;;AAAA,+CAK0B;AAE1B,iCAA8B;AAE9B,2CAAqD;AASrD,oDAAoD;AACpD,kEAAkE;AAClE,kDAAkD;AAClD,MAAM,6BAA6B,GAA2B;IAC5D,gBAAgB,EAAE,EAAE;IACpB,cAAc,EAAE,EAAE;CACnB,CAAC;AAEF,MAAa,eAAe;IAY1B,YAAmB,QAA+B,EAAE,UAA2C,EAAE;QANhF,UAAK,GAIjB,EAAE,CAAC;QAGN,IAAI,CAAC,OAAO,GAAG;YACb,cAAc,EAAE,OAAO,CAAC,cAAc,IAAI,6BAA6B,CAAC,cAAc;YACtF,gBAAgB,EAAE,OAAO,CAAC,gBAAgB,IAAI,6BAA6B,CAAC,gBAAgB;SAC7F,CAAC;QACF,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE;YAChC,IAAI,CAAC,IAAA,uBAAW,EAAC,QAAQ,CAAC,EAAE;gBAC1B,MAAM,IAAI,KAAK,CAAC,uEAAuE,CAAC,CAAC;aAC1F;YACD,IAAI,CAAC,GAAG,GAAG,QAAQ,CAAC;SACrB;aAAM;YACL,IAAI,CAAC,GAAG,GAAG,QAAQ,CAAC,GAAG,CAAC;YACxB,IAAI,CAAC,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC;SACjC;QACD,IAAI,CAAC,KAAK,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,OAAO,CAAC,gBAAgB,CAAC,CAAC;QACtE,IAAI,CAAC,QAAQ,EAAE,CAAC;IAClB,CAAC;IAEM,UAAU;QACf,IAAI,CAAC,KAAK,IAAI,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACxC,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;IACzB,CAAC;IAEM,KAAK,CAAC,OAAO,CAAC,OAAuB;QAC1C,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC,CAAC;YAE9C,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE;gBACpD,+BAA+B;gBAC/B,IAAI,CAAC,IAAI,EAAE,CAAC;aACb;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,QAAQ;QACd,IACE,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc;YAC5B,CAAC,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC;YAClD,IAAI,CAAC,OAAO,CAAC,cAAc,GAAG,CAAC,EAC/B;YACA,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC;SAC/D;IACH,CAAC;IAED;;;OAGG;IACK,IAAI;QACV,wBAAwB;QACxB,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;QAEhE,IAAI,CAAC,KAAK,CAAC,MAAM;YAAE,OAAO;QAE1B,MAAM,QAAQ,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;QAC7C,MAAM,UAAU,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QAEzD,IAAA,WAAI,EAAC,MAAM,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC,IAAI,CACjD,CAAC,GAAG,EAAE,EAAE;YACN,mDAAmD;YACnD,MAAM,GAAG,GAAG,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;YAE7C,GAAG,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,EAAE;gBACjB,MAAM,GAAG,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC;gBACtD,IAAI,CAAC,GAAG;oBAAE,OAAO;gBACjB,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,GAAG,CAAC;gBAChC,MAAM,QAAQ,GAAG,IAAA,+BAAoB,EAAC,EAAE,CAAC,CAAC;gBAC1C,IAAI,IAAA,iCAAsB,EAAC,QAAQ,CAAC,EAAE;oBACpC,MAAM,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;iBACnD;qBAAM;oBACL,OAAO,CAAC,QAAQ,CAAC,CAAC;iBACnB;YACH,CAAC,CAAC,CAAC;QACL,CAAC,EACD,CAAC,KAAK,EAAE,EAAE;YACR,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE;gBAClC,MAAM,GAAG,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,EAAE,KAAK,SAAS,CAAC,CAAC;gBAC1D,IAAI,CAAC,GAAG;oBAAE,OAAO;gBACjB,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;aACnB;QACH,CAAC,CACF,CAAC;IACJ,CAAC;CACF;AA/FD,0CA+FC"}
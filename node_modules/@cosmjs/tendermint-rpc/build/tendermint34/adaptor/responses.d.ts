import { JsonRpcSuccessResponse } from "@cosmjs/json-rpc";
import { SubscriptionEvent } from "../../rpcclients";
import * as responses from "../responses";
export interface RpcProofOp {
    readonly type: string;
    /** base64 encoded */
    readonly key: string;
    /** base64 encoded */
    readonly data: string;
}
export interface RpcQueryProof {
    readonly ops: readonly RpcProofOp[];
}
interface RpcAttribute {
    /** base64 encoded */
    readonly key: string;
    /** base64 encoded */
    readonly value?: string | null;
}
interface RpcEvent {
    readonly type: string;
    /** Can be omitted (see https://github.com/cosmos/cosmjs/pull/1198) */
    readonly attributes?: readonly RpcAttribute[];
}
export declare function decodeEvent(event: RpcEvent): responses.Event;
type RpcPubkey = {
    readonly type: string;
    /** base64 encoded */
    readonly value: string;
} | {
    readonly Sum: {
        readonly type: string;
        readonly value: {
            /** base64 encoded */
            [algorithm: string]: string;
        };
    };
};
interface RpcValidatorUpdate {
    readonly pub_key: RpcPubkey;
    readonly power?: string;
}
export declare function decodeValidatorUpdate(data: RpcValidatorUpdate): responses.ValidatorUpdate;
interface RpcValidatorGenesis {
    /** hex-encoded */
    readonly address: string;
    readonly pub_key: RpcPubkey;
    readonly power: string;
    readonly name?: string;
}
export declare function decodeValidatorGenesis(data: RpcValidatorGenesis): responses.Validator;
interface RpcValidatorInfo {
    /** hex encoded */
    readonly address: string;
    readonly pub_key: RpcPubkey;
    readonly voting_power: string;
    readonly proposer_priority?: string;
}
export declare function decodeValidatorInfo(data: RpcValidatorInfo): responses.Validator;
export declare class Responses {
    static decodeAbciInfo(response: JsonRpcSuccessResponse): responses.AbciInfoResponse;
    static decodeAbciQuery(response: JsonRpcSuccessResponse): responses.AbciQueryResponse;
    static decodeBlock(response: JsonRpcSuccessResponse): responses.BlockResponse;
    static decodeBlockResults(response: JsonRpcSuccessResponse): responses.BlockResultsResponse;
    static decodeBlockSearch(response: JsonRpcSuccessResponse): responses.BlockSearchResponse;
    static decodeBlockchain(response: JsonRpcSuccessResponse): responses.BlockchainResponse;
    static decodeBroadcastTxSync(response: JsonRpcSuccessResponse): responses.BroadcastTxSyncResponse;
    static decodeBroadcastTxAsync(response: JsonRpcSuccessResponse): responses.BroadcastTxAsyncResponse;
    static decodeBroadcastTxCommit(response: JsonRpcSuccessResponse): responses.BroadcastTxCommitResponse;
    static decodeCommit(response: JsonRpcSuccessResponse): responses.CommitResponse;
    static decodeGenesis(response: JsonRpcSuccessResponse): responses.GenesisResponse;
    static decodeHealth(): responses.HealthResponse;
    static decodeNumUnconfirmedTxs(response: JsonRpcSuccessResponse): responses.NumUnconfirmedTxsResponse;
    static decodeStatus(response: JsonRpcSuccessResponse): responses.StatusResponse;
    static decodeNewBlockEvent(event: SubscriptionEvent): responses.NewBlockEvent;
    static decodeNewBlockHeaderEvent(event: SubscriptionEvent): responses.NewBlockHeaderEvent;
    static decodeTxEvent(event: SubscriptionEvent): responses.TxEvent;
    static decodeTx(response: JsonRpcSuccessResponse): responses.TxResponse;
    static decodeTxSearch(response: JsonRpcSuccessResponse): responses.TxSearchResponse;
    static decodeValidators(response: JsonRpcSuccessResponse): responses.ValidatorsResponse;
}
export {};

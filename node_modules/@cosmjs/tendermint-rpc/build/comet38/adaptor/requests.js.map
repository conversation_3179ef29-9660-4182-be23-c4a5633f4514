{"version": 3, "file": "requests.js", "sourceRoot": "", "sources": ["../../../src/comet38/adaptor/requests.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,yDAAyD;AACzD,+CAAmD;AAGnD,iDAAiD;AACjD,2CAAqD;AACrD,4CAAmD;AACnD,sDAAwC;AAQxC,SAAS,iBAAiB,CAAC,KAAkB;IAC3C,OAAO;QACL,MAAM,EAAE,IAAA,eAAG,EAAC,0BAAa,EAAE,KAAK,CAAC,MAAM,CAAC;KACzC,CAAC;AACJ,CAAC;AAOD,SAAS,6BAA6B,CAAC,KAAuC;IAC5E,OAAO;QACL,SAAS,EAAE,IAAA,eAAG,EAAC,0BAAa,EAAE,KAAK,CAAC,SAAS,CAAC;QAC9C,SAAS,EAAE,IAAA,eAAG,EAAC,0BAAa,EAAE,KAAK,CAAC,SAAS,CAAC;KAC/C,CAAC;AACJ,CAAC;AAQD,SAAS,uBAAuB,CAAC,MAAkC;IACjE,OAAO;QACL,KAAK,EAAE,MAAM,CAAC,KAAK;QACnB,IAAI,EAAE,IAAA,eAAG,EAAC,0BAAa,EAAE,MAAM,CAAC,IAAI,CAAC;QACrC,QAAQ,EAAE,IAAA,eAAG,EAAC,0BAAa,EAAE,MAAM,CAAC,QAAQ,CAAC;QAC7C,QAAQ,EAAE,MAAM,CAAC,QAAQ;KAC1B,CAAC;AACJ,CAAC;AAUD,SAAS,qBAAqB,CAAC,MAAgC;IAC7D,OAAO;QACL,IAAI,EAAE,IAAA,0BAAc,EAAC,MAAM,CAAC,IAAI,CAAC;QACjC,IAAI,EAAE,IAAA,gBAAK,EAAC,MAAM,CAAC,IAAI,CAAC;QACxB,MAAM,EAAE,IAAA,eAAG,EAAC,0BAAa,EAAE,MAAM,CAAC,MAAM,CAAC;QACzC,KAAK,EAAE,MAAM,CAAC,KAAK;KACpB,CAAC;AACJ,CAAC;AAMD,SAAS,uBAAuB,CAAC,MAAkC;IACjE,OAAO;QACL,EAAE,EAAE,IAAA,mBAAQ,EAAC,IAAA,0BAAc,EAAC,MAAM,CAAC,EAAE,CAAC,CAAC;KACxC,CAAC;AACJ,CAAC;AAOD,SAAS,cAAc,CAAC,MAAyB;IAC/C,OAAO;QACL,IAAI,EAAE,IAAA,mBAAQ,EAAC,IAAA,0BAAc,EAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAC3C,KAAK,EAAE,MAAM,CAAC,KAAK;KACpB,CAAC;AACJ,CAAC;AASD,SAAS,oBAAoB,CAAC,MAA+B;IAC3D,OAAO;QACL,KAAK,EAAE,MAAM,CAAC,KAAK;QACnB,KAAK,EAAE,MAAM,CAAC,KAAK;QACnB,IAAI,EAAE,IAAA,eAAG,EAAC,0BAAa,EAAE,MAAM,CAAC,IAAI,CAAC;QACrC,QAAQ,EAAE,IAAA,eAAG,EAAC,0BAAa,EAAE,MAAM,CAAC,QAAQ,CAAC;QAC7C,QAAQ,EAAE,MAAM,CAAC,QAAQ;KAC1B,CAAC;AACJ,CAAC;AAOD,SAAS,sBAAsB,CAAC,MAAiC;IAC/D,OAAO;QACL,MAAM,EAAE,IAAA,eAAG,EAAC,0BAAa,EAAE,MAAM,CAAC,MAAM,CAAC;QACzC,IAAI,EAAE,IAAA,eAAG,EAAC,0BAAa,EAAE,MAAM,CAAC,IAAI,CAAC;QACrC,QAAQ,EAAE,IAAA,eAAG,EAAC,0BAAa,EAAE,MAAM,CAAC,QAAQ,CAAC;KAC9C,CAAC;AACJ,CAAC;AAED,MAAa,MAAM;IACV,MAAM,CAAC,cAAc,CAAC,GAA6B;QACxD,OAAO,IAAA,8BAAoB,EAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IAC1C,CAAC;IAEM,MAAM,CAAC,eAAe,CAAC,GAA8B;QAC1D,OAAO,IAAA,8BAAoB,EAAC,GAAG,CAAC,MAAM,EAAE,qBAAqB,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC;IAC7E,CAAC;IAEM,MAAM,CAAC,WAAW,CAAC,GAA0B;QAClD,OAAO,IAAA,8BAAoB,EAAC,GAAG,CAAC,MAAM,EAAE,iBAAiB,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC;IACzE,CAAC;IAEM,MAAM,CAAC,gBAAgB,CAAC,GAA+B;QAC5D,OAAO,IAAA,8BAAoB,EAAC,GAAG,CAAC,MAAM,EAAE,6BAA6B,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC;IACrF,CAAC;IAEM,MAAM,CAAC,kBAAkB,CAAC,GAAiC;QAChE,OAAO,IAAA,8BAAoB,EAAC,GAAG,CAAC,MAAM,EAAE,iBAAiB,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC;IACzE,CAAC;IAEM,MAAM,CAAC,iBAAiB,CAAC,GAAgC;QAC9D,OAAO,IAAA,8BAAoB,EAAC,GAAG,CAAC,MAAM,EAAE,uBAAuB,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC;IAC/E,CAAC;IAEM,MAAM,CAAC,iBAAiB,CAAC,GAAgC;QAC9D,OAAO,IAAA,8BAAoB,EAAC,GAAG,CAAC,MAAM,EAAE,uBAAuB,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC;IAC/E,CAAC;IAEM,MAAM,CAAC,YAAY,CAAC,GAA2B;QACpD,OAAO,IAAA,8BAAoB,EAAC,GAAG,CAAC,MAAM,EAAE,iBAAiB,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC;IACzE,CAAC;IAEM,MAAM,CAAC,aAAa,CAAC,GAA4B;QACtD,OAAO,IAAA,8BAAoB,EAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IAC1C,CAAC;IAEM,MAAM,CAAC,YAAY,CAAC,GAA2B;QACpD,OAAO,IAAA,8BAAoB,EAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IAC1C,CAAC;IAEM,MAAM,CAAC,uBAAuB,CAAC,GAAsC;QAC1E,OAAO,IAAA,8BAAoB,EAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IAC1C,CAAC;IAEM,MAAM,CAAC,YAAY,CAAC,GAA2B;QACpD,OAAO,IAAA,8BAAoB,EAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IAC1C,CAAC;IAEM,MAAM,CAAC,eAAe,CAAC,GAA8B;QAC1D,MAAM,QAAQ,GAAG,EAAE,GAAG,EAAE,UAAU,EAAE,KAAK,EAAE,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;QAC5D,MAAM,KAAK,GAAG,QAAQ,CAAC,UAAU,CAAC,EAAE,IAAI,EAAE,CAAC,QAAQ,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC;QAC5E,OAAO,IAAA,8BAAoB,EAAC,WAAW,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC;IAC7D,CAAC;IAEM,MAAM,CAAC,QAAQ,CAAC,GAAuB;QAC5C,OAAO,IAAA,8BAAoB,EAAC,GAAG,CAAC,MAAM,EAAE,cAAc,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC;IACtE,CAAC;IAED,0CAA0C;IACnC,MAAM,CAAC,cAAc,CAAC,GAA6B;QACxD,OAAO,IAAA,8BAAoB,EAAC,GAAG,CAAC,MAAM,EAAE,oBAAoB,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC;IAC5E,CAAC;IAEM,MAAM,CAAC,gBAAgB,CAAC,GAA+B;QAC5D,OAAO,IAAA,8BAAoB,EAAC,GAAG,CAAC,MAAM,EAAE,sBAAsB,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC;IAC9E,CAAC;CACF;AAnED,wBAmEC"}
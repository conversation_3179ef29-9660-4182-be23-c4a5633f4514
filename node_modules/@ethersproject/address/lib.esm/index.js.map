{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src.ts/index.ts"], "names": [], "mappings": "AAAA,YAAY,CAAC;AAEb,OAAO,EAAE,QAAQ,EAAa,MAAM,EAAE,aAAa,EAAE,YAAY,EAAE,WAAW,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AACzH,OAAO,EAAE,SAAS,EAAgB,WAAW,EAAE,WAAW,EAAE,MAAM,0BAA0B,CAAC;AAC7F,OAAO,EAAE,SAAS,EAAE,MAAM,0BAA0B,CAAC;AACrD,OAAO,EAAE,MAAM,EAAE,MAAM,oBAAoB,CAAC;AAE5C,OAAO,EAAE,MAAM,EAAE,MAAM,uBAAuB,CAAC;AAC/C,OAAO,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;AACrC,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,OAAO,CAAC,CAAC;AAEnC,SAAS,kBAAkB,CAAC,OAAe;IACvC,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,EAAE,CAAC,EAAE;QAC3B,MAAM,CAAC,kBAAkB,CAAC,iBAAiB,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;KACpE;IAED,OAAO,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;IAEhC,MAAM,KAAK,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;IAE7C,MAAM,QAAQ,GAAG,IAAI,UAAU,CAAC,EAAE,CAAC,CAAC;IACpC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;QACzB,QAAQ,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;KACxC;IAED,MAAM,MAAM,GAAG,QAAQ,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC;IAE7C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,IAAI,CAAC,EAAE;QAC5B,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,EAAE;YAC5B,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;SACrC;QACD,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE;YAC9B,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;SAC7C;KACJ;IAED,OAAO,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACjC,CAAC;AAED,gFAAgF;AAChF,MAAM,gBAAgB,GAAW,gBAAgB,CAAC;AAElD,SAAS,KAAK,CAAC,CAAS;IACpB,IAAI,IAAI,CAAC,KAAK,EAAE;QAAE,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;KAAE;IACzC,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC;AACnC,CAAC;AAGD,uEAAuE;AAEvE,sBAAsB;AACtB,MAAM,UAAU,GAAoC,EAAG,CAAC;AACxD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;IAAE,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;CAAE;AACnE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;IAAE,UAAU,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;CAAE;AAE1F,yEAAyE;AACzE,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC,CAAC;AAEvD,SAAS,YAAY,CAAC,OAAe;IACjC,OAAO,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;IAChC,OAAO,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC;IAEhE,IAAI,QAAQ,GAAG,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,GAAG,OAAO,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAEhF,kEAAkE;IAClE,OAAO,QAAQ,CAAC,MAAM,IAAI,UAAU,EAAC;QACjC,IAAI,KAAK,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;QAC9C,QAAQ,GAAG,QAAQ,CAAC,KAAK,EAAE,EAAE,CAAC,GAAG,EAAE,GAAG,QAAQ,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;KAC1E;IAED,IAAI,QAAQ,GAAG,MAAM,CAAC,EAAE,GAAG,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;IAC1D,OAAO,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;QAAE,QAAQ,GAAG,GAAG,GAAG,QAAQ,CAAC;KAAE;IAE1D,OAAO,QAAQ,CAAC;AACpB,CAAC;AAAA,CAAC;AAEF,MAAM,UAAU,UAAU,CAAC,OAAe;IACtC,IAAI,MAAM,GAAG,IAAI,CAAC;IAElB,IAAI,OAAM,CAAC,OAAO,CAAC,KAAK,QAAQ,EAAE;QAC9B,MAAM,CAAC,kBAAkB,CAAC,iBAAiB,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;KACpE;IAED,IAAI,OAAO,CAAC,KAAK,CAAC,wBAAwB,CAAC,EAAE;QAEzC,wBAAwB;QACxB,IAAI,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,IAAI,EAAE;YAAE,OAAO,GAAG,IAAI,GAAG,OAAO,CAAC;SAAE;QAEnE,MAAM,GAAG,kBAAkB,CAAC,OAAO,CAAC,CAAC;QAErC,kDAAkD;QAClD,IAAI,OAAO,CAAC,KAAK,CAAC,+BAA+B,CAAC,IAAI,MAAM,KAAK,OAAO,EAAE;YACtE,MAAM,CAAC,kBAAkB,CAAC,sBAAsB,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;SACzE;QAEL,4CAA4C;KAC3C;SAAM,IAAI,OAAO,CAAC,KAAK,CAAC,gCAAgC,CAAC,EAAE;QAExD,4CAA4C;QAC5C,IAAI,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,YAAY,CAAC,OAAO,CAAC,EAAE;YACnD,MAAM,CAAC,kBAAkB,CAAC,mBAAmB,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;SACtE;QAED,MAAM,GAAG,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3C,OAAO,MAAM,CAAC,MAAM,GAAG,EAAE,EAAE;YAAE,MAAM,GAAG,GAAG,GAAG,MAAM,CAAC;SAAE;QACrD,MAAM,GAAG,kBAAkB,CAAC,IAAI,GAAG,MAAM,CAAC,CAAC;KAE9C;SAAM;QACH,MAAM,CAAC,kBAAkB,CAAC,iBAAiB,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;KACpE;IAED,OAAO,MAAM,CAAC;AAClB,CAAC;AAED,MAAM,UAAU,SAAS,CAAC,OAAe;IACrC,IAAI;QACA,UAAU,CAAC,OAAO,CAAC,CAAC;QACpB,OAAO,IAAI,CAAC;KACf;IAAC,OAAO,KAAK,EAAE,GAAG;IACnB,OAAO,KAAK,CAAC;AACjB,CAAC;AAED,MAAM,UAAU,cAAc,CAAC,OAAe;IAC1C,IAAI,MAAM,GAAG,WAAW,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;IACzE,OAAO,MAAM,CAAC,MAAM,GAAG,EAAE,EAAE;QAAE,MAAM,GAAG,GAAG,GAAG,MAAM,CAAC;KAAE;IACrD,OAAO,IAAI,GAAG,YAAY,CAAC,MAAM,GAAG,MAAM,CAAC,GAAG,MAAM,CAAC;AACzD,CAAC;AAED,sGAAsG;AACtG,MAAM,UAAU,kBAAkB,CAAC,WAAkD;IACjF,IAAI,IAAI,GAAW,IAAI,CAAC;IACxB,IAAI;QACA,IAAI,GAAG,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;KACvC;IAAC,OAAO,KAAK,EAAE;QACZ,MAAM,CAAC,kBAAkB,CAAC,sBAAsB,EAAE,aAAa,EAAE,WAAW,CAAC,CAAC;KACjF;IAED,MAAM,KAAK,GAAG,UAAU,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;IAEpF,OAAO,UAAU,CAAC,YAAY,CAAC,SAAS,CAAC,MAAM,CAAC,CAAE,IAAI,EAAE,KAAK,CAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;AAC5E,CAAC;AAED,MAAM,UAAU,iBAAiB,CAAC,IAAY,EAAE,IAAe,EAAE,YAAuB;IACpF,IAAI,aAAa,CAAC,IAAI,CAAC,KAAK,EAAE,EAAE;QAC5B,MAAM,CAAC,kBAAkB,CAAC,uBAAuB,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;KACpE;IACD,IAAI,aAAa,CAAC,YAAY,CAAC,KAAK,EAAE,EAAE;QACpC,MAAM,CAAC,kBAAkB,CAAC,+BAA+B,EAAE,cAAc,EAAE,YAAY,CAAC,CAAC;KAC5F;IACD,OAAO,UAAU,CAAC,YAAY,CAAC,SAAS,CAAC,MAAM,CAAC,CAAE,MAAM,EAAE,UAAU,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,YAAY,CAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAA;AAC5G,CAAC"}
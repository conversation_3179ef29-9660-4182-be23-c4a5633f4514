{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src.ts/index.ts"], "names": [], "mappings": "AAAA,YAAY,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKb,wDAAuG;AAEvG,gDAA+C;AAC/C,uCAAqC;AACrC,IAAM,MAAM,GAAG,IAAI,eAAM,CAAC,kBAAO,CAAC,CAAC;AAEnC,IAAM,sBAAsB,GAAkB;IAC1C,YAAY,EAAE,iBAAiB,EAAE,SAAS,EAAE,YAAY,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,cAAc,EAAE,sBAAsB,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO;CAC3K,CAAC;AAEF,IAAM,aAAa,GAAG;IAClB,eAAM,CAAC,MAAM,CAAC,kBAAkB;IAChC,eAAM,CAAC,MAAM,CAAC,aAAa;IAC3B,eAAM,CAAC,MAAM,CAAC,uBAAuB;CACxC,CAAC;AAWD,CAAC;AAKD,CAAC;AAsBF;IA4BI,mBAAmB;IACnB,8BAA8B;IAC9B;;QACI,MAAM,CAAC,aAAa,aAAa,MAAM,CAAC,CAAC;QACzC,IAAA,2BAAc,EAAC,IAAI,EAAE,WAAW,EAAE,IAAI,CAAC,CAAC;IAC5C,CAAC;IAGD,mBAAmB;IACnB,iCAAiC;IAE3B,2BAAU,GAAhB,UAAiB,QAAmB;;;;;wBAChC,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;wBAC3B,qBAAM,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE,QAAQ,CAAC,EAAA;4BAAlE,sBAAO,SAA2D,EAAC;;;;KACtE;IAEK,oCAAmB,GAAzB,UAA0B,QAAmB;;;;;wBACzC,IAAI,CAAC,cAAc,CAAC,qBAAqB,CAAC,CAAC;wBACpC,qBAAM,IAAI,CAAC,QAAQ,CAAC,mBAAmB,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE,QAAQ,CAAC,EAAA;4BAA3E,sBAAO,SAAoE,EAAC;;;;KAC/E;IAED,6EAA6E;IACvE,4BAAW,GAAjB,UAAkB,WAA2C;;;;;;wBACzD,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;wBACxB,qBAAM,IAAA,8BAAiB,EAAC,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC,EAAA;;wBAAhE,EAAE,GAAG,SAA2D;wBAC/D,qBAAM,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC,EAAA;4BAA1C,sBAAO,SAAmC,EAAC;;;;KAC9C;IAED,kEAAkE;IAC5D,qBAAI,GAAV,UAAW,WAA2C,EAAE,QAAmB;;;;;;wBACvE,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;wBACjB,qBAAM,IAAA,8BAAiB,EAAC,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC,EAAA;;wBAAhE,EAAE,GAAG,SAA2D;wBAC/D,qBAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,EAAE,QAAQ,CAAC,EAAA;4BAA7C,sBAAO,SAAsC,EAAC;;;;KACjD;IAED,8EAA8E;IACxE,gCAAe,GAArB,UAAsB,WAA2C;;;;;;wBAC7D,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC,CAAC;wBAC5B,qBAAM,IAAI,CAAC,mBAAmB,CAAC,WAAW,CAAC,EAAA;;wBAAhD,EAAE,GAAG,SAA2C;wBACrC,qBAAM,IAAI,CAAC,eAAe,CAAC,EAAE,CAAC,EAAA;;wBAAzC,QAAQ,GAAG,SAA8B;wBACxC,qBAAM,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,QAAQ,CAAC,EAAA;4BAApD,sBAAO,SAA6C,EAAC;;;;KACxD;IAEK,2BAAU,GAAhB;;;;;;wBACI,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;wBAClB,qBAAM,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,EAAA;;wBAA1C,OAAO,GAAG,SAAgC;wBAChD,sBAAO,OAAO,CAAC,OAAO,EAAC;;;;KAC1B;IAEK,4BAAW,GAAjB;;;;;wBACI,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;wBAC5B,qBAAM,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,EAAA;4BAAxC,sBAAO,SAAiC,EAAC;;;;KAC5C;IAEK,2BAAU,GAAhB;;;;;wBACI,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;wBAC3B,qBAAM,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,EAAA;4BAAvC,sBAAO,SAAgC,EAAC;;;;KAC3C;IAGK,4BAAW,GAAjB,UAAkB,IAAY;;;;;wBAC1B,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;wBAC5B,qBAAM,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,EAAA;4BAA5C,sBAAO,SAAqC,EAAC;;;;KAChD;IAID,4DAA4D;IAC5D,uCAAuC;IACvC,gCAAgC;IAChC,+BAA+B;IAC/B,+CAA+C;IAC/C,yDAAyD;IACzD,WAAW;IACX,kBAAkB;IAClB,yDAAyD;IACzD,iCAAgB,GAAhB,UAAiB,WAA2C;QACxD,KAAK,IAAM,GAAG,IAAI,WAAW,EAAE;YAC3B,IAAI,sBAAsB,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;gBAC5C,MAAM,CAAC,kBAAkB,CAAC,2BAA2B,GAAG,GAAG,EAAE,aAAa,EAAE,WAAW,CAAC,CAAC;aAC5F;SACJ;QAED,IAAM,EAAE,GAAG,IAAA,wBAAW,EAAC,WAAW,CAAC,CAAC;QAEpC,IAAI,EAAE,CAAC,IAAI,IAAI,IAAI,EAAE;YACjB,EAAE,CAAC,IAAI,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;SAE/B;aAAM;YACH,qDAAqD;YACrD,EAAE,CAAC,IAAI,GAAG,OAAO,CAAC,GAAG,CAAC;gBAClB,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,IAAI,CAAC;gBACxB,IAAI,CAAC,UAAU,EAAE;aACpB,CAAC,CAAC,IAAI,CAAC,UAAC,MAAM;gBACX,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,KAAK,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,EAAE;oBACrD,MAAM,CAAC,kBAAkB,CAAC,uBAAuB,EAAE,aAAa,EAAE,WAAW,CAAC,CAAC;iBAClF;gBACD,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC;YACrB,CAAC,CAAC,CAAC;SACN;QAED,OAAO,EAAE,CAAC;IACd,CAAC;IAED,sEAAsE;IACtE,6EAA6E;IAC7E,yDAAyD;IACzD,sBAAsB;IACtB,EAAE;IACF,SAAS;IACT,uEAAuE;IACjE,oCAAmB,GAAzB,UAA0B,WAA2C;;;;;;4BAEtB,qBAAM,IAAA,8BAAiB,EAAC,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC,EAAA;;wBAAhG,EAAE,GAAmC,SAA2D;wBAEtG,IAAI,EAAE,CAAC,EAAE,IAAI,IAAI,EAAE;4BACf,EAAE,CAAC,EAAE,GAAG,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,UAAO,EAAE;;;;;4CACzC,IAAI,EAAE,IAAI,IAAI,EAAE;gDAAE,sBAAO,IAAI,EAAC;6CAAE;4CAChB,qBAAM,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,EAAA;;4CAApC,OAAO,GAAG,SAA0B;4CAC1C,IAAI,OAAO,IAAI,IAAI,EAAE;gDACjB,MAAM,CAAC,kBAAkB,CAAC,oCAAoC,EAAE,OAAO,EAAE,EAAE,CAAC,CAAC;6CAChF;4CACD,sBAAO,OAAO,EAAC;;;iCAClB,CAAC,CAAC;4BAEH,+DAA+D;4BAC/D,EAAE,CAAC,EAAE,CAAC,KAAK,CAAC,UAAC,KAAK,IAAQ,CAAC,CAAC,CAAC;yBAChC;wBAGK,UAAU,GAAG,CAAC,EAAE,CAAC,YAAY,IAAI,IAAI,IAAI,EAAE,CAAC,oBAAoB,IAAI,IAAI,CAAC,CAAC;wBAChF,IAAI,EAAE,CAAC,QAAQ,IAAI,IAAI,IAAI,CAAC,EAAE,CAAC,IAAI,KAAK,CAAC,IAAI,UAAU,CAAC,EAAE;4BACtD,MAAM,CAAC,kBAAkB,CAAC,8CAA8C,EAAE,aAAa,EAAE,WAAW,CAAC,CAAC;yBACzG;6BAAM,IAAI,CAAC,EAAE,CAAC,IAAI,KAAK,CAAC,IAAI,EAAE,CAAC,IAAI,KAAK,CAAC,CAAC,IAAI,UAAU,EAAE;4BACvD,MAAM,CAAC,kBAAkB,CAAC,2EAA2E,EAAE,aAAa,EAAE,WAAW,CAAC,CAAC;yBACtI;6BAEG,CAAA,CAAC,EAAE,CAAC,IAAI,KAAK,CAAC,IAAI,EAAE,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,YAAY,IAAI,IAAI,IAAI,EAAE,CAAC,oBAAoB,IAAI,IAAI,CAAC,CAAA,EAAlG,wBAAkG;wBAClG,sDAAsD;wBACtD,EAAE,CAAC,IAAI,GAAG,CAAC,CAAC;;;6BAEL,CAAA,EAAE,CAAC,IAAI,KAAK,CAAC,IAAI,EAAE,CAAC,IAAI,KAAK,CAAC,CAAA,EAA9B,wBAA8B;wBACrC,0CAA0C;wBAE1C,4BAA4B;wBAC5B,IAAI,EAAE,CAAC,QAAQ,IAAI,IAAI,EAAE;4BAAE,EAAE,CAAC,QAAQ,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;yBAAE;;4BAK9C,qBAAM,IAAI,CAAC,UAAU,EAAE,EAAA;;wBAAjC,OAAO,GAAG,SAAuB;wBAEvC,IAAI,EAAE,CAAC,IAAI,IAAI,IAAI,EAAE;4BACjB,kEAAkE;4BAElE,IAAI,OAAO,CAAC,YAAY,IAAI,IAAI,IAAI,OAAO,CAAC,oBAAoB,IAAI,IAAI,EAAE;gCACtE,iCAAiC;gCAEjC,4CAA4C;gCAC5C,EAAE,CAAC,IAAI,GAAG,CAAC,CAAC;gCAEZ,IAAI,EAAE,CAAC,QAAQ,IAAI,IAAI,EAAE;oCAGf,QAAQ,GAAG,EAAE,CAAC,QAAQ,CAAC;oCAC7B,OAAO,EAAE,CAAC,QAAQ,CAAC;oCACnB,EAAE,CAAC,YAAY,GAAG,QAAQ,CAAC;oCAC3B,EAAE,CAAC,oBAAoB,GAAG,QAAQ,CAAC;iCAEtC;qCAAM;oCACH,4BAA4B;oCAC5B,IAAI,EAAE,CAAC,YAAY,IAAI,IAAI,EAAE;wCAAE,EAAE,CAAC,YAAY,GAAG,OAAO,CAAC,YAAY,CAAC;qCAAE;oCACxE,IAAI,EAAE,CAAC,oBAAoB,IAAI,IAAI,EAAE;wCAAE,EAAE,CAAC,oBAAoB,GAAG,OAAO,CAAC,oBAAoB,CAAC;qCAAE;iCACnG;6BAEJ;iCAAM,IAAI,OAAO,CAAC,QAAQ,IAAI,IAAI,EAAE;gCACjC,sCAAsC;gCAEtC,oDAAoD;gCACpD,IAAI,UAAU,EAAE;oCACZ,MAAM,CAAC,UAAU,CAAC,mCAAmC,EAAE,eAAM,CAAC,MAAM,CAAC,qBAAqB,EAAE;wCACxF,SAAS,EAAE,qBAAqB;qCACnC,CAAC,CAAC;iCACN;gCAED,4BAA4B;gCAC5B,IAAI,EAAE,CAAC,QAAQ,IAAI,IAAI,EAAE;oCAAE,EAAE,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;iCAAE;gCAE5D,+CAA+C;gCAC/C,EAAE,CAAC,IAAI,GAAG,CAAC,CAAC;6BAEf;iCAAM;gCACH,4BAA4B;gCAC5B,MAAM,CAAC,UAAU,CAAC,mCAAmC,EAAE,eAAM,CAAC,MAAM,CAAC,qBAAqB,EAAE;oCACxF,SAAS,EAAE,mBAAmB;iCACjC,CAAC,CAAC;6BACN;yBAEJ;6BAAM,IAAI,EAAE,CAAC,IAAI,KAAK,CAAC,EAAE;4BACtB,4BAA4B;4BAE5B,4BAA4B;4BAC5B,IAAI,EAAE,CAAC,YAAY,IAAI,IAAI,EAAE;gCAAE,EAAE,CAAC,YAAY,GAAG,OAAO,CAAC,YAAY,CAAC;6BAAE;4BACxE,IAAI,EAAE,CAAC,oBAAoB,IAAI,IAAI,EAAE;gCAAE,EAAE,CAAC,oBAAoB,GAAG,OAAO,CAAC,oBAAoB,CAAC;6BAAE;yBACnG;;;wBAGL,IAAI,EAAE,CAAC,KAAK,IAAI,IAAI,EAAE;4BAAE,EAAE,CAAC,KAAK,GAAG,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAC;yBAAE;wBAEzE,IAAI,EAAE,CAAC,QAAQ,IAAI,IAAI,EAAE;4BACrB,EAAE,CAAC,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,UAAC,KAAK;gCAC3C,IAAI,aAAa,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;oCACxC,MAAM,KAAK,CAAC;iCACf;gCAED,OAAO,MAAM,CAAC,UAAU,CAAC,2EAA2E,EAAE,eAAM,CAAC,MAAM,CAAC,uBAAuB,EAAE;oCACzI,KAAK,EAAE,KAAK;oCACZ,EAAE,EAAE,EAAE;iCACT,CAAC,CAAC;4BACP,CAAC,CAAC,CAAC;yBACN;wBAED,IAAI,EAAE,CAAC,OAAO,IAAI,IAAI,EAAE;4BACpB,EAAE,CAAC,OAAO,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;yBAClC;6BAAM;4BACH,EAAE,CAAC,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC;gCACrB,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC;gCAC3B,IAAI,CAAC,UAAU,EAAE;6BACpB,CAAC,CAAC,IAAI,CAAC,UAAC,OAAO;gCACZ,IAAI,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,OAAO,CAAC,CAAC,CAAC,KAAK,OAAO,CAAC,CAAC,CAAC,EAAE;oCAC/C,MAAM,CAAC,kBAAkB,CAAC,0BAA0B,EAAE,aAAa,EAAE,WAAW,CAAC,CAAC;iCACrF;gCACD,OAAO,OAAO,CAAC,CAAC,CAAC,CAAC;4BACtB,CAAC,CAAC,CAAC;yBACN;wBAEM,qBAAM,IAAA,8BAAiB,EAAC,EAAE,CAAC,EAAA;4BAAlC,sBAAO,SAA2B,EAAC;;;;KACtC;IAGD,mBAAmB;IACnB,uCAAuC;IAEvC,+BAAc,GAAd,UAAe,SAAkB;QAC7B,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAAE,MAAM,CAAC,UAAU,CAAC,kBAAkB,EAAE,eAAM,CAAC,MAAM,CAAC,qBAAqB,EAAE;gBAC7F,SAAS,EAAE,CAAC,SAAS,IAAI,gBAAgB,CAAC;aAAE,CAAC,CAAC;SACjD;IACL,CAAC;IAEM,eAAQ,GAAf,UAAgB,KAAU;QACtB,OAAO,CAAC,CAAC,CAAC,KAAK,IAAI,KAAK,CAAC,SAAS,CAAC,CAAC;IACxC,CAAC;IACL,aAAC;AAAD,CAAC,AAxRD,IAwRC;AAxRqB,wBAAM;AA0R5B;IAAgC,8BAAM;IAGlC,oBAAY,OAAe,EAAE,QAAmB;QAAhD,YACI,iBAAO,SAGV;QAFG,IAAA,2BAAc,EAAC,KAAI,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;QACzC,IAAA,2BAAc,EAAC,KAAI,EAAE,UAAU,EAAE,QAAQ,IAAI,IAAI,CAAC,CAAC;;IACvD,CAAC;IAED,+BAAU,GAAV;QACI,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACzC,CAAC;IAED,0BAAK,GAAL,UAAM,OAAe,EAAE,SAAiB;QACpC,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC;YAC1B,MAAM,CAAC,UAAU,CAAC,OAAO,EAAE,eAAM,CAAC,MAAM,CAAC,qBAAqB,EAAE,EAAE,SAAS,EAAE,SAAS,EAAE,CAAC,CAAC;QAC9F,CAAC,CAAC,CAAC;IACP,CAAC;IAED,gCAAW,GAAX,UAAY,OAAuB;QAC/B,OAAO,IAAI,CAAC,KAAK,CAAC,iCAAiC,EAAE,aAAa,CAAC,CAAC;IACxE,CAAC;IAED,oCAAe,GAAf,UAAgB,WAA2C;QACvD,OAAO,IAAI,CAAC,KAAK,CAAC,qCAAqC,EAAE,iBAAiB,CAAC,CAAC;IAChF,CAAC;IAED,mCAAc,GAAd,UAAe,MAAuB,EAAE,KAA4C,EAAE,KAA0B;QAC5G,OAAO,IAAI,CAAC,KAAK,CAAC,mCAAmC,EAAE,eAAe,CAAC,CAAC;IAC5E,CAAC;IAED,4BAAO,GAAP,UAAQ,QAAkB;QACtB,OAAO,IAAI,UAAU,CAAC,IAAI,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;IAClD,CAAC;IACL,iBAAC;AAAD,CAAC,AAlCD,CAAgC,MAAM,GAkCrC;AAlCY,gCAAU"}
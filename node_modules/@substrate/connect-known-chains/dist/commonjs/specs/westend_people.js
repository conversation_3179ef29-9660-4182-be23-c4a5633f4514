"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.chainSpec = void 0;
exports.chainSpec = `{
  "name": "Westend People",
  "id": "people-westend",
  "chainType": "Live",
  "bootNodes": [
    "/dns/westend-people-collator-node-0.parity-testnet.parity.io/tcp/30333/p2p/12D3KooWDcLjDLTu9fNhmas9DTWtqdv8eUbFMWQzVwvXRK7QcjHD",
    "/dns/westend-people-collator-node-0.parity-testnet.parity.io/tcp/443/wss/p2p/12D3KooWDcLjDLTu9fNhmas9DTWtqdv8eUbFMWQzVwvXRK7QcjHD",
    "/dns/westend-people-collator-node-1.parity-testnet.parity.io/tcp/30333/p2p/12D3KooWM56JbKWAXsDyWh313z73aKYVMp1Hj2nSnAKY3q6MnoC9",
    "/dns/westend-people-collator-node-1.parity-testnet.parity.io/tcp/443/wss/p2p/12D3KooWM56JbKWAXsDyWh313z73aKYVMp1Hj2nSnAKY3q6MnoC9",
    "/dns/westend-people-collator-node-2.parity-testnet.parity.io/tcp/30333/p2p/12D3KooWGVYTVKW7tYe51JvetvGvVLDPXzqQX1mueJgz14FgkmHG",
    "/dns/westend-people-collator-node-2.parity-testnet.parity.io/tcp/443/wss/p2p/12D3KooWGVYTVKW7tYe51JvetvGvVLDPXzqQX1mueJgz14FgkmHG",
    "/dns/westend-people-collator-node-3.parity-testnet.parity.io/tcp/30333/p2p/12D3KooWCF1eA2Gap69zgXD7Df3e9DqDUsGoByocggTGejoHjK23",
    "/dns/westend-people-collator-node-3.parity-testnet.parity.io/tcp/443/wss/p2p/12D3KooWCF1eA2Gap69zgXD7Df3e9DqDUsGoByocggTGejoHjK23"
  ],
  "telemetryEndpoints": null,
  "protocolId": null,
  "properties": {
    "tokenDecimals": 12,
    "tokenSymbol": "WND"
  },
  "relay_chain": "westend2",
  "para_id": 1004,
  "codeSubstitutes": {},
  "genesis": {
    "stateRootHash": "0x6f2f13b897fd27eeaad3a36faf12ea604c781533e5f2398a0b48bdc1a6eb78b7"
  }
}
`;
//# sourceMappingURL=westend_people.js.map
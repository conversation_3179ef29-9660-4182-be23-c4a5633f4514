"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.chainSpec = void 0;
exports.chainSpec = `{
  "name": "Polkadot People",
  "id": "people-polkadot",
  "chainType": "Live",
  "bootNodes": [
    "/dns/polkadot-people-connect-0.polkadot.io/tcp/30334/p2p/12D3KooWP7BoJ7nAF9QnsreN8Eft1yHNUhvhxFiQyKFEUePi9mu3",
    "/dns/polkadot-people-connect-1.polkadot.io/tcp/30334/p2p/12D3KooWSSfWY3fTGJvGkuNUNBSNVCdLLNJnwkZSNQt7GCRYXu4o",
    "/dns/polkadot-people-connect-0.polkadot.io/tcp/443/wss/p2p/12D3KooWP7BoJ7nAF9QnsreN8Eft1yHNUhvhxFiQyKFEUePi9mu3",
    "/dns/polkadot-people-connect-1.polkadot.io/tcp/443/wss/p2p/12D3KooWSSfWY3fTGJvGkuNUNBSNVCdLLNJnwkZSNQt7GCRYXu4o"
  ],
  "telemetryEndpoints": null,
  "protocolId": null,
  "properties": {
    "ss58Format": 0,
    "tokenDecimals": 10,
    "tokenSymbol": "DOT"
  },
  "relay_chain": "polkadot",
  "para_id": 1004,
  "codeSubstitutes": {},
  "genesis": {
    "stateRootHash": "0x7a62a14ebf9b2e86292593414d58818324acc5701ea369734c0074f7c962bc0f"
  }
}
`;
//# sourceMappingURL=polkadot_people.js.map
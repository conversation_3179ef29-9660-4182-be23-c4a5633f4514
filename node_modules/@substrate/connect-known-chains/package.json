{"name": "@substrate/connect-known-chains", "version": "1.4.0", "description": "Substrate-connect well known chain specifications", "author": "Parity Team <<EMAIL>>", "license": "GPL-3.0-only", "type": "module", "main": "./dist/commonjs/index.js", "module": "./dist/esm/index.js", "types": "./dist/commonjs/index.d.ts", "exports": {"./package.json": "./package.json", ".": {"import": {"@substrate-connect/source": "./src/index.ts", "types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}, "./polkadot": {"import": {"@substrate-connect/source": "./src/specs/polkadot.ts", "types": "./dist/esm/specs/polkadot.d.ts", "default": "./dist/esm/specs/polkadot.js"}, "require": {"types": "./dist/commonjs/specs/polkadot.d.ts", "default": "./dist/commonjs/specs/polkadot.js"}}, "./ksmcc3": {"import": {"@substrate-connect/source": "./src/specs/ksmcc3.ts", "types": "./dist/esm/specs/ksmcc3.d.ts", "default": "./dist/esm/specs/ksmcc3.js"}, "require": {"types": "./dist/commonjs/specs/ksmcc3.d.ts", "default": "./dist/commonjs/specs/ksmcc3.js"}}, "./westend2": {"import": {"@substrate-connect/source": "./src/specs/westend2.ts", "types": "./dist/esm/specs/westend2.d.ts", "default": "./dist/esm/specs/westend2.js"}, "require": {"types": "./dist/commonjs/specs/westend2.d.ts", "default": "./dist/commonjs/specs/westend2.js"}}, "./rococo_v2_2": {"import": {"@substrate-connect/source": "./src/specs/rococo_v2_2.ts", "types": "./dist/esm/specs/rococo_v2_2.d.ts", "default": "./dist/esm/specs/rococo_v2_2.js"}, "require": {"types": "./dist/commonjs/specs/rococo_v2_2.d.ts", "default": "./dist/commonjs/specs/rococo_v2_2.js"}}, "./ksmcc3_asset_hub": {"import": {"@substrate-connect/source": "./src/specs/ksmcc3_asset_hub.ts", "types": "./dist/esm/specs/ksmcc3_asset_hub.d.ts", "default": "./dist/esm/specs/ksmcc3_asset_hub.js"}, "require": {"types": "./dist/commonjs/specs/ksmcc3_asset_hub.d.ts", "default": "./dist/commonjs/specs/ksmcc3_asset_hub.js"}}, "./ksmcc3_bridge_hub": {"import": {"@substrate-connect/source": "./src/specs/ksmcc3_bridge_hub.ts", "types": "./dist/esm/specs/ksmcc3_bridge_hub.d.ts", "default": "./dist/esm/specs/ksmcc3_bridge_hub.js"}, "require": {"types": "./dist/commonjs/specs/ksmcc3_bridge_hub.d.ts", "default": "./dist/commonjs/specs/ksmcc3_bridge_hub.js"}}, "./polkadot_asset_hub": {"import": {"@substrate-connect/source": "./src/specs/polkadot_asset_hub.ts", "types": "./dist/esm/specs/polkadot_asset_hub.d.ts", "default": "./dist/esm/specs/polkadot_asset_hub.js"}, "require": {"types": "./dist/commonjs/specs/polkadot_asset_hub.d.ts", "default": "./dist/commonjs/specs/polkadot_asset_hub.js"}}, "./polkadot_bridge_hub": {"import": {"@substrate-connect/source": "./src/specs/polkadot_bridge_hub.ts", "types": "./dist/esm/specs/polkadot_bridge_hub.d.ts", "default": "./dist/esm/specs/polkadot_bridge_hub.js"}, "require": {"types": "./dist/commonjs/specs/polkadot_bridge_hub.d.ts", "default": "./dist/commonjs/specs/polkadot_bridge_hub.js"}}, "./polkadot_collectives": {"import": {"@substrate-connect/source": "./src/specs/polkadot_collectives.ts", "types": "./dist/esm/specs/polkadot_collectives.d.ts", "default": "./dist/esm/specs/polkadot_collectives.js"}, "require": {"types": "./dist/commonjs/specs/polkadot_collectives.d.ts", "default": "./dist/commonjs/specs/polkadot_collectives.js"}}, "./rococo_v2_2_asset_hub": {"import": {"@substrate-connect/source": "./src/specs/rococo_v2_2_asset_hub.ts", "types": "./dist/esm/specs/rococo_v2_2_asset_hub.d.ts", "default": "./dist/esm/specs/rococo_v2_2_asset_hub.js"}, "require": {"types": "./dist/commonjs/specs/rococo_v2_2_asset_hub.d.ts", "default": "./dist/commonjs/specs/rococo_v2_2_asset_hub.js"}}, "./rococo_v2_2_bridge_hub": {"import": {"@substrate-connect/source": "./src/specs/rococo_v2_2_bridge_hub.ts", "types": "./dist/esm/specs/rococo_v2_2_bridge_hub.d.ts", "default": "./dist/esm/specs/rococo_v2_2_bridge_hub.js"}, "require": {"types": "./dist/commonjs/specs/rococo_v2_2_bridge_hub.d.ts", "default": "./dist/commonjs/specs/rococo_v2_2_bridge_hub.js"}}, "./westend2_asset_hub": {"import": {"@substrate-connect/source": "./src/specs/westend2_asset_hub.ts", "types": "./dist/esm/specs/westend2_asset_hub.d.ts", "default": "./dist/esm/specs/westend2_asset_hub.js"}, "require": {"types": "./dist/commonjs/specs/westend2_asset_hub.d.ts", "default": "./dist/commonjs/specs/westend2_asset_hub.js"}}, "./westend2_bridge_hub": {"import": {"@substrate-connect/source": "./src/specs/westend2_bridge_hub.ts", "types": "./dist/esm/specs/westend2_bridge_hub.d.ts", "default": "./dist/esm/specs/westend2_bridge_hub.js"}, "require": {"types": "./dist/commonjs/specs/westend2_bridge_hub.d.ts", "default": "./dist/commonjs/specs/westend2_bridge_hub.js"}}, "./westend2_collectives": {"import": {"@substrate-connect/source": "./src/specs/westend2_collectives.ts", "types": "./dist/esm/specs/westend2_collectives.d.ts", "default": "./dist/esm/specs/westend2_collectives.js"}, "require": {"types": "./dist/commonjs/specs/westend2_collectives.d.ts", "default": "./dist/commonjs/specs/westend2_collectives.js"}}, "./paseo": {"import": {"@substrate-connect/source": "./src/specs/paseo.ts", "types": "./dist/esm/specs/paseo.d.ts", "default": "./dist/esm/specs/paseo.js"}, "require": {"types": "./dist/commonjs/specs/paseo.d.ts", "default": "./dist/commonjs/specs/paseo.js"}}, "./polkadot_people": {"import": {"@substrate-connect/source": "./src/specs/polkadot_people.ts", "types": "./dist/esm/specs/polkadot_people.d.ts", "default": "./dist/esm/specs/polkadot_people.js"}, "require": {"types": "./dist/commonjs/specs/polkadot_people.d.ts", "default": "./dist/commonjs/specs/polkadot_people.js"}}, "./westend_people": {"import": {"@substrate-connect/source": "./src/specs/westend_people.ts", "types": "./dist/esm/specs/westend_people.d.ts", "default": "./dist/esm/specs/westend_people.js"}, "require": {"types": "./dist/commonjs/specs/westend_people.d.ts", "default": "./dist/commonjs/specs/westend_people.js"}}}, "files": ["dist"], "tshy": {"project": "./tsconfig.build.json", "exports": {"./package.json": "./package.json", ".": "./src/index.ts", "./polkadot": "./src/specs/polkadot.ts", "./ksmcc3": "./src/specs/ksmcc3.ts", "./westend2": "./src/specs/westend2.ts", "./rococo_v2_2": "./src/specs/rococo_v2_2.ts", "./ksmcc3_asset_hub": "./src/specs/ksmcc3_asset_hub.ts", "./ksmcc3_bridge_hub": "./src/specs/ksmcc3_bridge_hub.ts", "./polkadot_asset_hub": "./src/specs/polkadot_asset_hub.ts", "./polkadot_bridge_hub": "./src/specs/polkadot_bridge_hub.ts", "./polkadot_collectives": "./src/specs/polkadot_collectives.ts", "./rococo_v2_2_asset_hub": "./src/specs/rococo_v2_2_asset_hub.ts", "./rococo_v2_2_bridge_hub": "./src/specs/rococo_v2_2_bridge_hub.ts", "./westend2_asset_hub": "./src/specs/westend2_asset_hub.ts", "./westend2_bridge_hub": "./src/specs/westend2_bridge_hub.ts", "./westend2_collectives": "./src/specs/westend2_collectives.ts", "./paseo": "./src/specs/paseo.ts", "./polkadot_people": "./src/specs/polkadot_people.ts", "./westend_people": "./src/specs/westend_people.ts"}, "sourceDialects": ["@substrate-connect/source"]}, "repository": {"type": "git", "url": "git+https://github.com/paritytech/substrate-connect.git"}, "bugs": {"url": "https://github.com/paritytech/substrate-connect/issues"}, "homepage": "https://github.com/paritytech/substrate-connect#readme", "devDependencies": {"vitest": "^2.0.5"}, "scripts": {"build-js-specs": "node scripts/build-js-specs.js", "deep-clean": "rimraf dist node_modules", "clean": "<PERSON><PERSON><PERSON> dist", "build": "pnpm build-js-specs && tsc --noEmit && tshy", "lint": "eslint . --ext .js,.ts"}}
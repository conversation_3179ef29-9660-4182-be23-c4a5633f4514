{"name": "@substrate/connect-extension-protocol", "version": "2.1.0", "description": "Protocol for connect message passing with the extension", "author": "Parity Team <<EMAIL>>", "license": "GPL-3.0-only", "type": "module", "main": "./dist/commonjs/index.js", "types": "./dist/commonjs/index.d.ts", "module": "./dist/esm/index.js", "exports": {"./package.json": "./package.json", ".": {"import": {"@substrate-connect/source": "./src/index.ts", "types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}}, "files": ["dist"], "tshy": {"project": "./tsconfig.build.json", "exports": {"./package.json": "./package.json", ".": "./src/index.ts"}, "sourceDialects": ["@substrate-connect/source"]}, "devDependencies": {"vitest": "^2.0.5"}, "scripts": {"deep-clean": "rimraf dist node_modules", "clean": "<PERSON><PERSON><PERSON> dist", "build": "npm run clean && tshy", "test": "exit 0; #This package is only types", "lint": "eslint . --ext .js,.ts"}}
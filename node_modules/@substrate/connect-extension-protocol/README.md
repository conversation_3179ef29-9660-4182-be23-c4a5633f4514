<br /><br />

<div align="center">
  <h1 align="center">@substrate/connect-extension-protocol</h1>
  <p align="center">
    <a href="https://www.npmjs.com/package/@substrate/connect-extension-protocol">
      <img alt="npm" src="https://img.shields.io/npm/v/@substrate/connect-extension-protocol" />
    </a>
    <a href="https://github.com/paritytech/substrate-connect/blob/master/LICENSE">
      <img alt="GPL-3.0-or-later" src="https://img.shields.io/npm/l/@substrate/connect-extension-protocol" />
    </a>
  </p>
</div>

<br /><br />


This is a module consisting (almost) only of types. These types are the types
of the messages exchanged between apps using `@substrate/connect` and the
browser extension. This package is shared between `@substrate/connect` and the
extension in order for the TypeScript compiler to enforce the contract between
them.

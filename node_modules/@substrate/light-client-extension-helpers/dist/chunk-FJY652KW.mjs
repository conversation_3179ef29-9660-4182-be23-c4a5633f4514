import {
  isRpcMessage
} from "./chunk-KOQEJYB7.mjs";

// src/shared/constants.ts
var extensionPrefix = "@substrate/light-client-extension-helper";
var contextPrefix = `${extensionPrefix}-context`;
var CONTEXT = {
  CONTENT_SCRIPT: `${contextPrefix}-content-script`,
  BACKGROUND: `${contextPrefix}-background`,
  EXTENSION_PAGE: `${contextPrefix}-extension-page`,
  WEB_PAGE: `${contextPrefix}-web-page`
};
var portPrefix = `${extensionPrefix}-port`;
var PORT = {
  CONTENT_SCRIPT: `${portPrefix}-content-script`,
  EXTENSION_PAGE: `${portPrefix}-extension-page`,
  WEB_PAGE: `${portPrefix}-web-page`
};
var STORAGE_PREFIX = extensionPrefix;
var alarmPrefix = `${extensionPrefix}-alarm`;
var ALARM = {
  DATABASE_UPDATE: `${alarmPrefix}-database-update`
};
var KEEP_ALIVE_INTERVAL = 2e4;

// src/shared/createBackgroundClientConnectProvider.ts
import { getSyncProvider } from "@polkadot-api/json-rpc-provider-proxy";

// src/shared/getRandomChainId.ts
var getRandomChainId = () => {
  const arr = new BigUint64Array(2);
  crypto.getRandomValues(arr);
  const result = arr[1] << BigInt(64) | arr[0];
  return result.toString(36);
};

// src/shared/createBackgroundClientConnectProvider.ts
var createBackgroundClientConnectProvider = ({
  genesisHash,
  chainSpec,
  relayChainGenesisHash,
  postMessage,
  addOnMessageListener,
  addOnDisconnectListener
}) => getSyncProvider(async () => {
  const chainId = getRandomChainId();
  await new Promise((resolve, reject) => {
    const removeOnMessageListener = addOnMessageListener((msg) => {
      if (msg?.chainId !== chainId)
        return;
      switch (msg.type) {
        case "chain-ready": {
          resolve();
          break;
        }
        case "error": {
          reject(new Error(msg.errorMessage));
          break;
        }
        default:
          reject(new Error(`Unrecognized message ${JSON.stringify(msg)}`));
          break;
      }
      removeOnMessageListener();
    });
    postMessage(
      chainSpec ? {
        origin: "substrate-connect-client",
        type: "add-chain",
        chainId,
        chainSpec,
        potentialRelayChainIds: relayChainGenesisHash ? [relayChainGenesisHash] : []
      } : {
        origin: "substrate-connect-client",
        type: "add-well-known-chain",
        chainId,
        chainName: genesisHash
      }
    );
  });
  return (onMessage, onHalt) => {
    const removeOnMessageListener = addOnMessageListener((msg) => {
      if (msg.chainId !== chainId)
        return;
      switch (msg.type) {
        case "rpc": {
          onMessage(msg.jsonRpcMessage);
          break;
        }
        case "error": {
          console.error(msg.errorMessage);
          removeListeners();
          onHalt();
          break;
        }
        default:
          console.warn(`Unrecognized message ${JSON.stringify(msg)}`);
          break;
      }
    });
    const removeOnDisconnectListener = addOnDisconnectListener?.(onHalt);
    const removeListeners = () => {
      removeOnMessageListener();
      removeOnDisconnectListener?.();
    };
    return {
      send(jsonRpcMessage) {
        postMessage({
          origin: "substrate-connect-client",
          type: "rpc",
          chainId,
          jsonRpcMessage
        });
      },
      disconnect() {
        removeListeners();
        postMessage({
          origin: "substrate-connect-client",
          type: "remove-chain",
          chainId
        });
      }
    };
  };
});

// src/shared/message-utils.ts
var isSubstrateConnectToExtensionMessage = (msg) => {
  if (typeof msg !== "object")
    return false;
  if (msg.origin !== "substrate-connect-client")
    return false;
  return true;
};
var isSubstrateConnectToApplicationMessage = (msg) => {
  if (typeof msg !== "object")
    return false;
  if (msg.origin !== "substrate-connect-extension")
    return false;
  return true;
};
var isRpcMessageWithOrigin = (msg, origin) => {
  if (!isRpcMessage(msg))
    return false;
  if ("origin" in msg && msg.origin !== origin)
    return false;
  return true;
};

export {
  CONTEXT,
  PORT,
  STORAGE_PREFIX,
  ALARM,
  KEEP_ALIVE_INTERVAL,
  createBackgroundClientConnectProvider,
  isSubstrateConnectToExtensionMessage,
  isSubstrateConnectToApplicationMessage,
  isRpcMessageWithOrigin
};
//# sourceMappingURL=chunk-FJY652KW.mjs.map
{"version": 3, "sources": ["../../src/extension-page/extension-page-helper.ts"], "sourcesContent": ["import { PORT, createBackgroundClientConnectProvider } from \"@/shared\"\nimport { createRpc } from \"@/utils\"\nimport * as storage from \"@/storage\"\nimport type { LightClientPageHelper } from \"./types\"\nimport type { BackgroundRpcSpec } from \"@/background/types\"\n\nexport type * from \"./types\"\n\n// FIXME: re-connect?\nconst port = chrome.runtime.connect({ name: PORT.EXTENSION_PAGE })\nconst rpc = createRpc((msg) =>\n  port.postMessage(msg),\n).withClient<BackgroundRpcSpec>()\nport.onMessage.addListener(rpc.handle)\n\nexport const helper: LightClientPageHelper = {\n  async deleteChain(genesisHash) {\n    await rpc.client.deleteChain(genesisHash)\n  },\n  async persistChain(chainSpec, relayChainGenesisHash) {\n    await rpc.client.persistChain(chainSpec, relayChainGenesisHash)\n  },\n  async getChains() {\n    return Promise.all(\n      Object.entries(await storage.getChains()).map(\n        async ([genesisHash, chain]) => ({\n          ...chain,\n          bootNodes:\n            (await storage.get({ type: \"bootNodes\", genesisHash })) ??\n            (JSON.parse(chain.chainSpec).bootNodes as string[]),\n          provider: createBackgroundClientConnectProvider({\n            genesisHash,\n            postMessage(msg) {\n              port.postMessage(msg)\n            },\n            addOnMessageListener(cb) {\n              port.onMessage.addListener(cb)\n              return () => port.onMessage.removeListener(cb)\n            },\n            addOnDisconnectListener(cb) {\n              port.onDisconnect.addListener(cb)\n              return () => port.onDisconnect.removeListener(cb)\n            },\n          }),\n        }),\n      ),\n    )\n  },\n  async getActiveConnections() {\n    const connections = await rpc.client.getActiveConnections()\n    return connections.map(({ tabId, chain }) => ({\n      tabId,\n      chain: {\n        ...chain,\n        provider: createBackgroundClientConnectProvider({\n          genesisHash: chain.genesisHash,\n          chainSpec: chain.chainSpec,\n          relayChainGenesisHash: chain.relayChainGenesisHash,\n          postMessage(msg) {\n            port.postMessage(msg)\n          },\n          addOnMessageListener(cb) {\n            port.onMessage.addListener(cb)\n            return () => port.onMessage.removeListener(cb)\n          },\n          addOnDisconnectListener(cb) {\n            port.onDisconnect.addListener(cb)\n            return () => port.onDisconnect.removeListener(cb)\n          },\n        }),\n      },\n    }))\n  },\n  async disconnect(tabId: number, genesisHash: string) {\n    await rpc.client.disconnect(tabId, genesisHash)\n  },\n  async setBootNodes(genesisHash, bootNodes) {\n    await rpc.client.setBootNodes(genesisHash, bootNodes)\n  },\n}\n"], "mappings": ";;;;;;;;;;;;;AASA,IAAM,OAAO,OAAO,QAAQ,QAAQ,EAAE,MAAM,KAAK,eAAe,CAAC;AACjE,IAAM,MAAM;AAAA,EAAU,CAAC,QACrB,KAAK,YAAY,GAAG;AACtB,EAAE,WAA8B;AAChC,KAAK,UAAU,YAAY,IAAI,MAAM;AAE9B,IAAM,SAAgC;AAAA,EAC3C,MAAM,YAAY,aAAa;AAC7B,UAAM,IAAI,OAAO,YAAY,WAAW;AAAA,EAC1C;AAAA,EACA,MAAM,aAAa,WAAW,uBAAuB;AACnD,UAAM,IAAI,OAAO,aAAa,WAAW,qBAAqB;AAAA,EAChE;AAAA,EACA,MAAM,YAAY;AAChB,WAAO,QAAQ;AAAA,MACb,OAAO,QAAQ,MAAc,UAAU,CAAC,EAAE;AAAA,QACxC,OAAO,CAAC,aAAa,KAAK,OAAO;AAAA,UAC/B,GAAG;AAAA,UACH,WACG,MAAc,IAAI,EAAE,MAAM,aAAa,YAAY,CAAC,KACpD,KAAK,MAAM,MAAM,SAAS,EAAE;AAAA,UAC/B,UAAU,sCAAsC;AAAA,YAC9C;AAAA,YACA,YAAY,KAAK;AACf,mBAAK,YAAY,GAAG;AAAA,YACtB;AAAA,YACA,qBAAqB,IAAI;AACvB,mBAAK,UAAU,YAAY,EAAE;AAC7B,qBAAO,MAAM,KAAK,UAAU,eAAe,EAAE;AAAA,YAC/C;AAAA,YACA,wBAAwB,IAAI;AAC1B,mBAAK,aAAa,YAAY,EAAE;AAChC,qBAAO,MAAM,KAAK,aAAa,eAAe,EAAE;AAAA,YAClD;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,MAAM,uBAAuB;AAC3B,UAAM,cAAc,MAAM,IAAI,OAAO,qBAAqB;AAC1D,WAAO,YAAY,IAAI,CAAC,EAAE,OAAO,MAAM,OAAO;AAAA,MAC5C;AAAA,MACA,OAAO;AAAA,QACL,GAAG;AAAA,QACH,UAAU,sCAAsC;AAAA,UAC9C,aAAa,MAAM;AAAA,UACnB,WAAW,MAAM;AAAA,UACjB,uBAAuB,MAAM;AAAA,UAC7B,YAAY,KAAK;AACf,iBAAK,YAAY,GAAG;AAAA,UACtB;AAAA,UACA,qBAAqB,IAAI;AACvB,iBAAK,UAAU,YAAY,EAAE;AAC7B,mBAAO,MAAM,KAAK,UAAU,eAAe,EAAE;AAAA,UAC/C;AAAA,UACA,wBAAwB,IAAI;AAC1B,iBAAK,aAAa,YAAY,EAAE;AAChC,mBAAO,MAAM,KAAK,aAAa,eAAe,EAAE;AAAA,UAClD;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF,EAAE;AAAA,EACJ;AAAA,EACA,MAAM,WAAW,OAAe,aAAqB;AACnD,UAAM,IAAI,OAAO,WAAW,OAAO,WAAW;AAAA,EAChD;AAAA,EACA,MAAM,aAAa,aAAa,WAAW;AACzC,UAAM,IAAI,OAAO,aAAa,aAAa,SAAS;AAAA,EACtD;AACF;", "names": []}
import {
  get,
  getChains
} from "../chunk-TMDP3M7I.mjs";
import {
  PORT,
  createBackgroundClientConnectProvider
} from "../chunk-FJY652KW.mjs";
import {
  createRpc
} from "../chunk-KOQEJYB7.mjs";

// src/extension-page/extension-page-helper.ts
var port = chrome.runtime.connect({ name: PORT.EXTENSION_PAGE });
var rpc = createRpc(
  (msg) => port.postMessage(msg)
).withClient();
port.onMessage.addListener(rpc.handle);
var helper = {
  async deleteChain(genesisHash) {
    await rpc.client.deleteChain(genesisHash);
  },
  async persistChain(chainSpec, relayChainGenesisHash) {
    await rpc.client.persistChain(chainSpec, relayChainGenesisHash);
  },
  async getChains() {
    return Promise.all(
      Object.entries(await getChains()).map(
        async ([genesisHash, chain]) => ({
          ...chain,
          bootNodes: await get({ type: "bootNodes", genesisHash }) ?? JSON.parse(chain.chainSpec).bootNodes,
          provider: createBackgroundClientConnectProvider({
            genesisHash,
            postMessage(msg) {
              port.postMessage(msg);
            },
            addOnMessageListener(cb) {
              port.onMessage.addListener(cb);
              return () => port.onMessage.removeListener(cb);
            },
            addOnDisconnectListener(cb) {
              port.onDisconnect.addListener(cb);
              return () => port.onDisconnect.removeListener(cb);
            }
          })
        })
      )
    );
  },
  async getActiveConnections() {
    const connections = await rpc.client.getActiveConnections();
    return connections.map(({ tabId, chain }) => ({
      tabId,
      chain: {
        ...chain,
        provider: createBackgroundClientConnectProvider({
          genesisHash: chain.genesisHash,
          chainSpec: chain.chainSpec,
          relayChainGenesisHash: chain.relayChainGenesisHash,
          postMessage(msg) {
            port.postMessage(msg);
          },
          addOnMessageListener(cb) {
            port.onMessage.addListener(cb);
            return () => port.onMessage.removeListener(cb);
          },
          addOnDisconnectListener(cb) {
            port.onDisconnect.addListener(cb);
            return () => port.onDisconnect.removeListener(cb);
          }
        })
      }
    }));
  },
  async disconnect(tabId, genesisHash) {
    await rpc.client.disconnect(tabId, genesisHash);
  },
  async setBootNodes(genesisHash, bootNodes) {
    await rpc.client.setBootNodes(genesisHash, bootNodes);
  }
};
export {
  helper
};
//# sourceMappingURL=extension-page-helper.mjs.map
import { a as ClientOptions, C as Client } from '../types-BlA9-2mv.mjs';
export { A as AddChainOptions, b as Chain } from '../types-BlA9-2mv.mjs';
export { AddChainError, AlreadyD<PERSON>royedError, CrashError, JsonRpcDisabledError, QueueFullError } from 'smoldot';

/**
 * Wraps a smoldot client to add restart functionality.
 *
 * The key difference from the standard smoldot client is how relay chains are
 * specified. Instead of using `Chain` objects for `potentialRelayChains`,
 * we use `AddChainOptions`. This is necessary because the `Chain` objects
 * become invalid after a restart and can't be used in a replay `addChain` call.
 *
 * With `AddChainOptions`, we can easily re-add a parachain after a restart by
 * reusing the same options for each relay chain. Before adding the parachain,
 * we add the relay chains using their `AddChainOptions` and then remove them
 * after the parachain is created.
 *
 * This ensures the parachain can always be added successfully, as its relay
 * chain is always added first.
 */
declare const start: (options?: ClientOptions) => Client;

type SuperviseOptions = {
    repeatScheduleMs?: number;
    retryScheduleMs?: number;
    abortSignal?: AbortSignal;
    onError?: (error: Error) => void;
};
declare const DEFAULT_SUPERVISE_REPEAT_SCHEDULE = 1000;
declare const DEFAULT_SUPERVISE_RETRY_SCHEDULE = 1000;
/**
 * Supervises a smoldot client by periodically invoking `addChain` with an
 * empty chain spec.
 *
 * If `addChain` fails with anything other than a `AddChainError`, the client
 * will be restarted.
 *
 * @param client - The smoldot client instance to be supervised.
 * @param options - Options for customizing the supervision behavior.
 * @param options.repeatSchedule - The frequency at which to invoke `addChain`.
 * Defaults to {@link DEFAULT_SUPERVISE_REPEAT_SCHEDULE}.
 * @param options.retrySchedule - The frequency at which to attempt restarting
 * the client if needed. Defaults to {@link DEFAULT_SUPERVISE_RETRY_SCHEDULE}.
 * @param options.abortSignal - An `AbortSignal` that can be used to
 * stop the supervision.
 * @param options.onError - error handler for whenever smoldot crashes
 */
declare const supervise: (client: Client, options?: SuperviseOptions) => void;

export { Client, ClientOptions, DEFAULT_SUPERVISE_REPEAT_SCHEDULE, DEFAULT_SUPERVISE_RETRY_SCHEDULE, type SuperviseOptions, start, supervise };

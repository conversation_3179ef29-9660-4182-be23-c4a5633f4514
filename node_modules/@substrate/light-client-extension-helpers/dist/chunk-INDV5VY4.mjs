// src/smoldot/client.ts
import * as smoldot from "smoldot";
var start2 = (options) => {
  let client = smoldot.start(options);
  let terminated = false;
  const addChain = async (options2) => {
    const potentialRelayChains = await Promise.all(
      options2.potentialRelayChains?.map(
        (options3) => client.addChain(options3)
      ) ?? []
    );
    const newChain = await client.addChain({
      ...options2,
      potentialRelayChains
    });
    await Promise.all(potentialRelayChains.map((chain) => chain.remove()));
    return newChain;
  };
  const terminate = () => {
    terminated = true;
    return client.terminate();
  };
  const restart = async () => {
    if (terminated) {
      throw new Error("Cannot restart a terminated client");
    }
    try {
      await client.terminate();
    } catch {
    }
    client = smoldot.start(options);
  };
  return {
    addChain,
    restart,
    terminate
  };
};

// src/smoldot/types.ts
import {
  Add<PERSON>hain<PERSON>rror,
  AlreadyD<PERSON><PERSON>ed<PERSON>rror,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>r,
  <PERSON><PERSON><PERSON><PERSON>,
  QueueFullError
} from "smoldot";

// src/smoldot/tasks.ts
var DEFAULT_SUPERVISE_REPEAT_SCHEDULE = 1e3;
var DEFAULT_SUPERVISE_RETRY_SCHEDULE = 1e3;
var supervise = (client, options = {}) => {
  const repeatScheduleMs = options.repeatScheduleMs ?? DEFAULT_SUPERVISE_REPEAT_SCHEDULE;
  const retryScheduleMs = options.retryScheduleMs ?? DEFAULT_SUPERVISE_RETRY_SCHEDULE;
  if (options?.abortSignal?.aborted) {
    return;
  }
  let stopped = false;
  async function checkIfSmoldotIsHealthy() {
    return client.addChain({ chainSpec: "" }).then(() => void 0).catch((err) => {
      if (err instanceof AddChainError) {
        return;
      }
      throw err;
    });
  }
  ;
  (async () => {
    while (!stopped) {
      try {
        await checkIfSmoldotIsHealthy();
        await sleep(repeatScheduleMs);
      } catch (err) {
        try {
          options.onError?.(err);
          await client.restart();
          await sleep(retryScheduleMs);
        } catch {
        }
      }
    }
  })();
  if (options.abortSignal) {
    options.abortSignal.addEventListener("abort", () => {
      stopped = true;
    });
  }
};
function sleep(ms) {
  return new Promise((resolve) => {
    setTimeout(resolve, ms);
  });
}

export {
  start2 as start,
  AddChainError,
  AlreadyDestroyedError,
  JsonRpcDisabledError,
  CrashError,
  QueueFullError,
  DEFAULT_SUPERVISE_REPEAT_SCHEDULE,
  DEFAULT_SUPERVISE_RETRY_SCHEDULE,
  supervise
};
//# sourceMappingURL=chunk-INDV5VY4.mjs.map
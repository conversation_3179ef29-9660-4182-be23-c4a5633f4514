{"version": 3, "sources": ["../src/smoldot/client.ts", "../src/smoldot/types.ts", "../src/smoldot/tasks.ts"], "sourcesContent": ["import * as smoldot from \"smoldot\"\nimport { AddChainOptions, Client, ClientOptions } from \"./types\"\n\n/**\n * Wraps a smoldot client to add restart functionality.\n *\n * The key difference from the standard smoldot client is how relay chains are\n * specified. Instead of using `Chain` objects for `potentialRelayChains`,\n * we use `AddChainOptions`. This is necessary because the `Chain` objects\n * become invalid after a restart and can't be used in a replay `addChain` call.\n *\n * With `AddChainOptions`, we can easily re-add a parachain after a restart by\n * reusing the same options for each relay chain. Before adding the parachain,\n * we add the relay chains using their `AddChainOptions` and then remove them\n * after the parachain is created.\n *\n * This ensures the parachain can always be added successfully, as its relay\n * chain is always added first.\n */\nexport const start = (options?: ClientOptions): Client => {\n  let client = smoldot.start(options)\n  let terminated = false\n\n  const addChain = async (options: AddChainOptions) => {\n    const potentialRelayChains = await Promise.all(\n      options.potentialRelayChains?.map((options) =>\n        client.addChain(options),\n      ) ?? [],\n    )\n\n    const newChain = await client.addChain({\n      ...options,\n      potentialRelayChains,\n    })\n\n    await Promise.all(potentialRelayChains.map((chain) => chain.remove()))\n\n    return newChain\n  }\n\n  const terminate = () => {\n    terminated = true\n    return client.terminate()\n  }\n\n  const restart = async () => {\n    if (terminated) {\n      throw new Error(\"Cannot restart a terminated client\")\n    }\n\n    try {\n      await client.terminate()\n    } catch {}\n    client = smoldot.start(options)\n  }\n\n  return {\n    addChain,\n    restart,\n    terminate,\n  }\n}\n", "import * as smoldot from \"smoldot\"\n\nexport type ClientOptions = Readonly<smoldot.ClientOptions>\n\ntype AddRelayChainOptions = Omit<\n  smoldot.AddChainOptions,\n  \"potentialRelayChains\"\n>\n\nexport type AddChainOptions = Readonly<\n  AddRelayChainOptions & {\n    potentialRelayChains?: Readonly<AddRelayChainOptions>[]\n  }\n>\n\nexport type Client = {\n  addChain: (options: AddChainOptions) => Promise<Chain>\n  terminate: () => Promise<void>\n  restart: () => Promise<void>\n}\n\nexport type Chain = smoldot.Chain\n\nexport {\n  AddChainError,\n  AlreadyDestroyedError,\n  JsonRpcDisabledError,\n  CrashError,\n  QueueFullError,\n} from \"smoldot\"\n", "import { Client, Add<PERSON>hainError } from \"./types\"\n\nexport type SuperviseOptions = {\n  repeatScheduleMs?: number\n  retryScheduleMs?: number\n  abortSignal?: AbortSignal\n  onError?: (error: Error) => void\n}\n\nexport const DEFAULT_SUPERVISE_REPEAT_SCHEDULE = 1000\nexport const DEFAULT_SUPERVISE_RETRY_SCHEDULE = 1000\n\n/**\n * Supervises a smoldot client by periodically invoking `add<PERSON>hain` with an\n * empty chain spec.\n *\n * If `add<PERSON>hain` fails with anything other than a `AddChainError`, the client\n * will be restarted.\n *\n * @param client - The smoldot client instance to be supervised.\n * @param options - Options for customizing the supervision behavior.\n * @param options.repeatSchedule - The frequency at which to invoke `addChain`.\n * Defaults to {@link DEFAULT_SUPERVISE_REPEAT_SCHEDULE}.\n * @param options.retrySchedule - The frequency at which to attempt restarting\n * the client if needed. Defaults to {@link DEFAULT_SUPERVISE_RETRY_SCHEDULE}.\n * @param options.abortSignal - An `AbortSignal` that can be used to\n * stop the supervision.\n * @param options.onError - error handler for whenever smoldot crashes\n */\nexport const supervise = (\n  client: Client,\n  options: SuperviseOptions = {},\n): void => {\n  const repeatScheduleMs =\n    options.repeatScheduleMs ?? DEFAULT_SUPERVISE_REPEAT_SCHEDULE\n  const retryScheduleMs =\n    options.retryScheduleMs ?? DEFAULT_SUPERVISE_RETRY_SCHEDULE\n\n  if (options?.abortSignal?.aborted) {\n    return\n  }\n\n  let stopped = false\n\n  async function checkIfSmoldotIsHealthy(): Promise<void> {\n    return client\n      .addChain({ chainSpec: \"\" })\n      .then(() => void 0)\n      .catch((err) => {\n        if (err instanceof AddChainError) {\n          return\n        }\n        throw err\n      })\n  }\n\n  ;(async () => {\n    while (!stopped) {\n      try {\n        await checkIfSmoldotIsHealthy()\n        await sleep(repeatScheduleMs)\n      } catch (err) {\n        try {\n          options.onError?.(err as Error)\n          await client.restart()\n          await sleep(retryScheduleMs)\n        } catch {}\n      }\n    }\n  })()\n\n  if (options.abortSignal) {\n    options.abortSignal.addEventListener(\"abort\", () => {\n      stopped = true\n    })\n  }\n}\n\nfunction sleep(ms: number): Promise<void> {\n  return new Promise((resolve) => {\n    setTimeout(resolve, ms)\n  })\n}\n"], "mappings": ";AAAA,YAAY,aAAa;AAmBlB,IAAMA,SAAQ,CAAC,YAAoC;AACxD,MAAI,SAAiB,cAAM,OAAO;AAClC,MAAI,aAAa;AAEjB,QAAM,WAAW,OAAOC,aAA6B;AACnD,UAAM,uBAAuB,MAAM,QAAQ;AAAA,MACzCA,SAAQ,sBAAsB;AAAA,QAAI,CAACA,aACjC,OAAO,SAASA,QAAO;AAAA,MACzB,KAAK,CAAC;AAAA,IACR;AAEA,UAAM,WAAW,MAAM,OAAO,SAAS;AAAA,MACrC,GAAGA;AAAA,MACH;AAAA,IACF,CAAC;AAED,UAAM,QAAQ,IAAI,qBAAqB,IAAI,CAAC,UAAU,MAAM,OAAO,CAAC,CAAC;AAErE,WAAO;AAAA,EACT;AAEA,QAAM,YAAY,MAAM;AACtB,iBAAa;AACb,WAAO,OAAO,UAAU;AAAA,EAC1B;AAEA,QAAM,UAAU,YAAY;AAC1B,QAAI,YAAY;AACd,YAAM,IAAI,MAAM,oCAAoC;AAAA,IACtD;AAEA,QAAI;AACF,YAAM,OAAO,UAAU;AAAA,IACzB,QAAQ;AAAA,IAAC;AACT,aAAiB,cAAM,OAAO;AAAA,EAChC;AAEA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;;;ACtCA;AAAA,EACE;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,OACK;;;ACpBA,IAAM,oCAAoC;AAC1C,IAAM,mCAAmC;AAmBzC,IAAM,YAAY,CACvB,QACA,UAA4B,CAAC,MACpB;AACT,QAAM,mBACJ,QAAQ,oBAAoB;AAC9B,QAAM,kBACJ,QAAQ,mBAAmB;AAE7B,MAAI,SAAS,aAAa,SAAS;AACjC;AAAA,EACF;AAEA,MAAI,UAAU;AAEd,iBAAe,0BAAyC;AACtD,WAAO,OACJ,SAAS,EAAE,WAAW,GAAG,CAAC,EAC1B,KAAK,MAAM,MAAM,EACjB,MAAM,CAAC,QAAQ;AACd,UAAI,eAAe,eAAe;AAChC;AAAA,MACF;AACA,YAAM;AAAA,IACR,CAAC;AAAA,EACL;AAEA;AAAC,GAAC,YAAY;AACZ,WAAO,CAAC,SAAS;AACf,UAAI;AACF,cAAM,wBAAwB;AAC9B,cAAM,MAAM,gBAAgB;AAAA,MAC9B,SAAS,KAAK;AACZ,YAAI;AACF,kBAAQ,UAAU,GAAY;AAC9B,gBAAM,OAAO,QAAQ;AACrB,gBAAM,MAAM,eAAe;AAAA,QAC7B,QAAQ;AAAA,QAAC;AAAA,MACX;AAAA,IACF;AAAA,EACF,GAAG;AAEH,MAAI,QAAQ,aAAa;AACvB,YAAQ,YAAY,iBAAiB,SAAS,MAAM;AAClD,gBAAU;AAAA,IACZ,CAAC;AAAA,EACH;AACF;AAEA,SAAS,MAAM,IAA2B;AACxC,SAAO,IAAI,QAAQ,CAAC,YAAY;AAC9B,eAAW,SAAS,EAAE;AAAA,EACxB,CAAC;AACH;", "names": ["start", "options"]}
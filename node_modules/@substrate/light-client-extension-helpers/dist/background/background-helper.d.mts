import { L as LightClientPageHelper, A as AddOnAddChainByUserListener } from '../types-CpJ7qODO.mjs';
export { B as BackgroundRpcSpec, I as Input<PERSON>hain, P as <PERSON>Chain } from '../types-CpJ7qODO.mjs';
import { C as Client } from '../types-BlA9-2mv.mjs';
import '@polkadot-api/json-rpc-provider';
import 'smoldot';

type RegisterOptions = {
    smoldotClient: Client;
    getWellKnownChainSpecs: () => Promise<string[] | Record<string, string>>;
};
declare const register: ({ smoldotClient, getWellKnownChainSpecs, }: RegisterOptions) => {
    lightClientPageHelper: LightClientPageHelper;
    addOnAddChainByUserListener: AddOnAddChainByUserListener;
};

export { AddOnAddChainByUserListener, LightClientPageHelper, register };

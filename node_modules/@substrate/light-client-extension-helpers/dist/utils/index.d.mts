type RpcRequestMessage = {
    id?: string;
    method: string;
    params?: any[];
};
type RpcResponseMessage = {
    id: string;
    result: any;
} | {
    id: string;
    error: {
        code: number;
        message: string;
        data?: any;
    };
};
type RpcMessage = RpcRequestMessage | RpcResponseMessage;
type RpcMethod = (...params: any[]) => any | Promise<any>;
type RpcSpec = Record<string, RpcMethod>;
type RpcMethodHandlers<TRpcSpec extends RpcSpec, TContext = void> = {
    [method in keyof TRpcSpec]: (params: Parameters<TRpcSpec[method]>, context: TContext) => ReturnType<TRpcSpec[method]>;
};
type RpcMethodMiddleware<Context = any> = (next: RpcMethodMiddlewareNext<Context>, request: RpcRequestMessage, context: Context) => Promise<any>;
type RpcMethodMiddlewareNext<Context> = (request: RpcRequestMessage, context: Context) => Promise<any>;
declare const createRpc: <TContext>(sendMessage: (message: RpcMessage) => void, handlers?: RpcMethodHandlers<RpcSpec, TContext>, middlewares?: RpcMethodMiddleware<TContext>[]) => {
    request: <T>(method: string, params: any[]) => Promise<T>;
    notify: (method: string, params: any[]) => void;
    handle: (message: RpcMessage, context: TContext) => Promise<void>;
    withClient<TRpcSpec extends RpcSpec>(): {
        request<TMethod extends string & keyof TRpcSpec, TParams extends Parameters<TRpcSpec[TMethod]>, TReturn extends Awaited<ReturnType<TRpcSpec[TMethod]>>>(method: TMethod, params: TParams): Promise<TReturn>;
        notify<TMethod_1 extends string & keyof TRpcSpec, TParams_1 extends Parameters<TRpcSpec[TMethod_1]>>(method: TMethod_1, params: TParams_1): void;
        handle: (message: RpcMessage, context: TContext) => Promise<void>;
        client: TRpcSpec;
    };
};
declare const isRpcMessage: (message: any) => message is RpcMessage;
declare const isRpcRequestMessage: (message: any) => message is RpcRequestMessage;
declare class RpcError extends Error {
    readonly message: string;
    readonly code: number;
    readonly data?: any;
    constructor(message: string, code: number, data?: any);
}

export { RpcError, type RpcMessage, type RpcMethodHandlers, type RpcMethodMiddleware, createRpc, isRpcMessage, isRpcRequestMessage };

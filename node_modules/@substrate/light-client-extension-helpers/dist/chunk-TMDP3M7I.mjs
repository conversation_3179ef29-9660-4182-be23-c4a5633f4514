import {
  STORAGE_PREFIX
} from "./chunk-FJY652KW.mjs";

// src/storage/index.ts
var chainStoragePrefix = `${STORAGE_PREFIX}_chain_`;
var keyOf = ({ type, genesisHash }) => {
  if (!type.length || !genesisHash.length)
    throw new Error("Invalid entry");
  return `${STORAGE_PREFIX}_${type}_${genesisHash}`;
};
var get = async (entry) => {
  const key = keyOf(entry);
  const { [key]: value } = await chrome.storage.local.get([key]);
  return value;
};
var set = (entry, value) => chrome.storage.local.set({ [keyOf(entry)]: value });
var remove = (entryOrEntries) => chrome.storage.local.remove(
  Array.isArray(entryOrEntries) ? entryOrEntries.map(keyOf) : keyOf(entryOrEntries)
);
var onChainsChanged = (callback) => {
  const listener = async (changes) => {
    if (!Object.keys(changes).some((key) => key.startsWith(chainStoragePrefix)))
      return;
    callback(await getChains());
  };
  chrome.storage.onChanged.addListener(listener);
  return () => chrome.storage.onChanged.removeListener(listener);
};
var getChains = async () => Object.fromEntries(
  await Promise.all(
    Object.entries(await chrome.storage.local.get()).filter(
      (entry) => entry[0].startsWith(chainStoragePrefix)
    ).map(async ([_, { chainSpec, ...chain }]) => {
      const chainSpecJson = JSON.parse(chainSpec);
      chainSpecJson.bootNodes = await get({
        type: "bootNodes",
        genesisHash: chain.genesisHash
      });
      return [
        chain.genesisHash,
        { ...chain, chainSpec: JSON.stringify(chainSpecJson) }
      ];
    })
  )
);

export {
  get,
  set,
  remove,
  onChainsChanged,
  getChains
};
//# sourceMappingURL=chunk-TMDP3M7I.mjs.map
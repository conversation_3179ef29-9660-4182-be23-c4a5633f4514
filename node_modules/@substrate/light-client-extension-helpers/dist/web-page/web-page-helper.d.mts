type Callback<T> = (value: T) => void
type UnsubscribeFn = () => void

interface LightClientProvider {
  // Allows dApp developers to request the provider to register their chain
  getChain: (
    chainSpec: string,
    relayChainGenesisHash?: string,
  ) => Promise<RawChain>

  // Retrieves the current list of available Chains
  getChains: () => RawChains

  // Registers a callback invoked when the list of available chains changes
  addChainsChangeListener: (listener: Callback<RawChains>) => UnsubscribeFn
}

// The key is the genesis hash
type RawChains = Record<string, RawChain>

interface RawChain {
  genesisHash: string

  name: string

  connect: (
    // the listener callback that the JsonRpcProvider will be sending messages to.
    onMessage: Callback<string>,
  ) => JsonRpcProvider
}

interface JsonRpcProvider {
  // it sends messages to the JSON RPC Server
  send: Callback<string>

  // it disconnects from the JSON RPC Server and it de-registers
  // the `onMessage` and `onStatusChange` callbacks that was previously registered
  disconnect: UnsubscribeFn
}

type LightClientProviderDetail = {
  info: LightClientProviderInfo
  // FIXME: update to PolkadotProvider from https://github.com/paritytech/polkadot-provider
  provider: Promise<LightClientProvider>
}

type LightClientOnProvider = {
  onProvider(detail: LightClientProviderDetail): void
}

type LightClientProviderInfo = {
  uuid: string
  name: string
  icon: string
  rdns: string
}

declare global {
  interface WindowEventMap {
    "lightClient:announceProvider": LightClientAnnounceProviderEvent
    "lightClient:requestProvider": LightClientRequestProviderEvent
  }
}

interface LightClientAnnounceProviderEvent
  extends CustomEvent<LightClientProviderDetail> {
  type: "lightClient:announceProvider"
}

interface LightClientRequestProviderEvent
  extends CustomEvent<LightClientOnProvider> {
  type: "lightClient:requestProvider"
}

type WebPageRpcSpec = {
  onAddChains(
    chains: Record<string, { genesisHash: string; name: string }>,
  ): void
}

declare const getLightClientProvider: (channelId: string) => Promise<LightClientProvider>;

export { type JsonRpcProvider, type LightClientAnnounceProviderEvent, type LightClientOnProvider, type LightClientProvider, type LightClientProviderDetail, type LightClientProviderInfo, type LightClientRequestProviderEvent, type RawChain, type WebPageRpcSpec, getLightClientProvider };

import {
  CONTEXT,
  createBackground<PERSON>lientConnectProvider,
  isRpcMessageWithOrigin,
  isSubstrateConnectToApplicationMessage
} from "../chunk-FJY652KW.mjs";
import {
  createRpc
} from "../chunk-KOQEJYB7.mjs";

// src/web-page/web-page-helper.ts
var postToExtension = (message) => window.postMessage(message, window.origin);
var channelIds = /* @__PURE__ */ new Set();
var getLightClientProvider = async (channelId) => {
  if (channelIds.has(channelId))
    throw new Error(`channelId "${channelId}" already in use`);
  channelIds.add(channelId);
  const chainsChangeCallbacks = [];
  const handlers = {
    onAddChains([chains2]) {
      chainsChangeCallbacks.forEach(
        (cb) => cb(
          Object.fromEntries(
            Object.entries(chains2).map(([key, { genesisHash, name }]) => [
              key,
              createRaw<PERSON>hain(channelId, { genesisHash, name })
            ])
          )
        )
      );
    }
  };
  const rpc = createRpc(
    (msg) => window.postMessage(
      { channelId, msg: { origin: CONTEXT.WEB_PAGE, ...msg } },
      window.origin
    ),
    handlers
  ).withClient();
  window.addEventListener("message", ({ data, source }) => {
    if (source !== window || !data)
      return;
    const { channelId: messageChannelId, msg } = data;
    if (messageChannelId !== channelId)
      return;
    if (isRpcMessageWithOrigin(msg, CONTEXT.CONTENT_SCRIPT))
      return rpc.handle(msg);
    if (isSubstrateConnectToApplicationMessage(msg))
      return rawChainCallbacks.forEach((cb) => cb(msg));
  });
  let chains = await rpc.client.getChains();
  chainsChangeCallbacks.push((chains_) => chains = chains_);
  return {
    async getChain(chainSpec, relayChainGenesisHash) {
      const chainInfo = await rpc.client.getChain(
        chainSpec,
        relayChainGenesisHash
      );
      return createRawChain(
        channelId,
        chains[chainInfo.genesisHash] ? chainInfo : { ...chainInfo, chainSpec, relayChainGenesisHash }
      );
    },
    getChains() {
      return Object.entries(chains).reduce(
        (acc, [key, chain]) => {
          acc[key] = createRawChain(channelId, chain);
          return acc;
        },
        {}
      );
    },
    addChainsChangeListener(callback) {
      chainsChangeCallbacks.push(callback);
      return () => removeArrayItem(chainsChangeCallbacks, callback);
    }
  };
};
var rawChainCallbacks = [];
var createRawChain = (channelId, {
  name,
  genesisHash,
  chainSpec,
  relayChainGenesisHash
}) => {
  return {
    name,
    genesisHash,
    connect: createBackgroundClientConnectProvider({
      genesisHash,
      chainSpec,
      relayChainGenesisHash,
      postMessage(msg) {
        postToExtension({ channelId, msg });
      },
      addOnMessageListener(cb) {
        rawChainCallbacks.push(cb);
        return () => removeArrayItem(rawChainCallbacks, cb);
      }
    })
  };
};
var removeArrayItem = (array, item) => {
  array.splice(array.indexOf(item), 1);
};
export {
  getLightClientProvider
};
//# sourceMappingURL=web-page-helper.mjs.map
{"version": 3, "sources": ["../../src/web-page/web-page-helper.ts"], "sourcesContent": ["import type { ToExtensionMessage } from \"@/protocol\"\nimport {\n  CONTEXT,\n  createBackgroundClientConnectProvider,\n  isRpcMessageWithOrigin,\n  isSubstrateConnectToApplicationMessage,\n} from \"@/shared\"\nimport { createRpc, type RpcMethodHandlers } from \"@/utils\"\nimport type { LightClientProvider, RawChain, WebPageRpcSpec } from \"./types\"\nimport type { BackgroundRpcSpec } from \"@/background/types\"\nimport type { ToApplication } from \"@substrate/connect-extension-protocol\"\n\nexport type * from \"./types\"\n\nconst postToExtension = (message: ToExtensionMessage) =>\n  window.postMessage(message, window.origin)\n\nconst channelIds = new Set<string>()\n\nexport const getLightClientProvider = async (\n  channelId: string,\n): Promise<LightClientProvider> => {\n  if (channelIds.has(channelId))\n    throw new Error(`channelId \"${channelId}\" already in use`)\n  channelIds.add(channelId)\n\n  const chainsChangeCallbacks: Parameters<\n    LightClientProvider[\"addChainsChangeListener\"]\n  >[0][] = []\n  const handlers: RpcMethodHandlers<WebPageRpcSpec> = {\n    onAddChains([chains]) {\n      chainsChangeCallbacks.forEach((cb) =>\n        cb(\n          Object.fromEntries(\n            Object.entries(chains).map(([key, { genesisHash, name }]) => [\n              key,\n              createRawChain(channelId, { genesisHash, name }),\n            ]),\n          ),\n        ),\n      )\n    },\n  }\n  const rpc = createRpc(\n    (msg) =>\n      window.postMessage(\n        { channelId, msg: { origin: CONTEXT.WEB_PAGE, ...msg } },\n        window.origin,\n      ),\n    handlers,\n  ).withClient<BackgroundRpcSpec>()\n\n  window.addEventListener(\"message\", ({ data, source }) => {\n    if (source !== window || !data) return\n    const { channelId: messageChannelId, msg } = data\n    if (messageChannelId !== channelId) return\n    if (isRpcMessageWithOrigin(msg, CONTEXT.CONTENT_SCRIPT))\n      return rpc.handle(msg)\n    if (isSubstrateConnectToApplicationMessage(msg))\n      return rawChainCallbacks.forEach((cb) => cb(msg))\n  })\n\n  let chains = await rpc.client.getChains()\n  chainsChangeCallbacks.push((chains_) => (chains = chains_))\n  return {\n    async getChain(chainSpec, relayChainGenesisHash) {\n      const chainInfo = await rpc.client.getChain(\n        chainSpec,\n        relayChainGenesisHash,\n      )\n      return createRawChain(\n        channelId,\n        chains[chainInfo.genesisHash]\n          ? chainInfo\n          : { ...chainInfo, chainSpec, relayChainGenesisHash },\n      )\n    },\n    getChains() {\n      return Object.entries(chains).reduce(\n        (acc, [key, chain]) => {\n          acc[key] = createRawChain(channelId, chain)\n          return acc\n        },\n        {} as Record<string, RawChain>,\n      )\n    },\n    addChainsChangeListener(callback) {\n      chainsChangeCallbacks.push(callback)\n      return () => removeArrayItem(chainsChangeCallbacks, callback)\n    },\n  }\n}\n\nconst rawChainCallbacks: ((msg: ToApplication) => void)[] = []\n\nconst createRawChain = (\n  channelId: string,\n  {\n    name,\n    genesisHash,\n    chainSpec,\n    relayChainGenesisHash,\n  }: {\n    name: string\n    genesisHash: string\n    chainSpec?: string\n    relayChainGenesisHash?: string\n  },\n): RawChain => {\n  return {\n    name,\n    genesisHash,\n    connect: createBackgroundClientConnectProvider({\n      genesisHash,\n      chainSpec,\n      relayChainGenesisHash,\n      postMessage(msg) {\n        postToExtension({ channelId, msg })\n      },\n      addOnMessageListener(cb) {\n        rawChainCallbacks.push(cb)\n        return () => removeArrayItem(rawChainCallbacks, cb)\n      },\n    }),\n  }\n}\n\nconst removeArrayItem = <T>(array: T[], item: T) => {\n  array.splice(array.indexOf(item), 1)\n}\n"], "mappings": ";;;;;;;;;;;AAcA,IAAM,kBAAkB,CAAC,YACvB,OAAO,YAAY,SAAS,OAAO,MAAM;AAE3C,IAAM,aAAa,oBAAI,IAAY;AAE5B,IAAM,yBAAyB,OACpC,cACiC;AACjC,MAAI,WAAW,IAAI,SAAS;AAC1B,UAAM,IAAI,MAAM,cAAc,SAAS,kBAAkB;AAC3D,aAAW,IAAI,SAAS;AAExB,QAAM,wBAEG,CAAC;AACV,QAAM,WAA8C;AAAA,IAClD,YAAY,CAACA,OAAM,GAAG;AACpB,4BAAsB;AAAA,QAAQ,CAAC,OAC7B;AAAA,UACE,OAAO;AAAA,YACL,OAAO,QAAQA,OAAM,EAAE,IAAI,CAAC,CAAC,KAAK,EAAE,aAAa,KAAK,CAAC,MAAM;AAAA,cAC3D;AAAA,cACA,eAAe,WAAW,EAAE,aAAa,KAAK,CAAC;AAAA,YACjD,CAAC;AAAA,UACH;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,QAAM,MAAM;AAAA,IACV,CAAC,QACC,OAAO;AAAA,MACL,EAAE,WAAW,KAAK,EAAE,QAAQ,QAAQ,UAAU,GAAG,IAAI,EAAE;AAAA,MACvD,OAAO;AAAA,IACT;AAAA,IACF;AAAA,EACF,EAAE,WAA8B;AAEhC,SAAO,iBAAiB,WAAW,CAAC,EAAE,MAAM,OAAO,MAAM;AACvD,QAAI,WAAW,UAAU,CAAC;AAAM;AAChC,UAAM,EAAE,WAAW,kBAAkB,IAAI,IAAI;AAC7C,QAAI,qBAAqB;AAAW;AACpC,QAAI,uBAAuB,KAAK,QAAQ,cAAc;AACpD,aAAO,IAAI,OAAO,GAAG;AACvB,QAAI,uCAAuC,GAAG;AAC5C,aAAO,kBAAkB,QAAQ,CAAC,OAAO,GAAG,GAAG,CAAC;AAAA,EACpD,CAAC;AAED,MAAI,SAAS,MAAM,IAAI,OAAO,UAAU;AACxC,wBAAsB,KAAK,CAAC,YAAa,SAAS,OAAQ;AAC1D,SAAO;AAAA,IACL,MAAM,SAAS,WAAW,uBAAuB;AAC/C,YAAM,YAAY,MAAM,IAAI,OAAO;AAAA,QACjC;AAAA,QACA;AAAA,MACF;AACA,aAAO;AAAA,QACL;AAAA,QACA,OAAO,UAAU,WAAW,IACxB,YACA,EAAE,GAAG,WAAW,WAAW,sBAAsB;AAAA,MACvD;AAAA,IACF;AAAA,IACA,YAAY;AACV,aAAO,OAAO,QAAQ,MAAM,EAAE;AAAA,QAC5B,CAAC,KAAK,CAAC,KAAK,KAAK,MAAM;AACrB,cAAI,GAAG,IAAI,eAAe,WAAW,KAAK;AAC1C,iBAAO;AAAA,QACT;AAAA,QACA,CAAC;AAAA,MACH;AAAA,IACF;AAAA,IACA,wBAAwB,UAAU;AAChC,4BAAsB,KAAK,QAAQ;AACnC,aAAO,MAAM,gBAAgB,uBAAuB,QAAQ;AAAA,IAC9D;AAAA,EACF;AACF;AAEA,IAAM,oBAAsD,CAAC;AAE7D,IAAM,iBAAiB,CACrB,WACA;AAAA,EACE;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,MAMa;AACb,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,SAAS,sCAAsC;AAAA,MAC7C;AAAA,MACA;AAAA,MACA;AAAA,MACA,YAAY,KAAK;AACf,wBAAgB,EAAE,WAAW,IAAI,CAAC;AAAA,MACpC;AAAA,MACA,qBAAqB,IAAI;AACvB,0BAAkB,KAAK,EAAE;AACzB,eAAO,MAAM,gBAAgB,mBAAmB,EAAE;AAAA,MACpD;AAAA,IACF,CAAC;AAAA,EACH;AACF;AAEA,IAAM,kBAAkB,CAAI,OAAY,SAAY;AAClD,QAAM,OAAO,MAAM,QAAQ,IAAI,GAAG,CAAC;AACrC;", "names": ["chains"]}
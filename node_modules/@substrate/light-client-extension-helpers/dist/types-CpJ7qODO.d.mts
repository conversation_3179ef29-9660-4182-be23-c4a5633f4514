import { JsonRpcProvider } from '@polkadot-api/json-rpc-provider';

type AddOnAddChainByUserListener = (onAddChainByUser: (input: InputChain, tabId: number) => Promise<void>) => void;
interface InputChain {
    genesisHash: string;
    name: string;
    chainSpec: string;
    relayChainGenesisHash?: string;
}
interface LightClientPageHelper {
    deleteChain: (genesisHash: string) => Promise<void>;
    persistChain: (chainSpec: string, relayChainGenesisHash?: string) => Promise<void>;
    getChains: () => Promise<Array<PageChain>>;
    getActiveConnections: () => Promise<Array<{
        tabId: number;
        chain: PageChain;
    }>>;
    disconnect: (tabId: number, genesisHash: string) => Promise<void>;
    setBootNodes: (genesisHash: string, bootNodes: Array<string>) => Promise<void>;
}
interface PageChain {
    genesisHash: string;
    chainSpec: string;
    relayChainGenesisHash?: string;
    name: string;
    ss58Format: number;
    bootNodes: Array<string>;
    provider: JsonRpcProvider;
}
type BackgroundRpcSpec = {
    keepAlive(): void;
    getChain(chainSpec: string, relayChainGenesisHash?: string): Promise<{
        genesisHash: string;
        name: string;
    }>;
    getChains(): Promise<Record<string, {
        genesisHash: string;
        name: string;
    }>>;
    deleteChain(genesisHash: string): Promise<void>;
    persistChain(chainSpec: string, relayChainGenesisHash?: string): Promise<void>;
    getActiveConnections(): Promise<Array<{
        tabId: number;
        chain: {
            genesisHash: string;
            chainSpec: string;
            relayChainGenesisHash?: string;
            name: string;
            ss58Format: number;
            bootNodes: Array<string>;
        };
    }>>;
    disconnect(tabId: number, genesisHash: string): void;
    setBootNodes(genesisHash: string, bootNodes: string[]): Promise<void>;
};

export type { AddOnAddChainByUserListener as A, BackgroundRpcSpec as B, InputChain as I, LightClientPageHelper as L, PageChain as P };

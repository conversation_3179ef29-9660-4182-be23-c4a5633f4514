import {
  CONTEXT,
  KEEP_ALIVE_INTERVAL,
  PORT,
  isRpcMessageWithOrigin,
  isSubstrateConnectToApplicationMessage,
  isSubstrateConnectToExtensionMessage
} from "../chunk-FJY652KW.mjs";
import {
  createRpc,
  isRpcMessage
} from "../chunk-KOQEJYB7.mjs";

// src/content-script/content-script-helper.ts
var isRegistered = false;
var register = (channelId) => {
  if (isRegistered)
    throw new Error("helper already registered");
  isRegistered = true;
  const whenActivated = new Promise((resolve) => {
    if (document.prerendering) {
      document.addEventListener("prerenderingchange", resolve);
    } else {
      resolve();
    }
  });
  const postToPage = (msg) => window.postMessage({ channelId, msg });
  const chainIds = /* @__PURE__ */ new Set();
  chrome.runtime.onMessage.addListener((msg) => {
    if (isSubstrateConnectToApplicationMessage(msg) && msg.type === "error") {
      chainIds.delete(msg.chainId);
      postToPage(msg);
    }
  });
  const onRelayMessage = (msg) => {
    if (isRpcResponseToLegacyRequestMessage(msg))
      msg = adaptRpcResponseToLegacyToApplicationMessage(msg);
    if (isSubstrateConnectToApplicationMessage(msg) && msg.type === "error")
      chainIds.delete(msg.chainId);
    postToPage({
      ...msg,
      origin: isSubstrateConnectToApplicationMessage(msg) ? msg.origin : CONTEXT.CONTENT_SCRIPT
    });
  };
  const onRelayerDisconnect = () => {
    chainIds.forEach(
      (chainId) => postToPage({
        origin: "substrate-connect-extension",
        chainId,
        type: "error",
        errorMessage: "Disconnected from extension"
      })
    );
    chainIds.clear();
  };
  window.addEventListener("message", async ({ data, source }) => {
    if (source !== window || !data)
      return;
    const { channelId: msgChannelId } = data;
    if (channelId !== msgChannelId)
      return;
    let { msg } = data;
    if (isLegacyToExtensionMessage(msg))
      msg = adaptLegacyToExtensionMessageToRpcMessage(msg);
    if (!isRpcMessageWithOrigin(msg, CONTEXT.WEB_PAGE) && !isSubstrateConnectToExtensionMessage(msg))
      return;
    await whenActivated;
    getOrCreateInternalRpc();
    getOrCreateExtensionRelayer(
      onRelayMessage,
      onRelayerDisconnect
    ).postMessage(msg);
    if (isSubstrateConnectToExtensionMessage(msg))
      switch (msg.type) {
        case "add-chain":
        case "add-well-known-chain": {
          chainIds.add(msg.chainId);
          break;
        }
        case "remove-chain": {
          chainIds.delete(msg.chainId);
          break;
        }
        default:
          break;
      }
  });
};
var extensionRelayer;
var getOrCreateExtensionRelayer = (onMessage, onDisconnect) => {
  if (extensionRelayer)
    return extensionRelayer;
  const port = chrome.runtime.connect({ name: PORT.WEB_PAGE });
  port.onDisconnect.addListener((port2) => {
    extensionRelayer = void 0;
    onDisconnect?.(port2);
  });
  port.onMessage.addListener(onMessage);
  return extensionRelayer = {
    postMessage(msg) {
      port.postMessage(msg);
    }
  };
};
var internalRpc;
var getOrCreateInternalRpc = () => {
  if (internalRpc)
    return internalRpc;
  const port = chrome.runtime.connect({ name: PORT.CONTENT_SCRIPT });
  const rpc = createRpc(
    (msg) => port.postMessage(msg)
  ).withClient();
  port.onMessage.addListener(rpc.handle);
  const keepAliveInterval = setInterval(
    () => rpc.notify("keepAlive", []),
    KEEP_ALIVE_INTERVAL
  );
  port.onDisconnect.addListener(() => {
    internalRpc = void 0;
    clearInterval(keepAliveInterval);
  });
  internalRpc = rpc;
  return internalRpc;
};
var isLegacyToExtensionMessage = (msg) => {
  if (typeof msg !== "object" || typeof msg?.id !== "string" || msg?.origin !== "@substrate/light-client-extension-helper-context-web-page" || !(msg?.type === "getChains" || msg?.type === "getChain" && typeof msg?.chainSpec === "string"))
    return false;
  return true;
};
var isRpcResponseToLegacyRequestMessage = (msg) => isRpcMessage(msg) && !!msg?.id?.startsWith("legacy:");
var adaptLegacyToExtensionMessageToRpcMessage = (msg) => {
  return {
    id: `legacy:${msg.type}:${msg.id}`,
    orign: msg.origin,
    method: msg.type,
    params: msg.type === "getChains" ? [] : [msg.chainSpec, msg.relayChainGenesisHash]
  };
};
var adaptRpcResponseToLegacyToApplicationMessage = (msg) => {
  if (!("result" in msg))
    return msg;
  if (msg.id.startsWith("legacy:getChains:")) {
    return {
      origin: "@substrate/light-client-extension-helper-context-background",
      id: msg.id.replace("legacy:getChains:", ""),
      type: "getChainsResponse",
      chains: msg.result
    };
  } else if (msg.id.startsWith("legacy:getChain:")) {
    return {
      origin: "@substrate/light-client-extension-helper-context-background",
      id: msg.id.replace("legacy:getChain:", ""),
      type: "getChainResponse",
      chain: msg.result
    };
  }
  return msg;
};
export {
  register
};
//# sourceMappingURL=content-script-helper.mjs.map
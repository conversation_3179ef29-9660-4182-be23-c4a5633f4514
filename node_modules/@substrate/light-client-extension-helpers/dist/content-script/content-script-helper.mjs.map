{"version": 3, "sources": ["../../src/content-script/content-script-helper.ts"], "sourcesContent": ["import type { ToExtension } from \"@substrate/connect-extension-protocol\"\nimport type { ToApplicationMessage } from \"@/protocol\"\nimport type { BackgroundRpcSpec } from \"@/background/types\"\nimport {\n  CONTEXT,\n  KEEP_ALIVE_INTERVAL,\n  PORT,\n  isRpcMessageWithOrigin,\n  isSubstrateConnectToApplicationMessage,\n  isSubstrateConnectToExtensionMessage,\n} from \"@/shared\"\nimport { createRpc, isRpcMessage, type RpcMessage } from \"@/utils\"\n\nlet isRegistered = false\nexport const register = (channelId: string) => {\n  if (isRegistered) throw new Error(\"helper already registered\")\n  isRegistered = true\n\n  // Set up a promise for when the page is activated,\n  // which is needed for prerendered pages.\n  const whenActivated = new Promise<void>((resolve) => {\n    // @ts-ignore\n    if (document.prerendering) {\n      // @ts-ignore\n      document.addEventListener(\"prerenderingchange\", resolve)\n    } else {\n      resolve()\n    }\n  })\n\n  const postToPage = (msg: ToApplicationMessage[\"msg\"]) =>\n    window.postMessage({ channelId, msg } as ToApplicationMessage)\n\n  const chainIds = new Set<string>()\n\n  // TODO: update background-helper so this is handled in chrome.runtime.connect.onMessage\n  chrome.runtime.onMessage.addListener((msg) => {\n    if (isSubstrateConnectToApplicationMessage(msg) && msg.type === \"error\") {\n      chainIds.delete(msg.chainId)\n      postToPage(msg)\n    }\n  })\n\n  const onRelayMessage = (msg: any) => {\n    // TODO: remove on 0.0.4\n    if (isRpcResponseToLegacyRequestMessage(msg))\n      msg = adaptRpcResponseToLegacyToApplicationMessage(msg)\n\n    if (isSubstrateConnectToApplicationMessage(msg) && msg.type === \"error\")\n      chainIds.delete(msg.chainId)\n\n    postToPage({\n      ...msg,\n      origin: isSubstrateConnectToApplicationMessage(msg)\n        ? msg.origin\n        : CONTEXT.CONTENT_SCRIPT,\n    })\n  }\n  const onRelayerDisconnect = () => {\n    chainIds.forEach((chainId) =>\n      postToPage({\n        origin: \"substrate-connect-extension\",\n        chainId,\n        type: \"error\",\n        errorMessage: \"Disconnected from extension\",\n      }),\n    )\n    chainIds.clear()\n  }\n\n  window.addEventListener(\"message\", async ({ data, source }) => {\n    if (source !== window || !data) return\n    const { channelId: msgChannelId } = data\n    if (channelId !== msgChannelId) return\n    let { msg } = data\n    // TODO: remove on 0.0.4\n    if (isLegacyToExtensionMessage(msg))\n      msg = adaptLegacyToExtensionMessageToRpcMessage(msg)\n\n    if (\n      !isRpcMessageWithOrigin(msg, CONTEXT.WEB_PAGE) &&\n      !isSubstrateConnectToExtensionMessage(msg)\n    )\n      return\n\n    await whenActivated\n\n    getOrCreateInternalRpc()\n\n    getOrCreateExtensionRelayer(\n      onRelayMessage,\n      onRelayerDisconnect,\n    ).postMessage(msg)\n\n    if (isSubstrateConnectToExtensionMessage(msg))\n      switch (msg.type) {\n        case \"add-chain\":\n        case \"add-well-known-chain\": {\n          chainIds.add(msg.chainId)\n          break\n        }\n        case \"remove-chain\": {\n          chainIds.delete(msg.chainId)\n          break\n        }\n        default:\n          break\n      }\n  })\n}\n\nlet extensionRelayer:\n  | { postMessage(msg: RpcMessage | ToExtension): void }\n  | undefined\nconst getOrCreateExtensionRelayer = (\n  onMessage: (msg: any) => void,\n  onDisconnect?: (port: chrome.runtime.Port) => void,\n) => {\n  if (extensionRelayer) return extensionRelayer\n\n  const port = chrome.runtime.connect({ name: PORT.WEB_PAGE })\n  port.onDisconnect.addListener((port) => {\n    extensionRelayer = undefined\n    onDisconnect?.(port)\n  })\n  port.onMessage.addListener(onMessage)\n\n  return (extensionRelayer = {\n    postMessage(msg) {\n      port.postMessage(msg)\n    },\n  })\n}\n\nlet internalRpc: any | undefined\nconst getOrCreateInternalRpc = () => {\n  if (internalRpc) return internalRpc\n\n  const port = chrome.runtime.connect({ name: PORT.CONTENT_SCRIPT })\n  const rpc = createRpc((msg) =>\n    port.postMessage(msg),\n  ).withClient<BackgroundRpcSpec>()\n  port.onMessage.addListener(rpc.handle)\n  const keepAliveInterval = setInterval(\n    () => rpc.notify(\"keepAlive\", []),\n    KEEP_ALIVE_INTERVAL,\n  )\n  port.onDisconnect.addListener(() => {\n    internalRpc = undefined\n    clearInterval(keepAliveInterval)\n  })\n\n  internalRpc = rpc\n\n  return internalRpc\n}\n\n//#region Legacy message helpers for DApps or libraries using @substrate/light-client-extension-helpers@0.0.2\n// TODO: remove on v0.0.4\n// TODO: breaking change for @substrate/connect@0.8.5\n\ntype LegacyToExtensionMessage =\n  | {\n      id: string\n      origin: \"@substrate/light-client-extension-helper-context-web-page\"\n      type: \"getChains\"\n    }\n  | {\n      id: string\n      origin: \"@substrate/light-client-extension-helper-context-web-page\"\n      type: \"getChain\"\n      chainSpec: string\n      relayChainGenesisHash?: string\n    }\n\ntype LegacyToApplicationMessage =\n  | {\n      id: string\n      origin: \"@substrate/light-client-extension-helper-context-background\"\n      type: \"getChainsResponse\"\n      chains: any\n    }\n  | {\n      id: string\n      origin: \"@substrate/light-client-extension-helper-context-background\"\n      type: \"getChainResponse\"\n      chain: any\n    }\n\nconst isLegacyToExtensionMessage = (\n  msg: any,\n): msg is LegacyToExtensionMessage => {\n  if (\n    typeof msg !== \"object\" ||\n    typeof msg?.id !== \"string\" ||\n    msg?.origin !==\n      \"@substrate/light-client-extension-helper-context-web-page\" ||\n    !(\n      msg?.type === \"getChains\" ||\n      (msg?.type === \"getChain\" && typeof msg?.chainSpec === \"string\")\n    )\n  )\n    return false\n\n  return true\n}\n\nconst isRpcResponseToLegacyRequestMessage = (msg: any): msg is RpcMessage =>\n  isRpcMessage(msg) && !!msg?.id?.startsWith(\"legacy:\")\n\nconst adaptLegacyToExtensionMessageToRpcMessage = (\n  msg: LegacyToExtensionMessage,\n) => {\n  return {\n    id: `legacy:${msg.type}:${msg.id}`,\n    orign: msg.origin,\n    method: msg.type,\n    params:\n      msg.type === \"getChains\"\n        ? []\n        : [msg.chainSpec, msg.relayChainGenesisHash],\n  } as RpcMessage\n}\n\nconst adaptRpcResponseToLegacyToApplicationMessage = (msg: RpcMessage) => {\n  if (!(\"result\" in msg)) return msg\n  if (msg.id.startsWith(\"legacy:getChains:\")) {\n    return {\n      origin: \"@substrate/light-client-extension-helper-context-background\",\n      id: msg.id.replace(\"legacy:getChains:\", \"\"),\n      type: \"getChainsResponse\",\n      chains: msg.result,\n    } as LegacyToApplicationMessage\n  } else if (msg.id.startsWith(\"legacy:getChain:\")) {\n    return {\n      origin: \"@substrate/light-client-extension-helper-context-background\",\n      id: msg.id.replace(\"legacy:getChain:\", \"\"),\n      type: \"getChainResponse\",\n      chain: msg.result,\n    } as LegacyToApplicationMessage\n  }\n  return msg\n}\n\n//#endregion\n"], "mappings": ";;;;;;;;;;;;;;AAaA,IAAI,eAAe;AACZ,IAAM,WAAW,CAAC,cAAsB;AAC7C,MAAI;AAAc,UAAM,IAAI,MAAM,2BAA2B;AAC7D,iBAAe;AAIf,QAAM,gBAAgB,IAAI,QAAc,CAAC,YAAY;AAEnD,QAAI,SAAS,cAAc;AAEzB,eAAS,iBAAiB,sBAAsB,OAAO;AAAA,IACzD,OAAO;AACL,cAAQ;AAAA,IACV;AAAA,EACF,CAAC;AAED,QAAM,aAAa,CAAC,QAClB,OAAO,YAAY,EAAE,WAAW,IAAI,CAAyB;AAE/D,QAAM,WAAW,oBAAI,IAAY;AAGjC,SAAO,QAAQ,UAAU,YAAY,CAAC,QAAQ;AAC5C,QAAI,uCAAuC,GAAG,KAAK,IAAI,SAAS,SAAS;AACvE,eAAS,OAAO,IAAI,OAAO;AAC3B,iBAAW,GAAG;AAAA,IAChB;AAAA,EACF,CAAC;AAED,QAAM,iBAAiB,CAAC,QAAa;AAEnC,QAAI,oCAAoC,GAAG;AACzC,YAAM,6CAA6C,GAAG;AAExD,QAAI,uCAAuC,GAAG,KAAK,IAAI,SAAS;AAC9D,eAAS,OAAO,IAAI,OAAO;AAE7B,eAAW;AAAA,MACT,GAAG;AAAA,MACH,QAAQ,uCAAuC,GAAG,IAC9C,IAAI,SACJ,QAAQ;AAAA,IACd,CAAC;AAAA,EACH;AACA,QAAM,sBAAsB,MAAM;AAChC,aAAS;AAAA,MAAQ,CAAC,YAChB,WAAW;AAAA,QACT,QAAQ;AAAA,QACR;AAAA,QACA,MAAM;AAAA,QACN,cAAc;AAAA,MAChB,CAAC;AAAA,IACH;AACA,aAAS,MAAM;AAAA,EACjB;AAEA,SAAO,iBAAiB,WAAW,OAAO,EAAE,MAAM,OAAO,MAAM;AAC7D,QAAI,WAAW,UAAU,CAAC;AAAM;AAChC,UAAM,EAAE,WAAW,aAAa,IAAI;AACpC,QAAI,cAAc;AAAc;AAChC,QAAI,EAAE,IAAI,IAAI;AAEd,QAAI,2BAA2B,GAAG;AAChC,YAAM,0CAA0C,GAAG;AAErD,QACE,CAAC,uBAAuB,KAAK,QAAQ,QAAQ,KAC7C,CAAC,qCAAqC,GAAG;AAEzC;AAEF,UAAM;AAEN,2BAAuB;AAEvB;AAAA,MACE;AAAA,MACA;AAAA,IACF,EAAE,YAAY,GAAG;AAEjB,QAAI,qCAAqC,GAAG;AAC1C,cAAQ,IAAI,MAAM;AAAA,QAChB,KAAK;AAAA,QACL,KAAK,wBAAwB;AAC3B,mBAAS,IAAI,IAAI,OAAO;AACxB;AAAA,QACF;AAAA,QACA,KAAK,gBAAgB;AACnB,mBAAS,OAAO,IAAI,OAAO;AAC3B;AAAA,QACF;AAAA,QACA;AACE;AAAA,MACJ;AAAA,EACJ,CAAC;AACH;AAEA,IAAI;AAGJ,IAAM,8BAA8B,CAClC,WACA,iBACG;AACH,MAAI;AAAkB,WAAO;AAE7B,QAAM,OAAO,OAAO,QAAQ,QAAQ,EAAE,MAAM,KAAK,SAAS,CAAC;AAC3D,OAAK,aAAa,YAAY,CAACA,UAAS;AACtC,uBAAmB;AACnB,mBAAeA,KAAI;AAAA,EACrB,CAAC;AACD,OAAK,UAAU,YAAY,SAAS;AAEpC,SAAQ,mBAAmB;AAAA,IACzB,YAAY,KAAK;AACf,WAAK,YAAY,GAAG;AAAA,IACtB;AAAA,EACF;AACF;AAEA,IAAI;AACJ,IAAM,yBAAyB,MAAM;AACnC,MAAI;AAAa,WAAO;AAExB,QAAM,OAAO,OAAO,QAAQ,QAAQ,EAAE,MAAM,KAAK,eAAe,CAAC;AACjE,QAAM,MAAM;AAAA,IAAU,CAAC,QACrB,KAAK,YAAY,GAAG;AAAA,EACtB,EAAE,WAA8B;AAChC,OAAK,UAAU,YAAY,IAAI,MAAM;AACrC,QAAM,oBAAoB;AAAA,IACxB,MAAM,IAAI,OAAO,aAAa,CAAC,CAAC;AAAA,IAChC;AAAA,EACF;AACA,OAAK,aAAa,YAAY,MAAM;AAClC,kBAAc;AACd,kBAAc,iBAAiB;AAAA,EACjC,CAAC;AAED,gBAAc;AAEd,SAAO;AACT;AAkCA,IAAM,6BAA6B,CACjC,QACoC;AACpC,MACE,OAAO,QAAQ,YACf,OAAO,KAAK,OAAO,YACnB,KAAK,WACH,+DACF,EACE,KAAK,SAAS,eACb,KAAK,SAAS,cAAc,OAAO,KAAK,cAAc;AAGzD,WAAO;AAET,SAAO;AACT;AAEA,IAAM,sCAAsC,CAAC,QAC3C,aAAa,GAAG,KAAK,CAAC,CAAC,KAAK,IAAI,WAAW,SAAS;AAEtD,IAAM,4CAA4C,CAChD,QACG;AACH,SAAO;AAAA,IACL,IAAI,UAAU,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,IAChC,OAAO,IAAI;AAAA,IACX,QAAQ,IAAI;AAAA,IACZ,QACE,IAAI,SAAS,cACT,CAAC,IACD,CAAC,IAAI,WAAW,IAAI,qBAAqB;AAAA,EACjD;AACF;AAEA,IAAM,+CAA+C,CAAC,QAAoB;AACxE,MAAI,EAAE,YAAY;AAAM,WAAO;AAC/B,MAAI,IAAI,GAAG,WAAW,mBAAmB,GAAG;AAC1C,WAAO;AAAA,MACL,QAAQ;AAAA,MACR,IAAI,IAAI,GAAG,QAAQ,qBAAqB,EAAE;AAAA,MAC1C,MAAM;AAAA,MACN,QAAQ,IAAI;AAAA,IACd;AAAA,EACF,WAAW,IAAI,GAAG,WAAW,kBAAkB,GAAG;AAChD,WAAO;AAAA,MACL,QAAQ;AAAA,MACR,IAAI,IAAI,GAAG,QAAQ,oBAAoB,EAAE;AAAA,MACzC,MAAM;AAAA,MACN,OAAO,IAAI;AAAA,IACb;AAAA,EACF;AACA,SAAO;AACT;", "names": ["port"]}
import * as smoldot from 'smoldot';

type ClientOptions = Readonly<smoldot.ClientOptions>;
type AddRelayChainOptions = Omit<smoldot.AddChainOptions, "potentialRelayChains">;
type AddChainOptions = Readonly<AddRelayChainOptions & {
    potentialRelayChains?: Readonly<AddRelayChainOptions>[];
}>;
type Client = {
    addChain: (options: AddChainOptions) => Promise<Chain>;
    terminate: () => Promise<void>;
    restart: () => Promise<void>;
};
type Chain = smoldot.Chain;

export type { AddChainOptions as A, Client as C, ClientOptions as a, Chain as b };

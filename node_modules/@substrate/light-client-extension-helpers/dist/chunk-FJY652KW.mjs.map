{"version": 3, "sources": ["../src/shared/constants.ts", "../src/shared/createBackgroundClientConnectProvider.ts", "../src/shared/getRandomChainId.ts", "../src/shared/message-utils.ts"], "sourcesContent": ["const extensionPrefix = \"@substrate/light-client-extension-helper\"\n\nconst contextPrefix = `${extensionPrefix}-context`\nexport const CONTEXT = {\n  CONTENT_SCRIPT: `${contextPrefix}-content-script`,\n  BACKGROUND: `${contextPrefix}-background`,\n  EXTENSION_PAGE: `${contextPrefix}-extension-page`,\n  WEB_PAGE: `${contextPrefix}-web-page`,\n} as const\n\nconst portPrefix = `${extensionPrefix}-port`\nexport const PORT = {\n  CONTENT_SCRIPT: `${portPrefix}-content-script`,\n  EXTENSION_PAGE: `${portPrefix}-extension-page`,\n  WEB_PAGE: `${portPrefix}-web-page`,\n} as const\n\nexport const STORAGE_PREFIX = extensionPrefix\n\nconst alarmPrefix = `${extensionPrefix}-alarm`\nexport const ALARM = {\n  DATABASE_UPDATE: `${alarmPrefix}-database-update`,\n} as const\n\nexport const KEEP_ALIVE_INTERVAL = 20_000\n", "import { JsonRpcProvider } from \"@polkadot-api/json-rpc-provider\"\nimport { getSyncProvider } from \"@polkadot-api/json-rpc-provider-proxy\"\nimport { getRandomChainId } from \"./getRandomChainId\"\nimport {\n  ToApplication,\n  ToExtension,\n} from \"@substrate/connect-extension-protocol\"\n\ntype Callback<T> = (value: T) => void\ntype UnsubscribeFn = () => void\n\ntype CreateBackgroundClientConnectProviderOptions = {\n  genesisHash: string\n  chainSpec?: string\n  relayChainGenesisHash?: string\n  postMessage: (msg: ToExtension) => void\n  addOnMessageListener: (callback: Callback<ToApplication>) => UnsubscribeFn\n  addOnDisconnectListener?: (callback: Callback<any>) => UnsubscribeFn\n}\n\nexport const createBackgroundClientConnectProvider = ({\n  genesisHash,\n  chainSpec,\n  relayChainGenesisHash,\n  postMessage,\n  addOnMessageListener,\n  addOnDisconnectListener,\n}: CreateBackgroundClientConnectProviderOptions): JsonRpcProvider =>\n  getSyncProvider(async () => {\n    const chainId = getRandomChainId()\n    await new Promise<void>((resolve, reject) => {\n      const removeOnMessageListener = addOnMessageListener((msg) => {\n        if (msg?.chainId !== chainId) return\n        switch (msg.type) {\n          case \"chain-ready\": {\n            resolve()\n            break\n          }\n          case \"error\": {\n            reject(new Error(msg.errorMessage))\n            break\n          }\n          default:\n            reject(new Error(`Unrecognized message ${JSON.stringify(msg)}`))\n            break\n        }\n        removeOnMessageListener()\n      })\n      postMessage(\n        chainSpec\n          ? {\n              origin: \"substrate-connect-client\",\n              type: \"add-chain\",\n              chainId,\n              chainSpec,\n              potentialRelayChainIds: relayChainGenesisHash\n                ? [relayChainGenesisHash]\n                : [],\n            }\n          : {\n              origin: \"substrate-connect-client\",\n              type: \"add-well-known-chain\",\n              chainId,\n              chainName: genesisHash,\n            },\n      )\n    })\n    return (onMessage, onHalt) => {\n      const removeOnMessageListener = addOnMessageListener((msg) => {\n        if (msg.chainId !== chainId) return\n        switch (msg.type) {\n          case \"rpc\": {\n            onMessage(msg.jsonRpcMessage)\n            break\n          }\n          case \"error\": {\n            console.error(msg.errorMessage)\n            removeListeners()\n            onHalt()\n            break\n          }\n          default:\n            console.warn(`Unrecognized message ${JSON.stringify(msg)}`)\n            break\n        }\n      })\n      const removeOnDisconnectListener = addOnDisconnectListener?.(onHalt)\n      const removeListeners = () => {\n        removeOnMessageListener()\n        removeOnDisconnectListener?.()\n      }\n      return {\n        send(jsonRpcMessage) {\n          postMessage({\n            origin: \"substrate-connect-client\",\n            type: \"rpc\",\n            chainId,\n            jsonRpcMessage,\n          })\n        },\n        disconnect() {\n          removeListeners()\n          postMessage({\n            origin: \"substrate-connect-client\",\n            type: \"remove-chain\",\n            chainId,\n          })\n        },\n      }\n    }\n  })\n", "export const getRandomChainId = () => {\n  const arr = new BigUint64Array(2)\n  // It can only be used from the browser, so this is fine.\n  crypto.getRandomValues(arr)\n  const result = (arr[1]! << BigInt(64)) | arr[0]!\n  return result.toString(36)\n}\n", "import {\n  ToExtension,\n  ToApplication,\n} from \"@substrate/connect-extension-protocol\"\nimport { type RpcMessage, isRpcMessage } from \"@/utils\"\n\nexport const isSubstrateConnectMessage = (\n  msg: any,\n): msg is ToApplication | ToExtension =>\n  isSubstrateConnectToApplicationMessage(msg) ||\n  isSubstrateConnectToExtensionMessage(msg)\n\nexport const isSubstrateConnectToExtensionMessage = (\n  msg: any,\n): msg is ToExtension => {\n  if (typeof msg !== \"object\") return false\n  if (msg.origin !== \"substrate-connect-client\") return false\n  return true\n}\n\nexport const isSubstrateConnectToApplicationMessage = (\n  msg: any,\n): msg is ToApplication => {\n  if (typeof msg !== \"object\") return false\n  if (msg.origin !== \"substrate-connect-extension\") return false\n  return true\n}\n\nexport const isRpcMessageWithOrigin = <TOrigin extends string>(\n  msg: any,\n  origin: TOrigin,\n): msg is RpcMessage & { origin: TOrigin } => {\n  if (!isRpcMessage(msg)) return false\n  if (\"origin\" in msg && msg.origin !== origin) return false\n  return true\n}\n\nexport type RpcMessageWithOrigin<TOrigin extends string> = RpcMessage & {\n  origin: TOrigin\n}\n"], "mappings": ";;;;;AAAA,IAAM,kBAAkB;AAExB,IAAM,gBAAgB,GAAG,eAAe;AACjC,IAAM,UAAU;AAAA,EACrB,gBAAgB,GAAG,aAAa;AAAA,EAChC,YAAY,GAAG,aAAa;AAAA,EAC5B,gBAAgB,GAAG,aAAa;AAAA,EAChC,UAAU,GAAG,aAAa;AAC5B;AAEA,IAAM,aAAa,GAAG,eAAe;AAC9B,IAAM,OAAO;AAAA,EAClB,gBAAgB,GAAG,UAAU;AAAA,EAC7B,gBAAgB,GAAG,UAAU;AAAA,EAC7B,UAAU,GAAG,UAAU;AACzB;AAEO,IAAM,iBAAiB;AAE9B,IAAM,cAAc,GAAG,eAAe;AAC/B,IAAM,QAAQ;AAAA,EACnB,iBAAiB,GAAG,WAAW;AACjC;AAEO,IAAM,sBAAsB;;;ACvBnC,SAAS,uBAAuB;;;ACDzB,IAAM,mBAAmB,MAAM;AACpC,QAAM,MAAM,IAAI,eAAe,CAAC;AAEhC,SAAO,gBAAgB,GAAG;AAC1B,QAAM,SAAU,IAAI,CAAC,KAAM,OAAO,EAAE,IAAK,IAAI,CAAC;AAC9C,SAAO,OAAO,SAAS,EAAE;AAC3B;;;ADcO,IAAM,wCAAwC,CAAC;AAAA,EACpD;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,MACE,gBAAgB,YAAY;AAC1B,QAAM,UAAU,iBAAiB;AACjC,QAAM,IAAI,QAAc,CAAC,SAAS,WAAW;AAC3C,UAAM,0BAA0B,qBAAqB,CAAC,QAAQ;AAC5D,UAAI,KAAK,YAAY;AAAS;AAC9B,cAAQ,IAAI,MAAM;AAAA,QAChB,KAAK,eAAe;AAClB,kBAAQ;AACR;AAAA,QACF;AAAA,QACA,KAAK,SAAS;AACZ,iBAAO,IAAI,MAAM,IAAI,YAAY,CAAC;AAClC;AAAA,QACF;AAAA,QACA;AACE,iBAAO,IAAI,MAAM,wBAAwB,KAAK,UAAU,GAAG,CAAC,EAAE,CAAC;AAC/D;AAAA,MACJ;AACA,8BAAwB;AAAA,IAC1B,CAAC;AACD;AAAA,MACE,YACI;AAAA,QACE,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA,wBAAwB,wBACpB,CAAC,qBAAqB,IACtB,CAAC;AAAA,MACP,IACA;AAAA,QACE,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA,WAAW;AAAA,MACb;AAAA,IACN;AAAA,EACF,CAAC;AACD,SAAO,CAAC,WAAW,WAAW;AAC5B,UAAM,0BAA0B,qBAAqB,CAAC,QAAQ;AAC5D,UAAI,IAAI,YAAY;AAAS;AAC7B,cAAQ,IAAI,MAAM;AAAA,QAChB,KAAK,OAAO;AACV,oBAAU,IAAI,cAAc;AAC5B;AAAA,QACF;AAAA,QACA,KAAK,SAAS;AACZ,kBAAQ,MAAM,IAAI,YAAY;AAC9B,0BAAgB;AAChB,iBAAO;AACP;AAAA,QACF;AAAA,QACA;AACE,kBAAQ,KAAK,wBAAwB,KAAK,UAAU,GAAG,CAAC,EAAE;AAC1D;AAAA,MACJ;AAAA,IACF,CAAC;AACD,UAAM,6BAA6B,0BAA0B,MAAM;AACnE,UAAM,kBAAkB,MAAM;AAC5B,8BAAwB;AACxB,mCAA6B;AAAA,IAC/B;AACA,WAAO;AAAA,MACL,KAAK,gBAAgB;AACnB,oBAAY;AAAA,UACV,QAAQ;AAAA,UACR,MAAM;AAAA,UACN;AAAA,UACA;AAAA,QACF,CAAC;AAAA,MACH;AAAA,MACA,aAAa;AACX,wBAAgB;AAChB,oBAAY;AAAA,UACV,QAAQ;AAAA,UACR,MAAM;AAAA,UACN;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AACF,CAAC;;;AElGI,IAAM,uCAAuC,CAClD,QACuB;AACvB,MAAI,OAAO,QAAQ;AAAU,WAAO;AACpC,MAAI,IAAI,WAAW;AAA4B,WAAO;AACtD,SAAO;AACT;AAEO,IAAM,yCAAyC,CACpD,QACyB;AACzB,MAAI,OAAO,QAAQ;AAAU,WAAO;AACpC,MAAI,IAAI,WAAW;AAA+B,WAAO;AACzD,SAAO;AACT;AAEO,IAAM,yBAAyB,CACpC,KACA,WAC4C;AAC5C,MAAI,CAAC,aAAa,GAAG;AAAG,WAAO;AAC/B,MAAI,YAAY,OAAO,IAAI,WAAW;AAAQ,WAAO;AACrD,SAAO;AACT;", "names": []}
// src/utils/createRpc.ts
var createRpc = (sendMessage, handlers, middlewares) => {
  let nextId = 0;
  const pending = /* @__PURE__ */ new Map();
  middlewares ?? (middlewares = []);
  middlewares.unshift(
    createResponseMiddleware(sendMessage),
    createIsValidMethodMiddleware(Object.keys(handlers ?? {}))
  );
  const applyMiddleware = middlewares.reduce(
    (prevMiddleware, nextMiddleware) => (next, message, context) => prevMiddleware(
      (request2, context2) => nextMiddleware(next, request2, context2),
      message,
      context
    )
  );
  const innerMethodHandler = ({ method, params }, context) => handlers?.[method](params ?? [], context);
  const methodHandler = (message, context) => applyMiddleware(innerMethodHandler, message, context);
  const request = (method, params) => {
    const id = `${nextId++}`;
    sendMessage({ id, method, params });
    return new Promise(
      (resolve, reject) => pending.set(id, { resolve, reject })
    );
  };
  const notify = (method, params) => {
    sendMessage({ method, params });
  };
  const handle = async (message, context) => {
    if (!isRpcMessage(message))
      return;
    if ("method" in message) {
      try {
        await methodHandler(message, context);
      } catch (error) {
        console.error("error hanlding message:", message, error);
      }
    } else if ("id" in message) {
      const { id } = message;
      if (!pending.has(id))
        return console.assert(false, "Unknown message", message);
      const { resolve, reject } = pending.get(id);
      pending.delete(id);
      if ("error" in message)
        return reject(message.error);
      resolve(message.result);
    } else {
      console.assert(false, "Unhandled message", message);
    }
  };
  return {
    request,
    notify,
    handle,
    withClient() {
      const cache = /* @__PURE__ */ new Map();
      const client = new Proxy({}, {
        get(_, prop) {
          if (!cache.has(prop))
            cache.set(prop, (...args) => request(prop, args));
          return cache.get(prop);
        }
      });
      return {
        request(method, params) {
          return request(method, params);
        },
        notify(method, params) {
          notify(method, params);
        },
        handle,
        client
      };
    }
  };
};
var isRpcMessage = (message) => typeof message === "object" && ("method" in message || "id" in message);
var isRpcRequestMessage = (message) => isRpcMessage(message) && "method" in message;
var createIsValidMethodMiddleware = (methods) => (next, request, context) => {
  if (!methods.includes(request.method))
    throw new RpcError("Method not found", -32601);
  return next(request, context);
};
var createResponseMiddleware = (sendMessage) => async (next, request, context) => {
  const { id } = request;
  try {
    const result = await next(request, context);
    if (!id)
      return;
    sendMessage({ id, result });
  } catch (error) {
    if (!id)
      return;
    if (error instanceof RpcError)
      sendMessage({
        id,
        error: {
          code: error.code,
          message: error.message,
          data: error.data
        }
      });
    sendMessage({
      id,
      error: {
        code: -32603,
        message: error instanceof Error ? error.toString() : typeof error === "string" ? error : "Unknown error"
      }
    });
  }
};
var RpcError = class extends Error {
  constructor(message, code, data) {
    super();
    this.message = message;
    this.code = code;
    this.data = data;
  }
};

export {
  createRpc,
  isRpcMessage,
  isRpcRequestMessage,
  RpcError
};
//# sourceMappingURL=chunk-KOQEJYB7.mjs.map
{"version": 3, "sources": ["../src/storage/index.ts"], "sourcesContent": ["import { STORAGE_PREFIX } from \"@/shared\"\n\nconst chainStoragePrefix = `${STORAGE_PREFIX}_chain_`\n\ntype StorageConfig = {\n  chain: [entry: { type: \"chain\"; genesisHash: string }, value: ChainInfo]\n  bootNodes: [\n    entry: { type: \"bootNodes\"; genesisHash: string },\n    value: string[],\n  ]\n  databaseContent: [\n    entry: { type: \"databaseContent\"; genesisHash: string },\n    value: string,\n  ]\n}\n\ntype StorageEntry = StorageConfig[keyof StorageConfig][0]\ntype StorageValue<T> = T extends StorageEntry\n  ? StorageConfig[T[\"type\"]][1]\n  : never\n\ntype ChainInfo = {\n  genesisHash: string\n  name: string\n  chainSpec: string\n  relayChainGenesisHash?: string\n  ss58Format: number\n}\n\nconst keyOf = ({ type, genesisHash }: StorageEntry) => {\n  if (!type.length || !genesisHash.length) throw new Error(\"Invalid entry\")\n\n  return `${STORAGE_PREFIX}_${type}_${genesisHash}`\n}\n\nexport const get = async <E extends StorageEntry>(\n  entry: E,\n): Promise<StorageValue<E> | undefined> => {\n  const key = keyOf(entry)\n  const { [key]: value } = await chrome.storage.local.get([key])\n  return value\n}\n\nexport const set = <E extends StorageEntry>(entry: E, value: StorageValue<E>) =>\n  chrome.storage.local.set({ [keyOf(entry)]: value })\n\nexport const remove = (entryOrEntries: StorageEntry | StorageEntry[]) =>\n  chrome.storage.local.remove(\n    Array.isArray(entryOrEntries)\n      ? entryOrEntries.map(keyOf)\n      : keyOf(entryOrEntries),\n  )\n\nexport const onChainsChanged = (\n  callback: (chains: Record<string, ChainInfo>) => void,\n) => {\n  const listener = async (changes: {\n    [key: string]: chrome.storage.StorageChange\n  }) => {\n    if (!Object.keys(changes).some((key) => key.startsWith(chainStoragePrefix)))\n      return\n    callback(await getChains())\n  }\n  chrome.storage.onChanged.addListener(listener)\n  return () => chrome.storage.onChanged.removeListener(listener)\n}\n\nexport const getChains = async (): Promise<Record<string, ChainInfo>> =>\n  Object.fromEntries(\n    await Promise.all(\n      Object.entries(await chrome.storage.local.get())\n        .filter((entry): entry is [string, ChainInfo] =>\n          entry[0].startsWith(chainStoragePrefix),\n        )\n        .map(async ([_, { chainSpec, ...chain }]) => {\n          const chainSpecJson = JSON.parse(chainSpec)\n          chainSpecJson.bootNodes = await get({\n            type: \"bootNodes\",\n            genesisHash: chain.genesisHash,\n          })\n          return [\n            chain.genesisHash,\n            { ...chain, chainSpec: JSON.stringify(chainSpecJson) },\n          ]\n        }),\n    ),\n  )\n"], "mappings": ";;;;;AAEA,IAAM,qBAAqB,GAAG,cAAc;AA2B5C,IAAM,QAAQ,CAAC,EAAE,MAAM,YAAY,MAAoB;AACrD,MAAI,CAAC,KAAK,UAAU,CAAC,YAAY;AAAQ,UAAM,IAAI,MAAM,eAAe;AAExE,SAAO,GAAG,cAAc,IAAI,IAAI,IAAI,WAAW;AACjD;AAEO,IAAM,MAAM,OACjB,UACyC;AACzC,QAAM,MAAM,MAAM,KAAK;AACvB,QAAM,EAAE,CAAC,GAAG,GAAG,MAAM,IAAI,MAAM,OAAO,QAAQ,MAAM,IAAI,CAAC,GAAG,CAAC;AAC7D,SAAO;AACT;AAEO,IAAM,MAAM,CAAyB,OAAU,UACpD,OAAO,QAAQ,MAAM,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,GAAG,MAAM,CAAC;AAE7C,IAAM,SAAS,CAAC,mBACrB,OAAO,QAAQ,MAAM;AAAA,EACnB,MAAM,QAAQ,cAAc,IACxB,eAAe,IAAI,KAAK,IACxB,MAAM,cAAc;AAC1B;AAEK,IAAM,kBAAkB,CAC7B,aACG;AACH,QAAM,WAAW,OAAO,YAElB;AACJ,QAAI,CAAC,OAAO,KAAK,OAAO,EAAE,KAAK,CAAC,QAAQ,IAAI,WAAW,kBAAkB,CAAC;AACxE;AACF,aAAS,MAAM,UAAU,CAAC;AAAA,EAC5B;AACA,SAAO,QAAQ,UAAU,YAAY,QAAQ;AAC7C,SAAO,MAAM,OAAO,QAAQ,UAAU,eAAe,QAAQ;AAC/D;AAEO,IAAM,YAAY,YACvB,OAAO;AAAA,EACL,MAAM,QAAQ;AAAA,IACZ,OAAO,QAAQ,MAAM,OAAO,QAAQ,MAAM,IAAI,CAAC,EAC5C;AAAA,MAAO,CAAC,UACP,MAAM,CAAC,EAAE,WAAW,kBAAkB;AAAA,IACxC,EACC,IAAI,OAAO,CAAC,GAAG,EAAE,WAAW,GAAG,MAAM,CAAC,MAAM;AAC3C,YAAM,gBAAgB,KAAK,MAAM,SAAS;AAC1C,oBAAc,YAAY,MAAM,IAAI;AAAA,QAClC,MAAM;AAAA,QACN,aAAa,MAAM;AAAA,MACrB,CAAC;AACD,aAAO;AAAA,QACL,MAAM;AAAA,QACN,EAAE,GAAG,OAAO,WAAW,KAAK,UAAU,aAAa,EAAE;AAAA,MACvD;AAAA,IACF,CAAC;AAAA,EACL;AACF;", "names": []}
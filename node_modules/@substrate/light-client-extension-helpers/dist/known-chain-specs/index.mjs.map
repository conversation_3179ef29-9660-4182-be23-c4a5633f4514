{"version": 3, "sources": ["../../src/known-chain-specs/index.ts"], "sourcesContent": ["import { chainSpec as polkadot } from \"./polkadot\"\nimport { chainSpec as ksmcc3 } from \"./ksmcc3\"\nimport { chainSpec as westend2 } from \"./westend2\"\nimport { chainSpec as rococo_v2_2 } from \"./rococo_v2_2\"\n\nexport * from \"@substrate/connect-known-chains\"\n\nexport type WellKnownChainGenesisHash = keyof typeof wellKnownChainSpecs\n\nexport const wellKnownChainSpecs = {\n  \"0x91b171bb158e2d3848fa23a9f1c25182fb8e20313b2c1eb49219da7a70ce90c3\":\n    polkadot,\n  \"0xb0a8d493285c2df73290dfb7e61f870f17b41801197a149ca93654499ea3dafe\": ksmcc3,\n  \"0xe143f23803ac50e8f6f8e62695d1ce9e4e1d68aa36c1cd2cfd15340213f3423e\":\n    westend2,\n  \"0x6408de7737c59c238890533af25896a2c20608d8b380bb01029acb392781063e\":\n    rococo_v2_2,\n}\n"], "mappings": ";;;;;;;;;;;;;;AAKA,cAAc;AAIP,IAAM,sBAAsB;AAAA,EACjC,sEACE;AAAA,EACF,sEAAsEA;AAAA,EACtE,sEACEA;AAAA,EACF,sEACEA;AACJ;", "names": ["chainSpec"]}
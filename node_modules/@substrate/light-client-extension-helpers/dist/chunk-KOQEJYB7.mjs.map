{"version": 3, "sources": ["../src/utils/createRpc.ts"], "sourcesContent": ["type RpcRequestMessage = { id?: string; method: string; params?: any[] }\ntype RpcResponseMessage =\n  | { id: string; result: any }\n  | {\n      id: string\n      error: { code: number; message: string; data?: any }\n    }\n\nexport type RpcMessage = RpcRequestMessage | RpcResponseMessage\n\ntype RpcMethod = (...params: any[]) => any | Promise<any>\n\ntype RpcSpec = Record<string, RpcMethod>\n\nexport type RpcMethodHandlers<TRpcSpec extends RpcSpec, TContext = void> = {\n  [method in keyof TRpcSpec]: (\n    params: Parameters<TRpcSpec[method]>,\n    context: TContext,\n  ) => ReturnType<TRpcSpec[method]>\n}\n\nexport type RpcMethodMiddleware<Context = any> = (\n  next: RpcMethodMiddlewareNext<Context>,\n  request: RpcRequestMessage,\n  context: Context,\n) => Promise<any>\n\ntype RpcMethodMiddlewareNext<Context> = (\n  request: RpcRequestMessage,\n  context: Context,\n) => Promise<any>\n\nexport const createRpc = <TContext>(\n  sendMessage: (message: RpcMessage) => void,\n  handlers?: RpcMethodHandlers<RpcSpec, TContext>,\n  middlewares?: RpcMethodMiddleware<TContext>[],\n) => {\n  let nextId = 0\n  const pending = new Map<\n    string,\n    { resolve: (r: any) => void; reject: (e: any) => void }\n  >()\n  middlewares ??= []\n  middlewares.unshift(\n    createResponseMiddleware(sendMessage),\n    createIsValidMethodMiddleware(Object.keys(handlers ?? {})),\n  )\n  const applyMiddleware = middlewares.reduce(\n    (prevMiddleware, nextMiddleware) => (next, message, context) =>\n      prevMiddleware(\n        (request, context) => nextMiddleware(next, request, context),\n        message,\n        context,\n      ),\n  )\n  const innerMethodHandler: RpcMethodMiddlewareNext<TContext> = (\n    { method, params },\n    context,\n  ) => handlers?.[method](params ?? [], context)\n  const methodHandler = (message: RpcRequestMessage, context: TContext) =>\n    applyMiddleware(innerMethodHandler, message, context)\n  const request = <T>(method: string, params: any[]) => {\n    const id = `${nextId++}`\n    sendMessage({ id, method, params })\n    return new Promise<T>((resolve, reject) =>\n      pending.set(id, { resolve, reject }),\n    )\n  }\n  const notify = (method: string, params: any[]) => {\n    sendMessage({ method, params })\n  }\n  const handle = async (message: RpcMessage, context: TContext) => {\n    if (!isRpcMessage(message)) return\n    if (\"method\" in message) {\n      try {\n        await methodHandler(message, context)\n      } catch (error) {\n        console.error(\"error hanlding message:\", message, error)\n      }\n    } else if (\"id\" in message) {\n      const { id } = message\n      if (!pending.has(id))\n        return console.assert(false, \"Unknown message\", message)\n      const { resolve, reject } = pending.get(id)!\n      pending.delete(id)\n      if (\"error\" in message) return reject(message.error)\n      resolve(message.result)\n    } else {\n      console.assert(false, \"Unhandled message\", message)\n    }\n  }\n  return {\n    request,\n    notify,\n    handle,\n    withClient<TRpcSpec extends RpcSpec>() {\n      const cache = new Map<string, (...args: any[]) => any>()\n      const client = new Proxy({} as TRpcSpec, {\n        get(_, prop: string) {\n          if (!cache.has(prop))\n            cache.set(prop, (...args) => request(prop, args))\n          return cache.get(prop)!\n        },\n      })\n      return {\n        request<\n          TMethod extends string & keyof TRpcSpec,\n          TParams extends Parameters<TRpcSpec[TMethod]>,\n          TReturn extends Awaited<ReturnType<TRpcSpec[TMethod]>>,\n        >(method: TMethod, params: TParams) {\n          return request<TReturn>(method, params)\n        },\n        notify<\n          TMethod extends string & keyof TRpcSpec,\n          TParams extends Parameters<TRpcSpec[TMethod]>,\n        >(method: TMethod, params: TParams) {\n          notify(method, params)\n        },\n        handle,\n        client,\n      }\n    },\n  }\n}\n\nexport const isRpcMessage = (message: any): message is RpcMessage =>\n  typeof message === \"object\" && (\"method\" in message || \"id\" in message)\n\nexport const isRpcRequestMessage = (\n  message: any,\n): message is RpcRequestMessage => isRpcMessage(message) && \"method\" in message\n\n// export const logMiddleware: MethodMiddleware = (next, message, _context) => {\n//   console.log(`> Received ${JSON.stringify(message)}`)\n//   return next(message, _context).then((response) => {\n//     console.log(`< Responding ${JSON.stringify(response)}`)\n//     return response\n//   })\n// }\n\nconst createIsValidMethodMiddleware =\n  (methods: string[]): RpcMethodMiddleware =>\n  (next, request, context) => {\n    if (!methods.includes(request.method))\n      throw new RpcError(\"Method not found\", -32601)\n    return next(request, context)\n  }\n\nconst createResponseMiddleware =\n  (sendMessage: (message: RpcResponseMessage) => void): RpcMethodMiddleware =>\n  async (next, request, context) => {\n    const { id } = request\n    try {\n      const result = await next(request, context)\n      if (!id) return\n      sendMessage({ id, result })\n    } catch (error) {\n      if (!id) return\n      if (error instanceof RpcError)\n        sendMessage({\n          id,\n          error: {\n            code: error.code,\n            message: error.message,\n            data: error.data,\n          },\n        })\n      sendMessage({\n        id,\n        error: {\n          code: -32603,\n          message:\n            error instanceof Error\n              ? error.toString()\n              : typeof error === \"string\"\n                ? error\n                : \"Unknown error\",\n        },\n      })\n    }\n  }\n\nexport class RpcError extends Error {\n  constructor(\n    readonly message: string,\n    readonly code: number,\n    readonly data?: any,\n  ) {\n    super()\n  }\n}\n"], "mappings": ";AAgCO,IAAM,YAAY,CACvB,aACA,UACA,gBACG;AACH,MAAI,SAAS;AACb,QAAM,UAAU,oBAAI,IAGlB;AACF,gCAAgB,CAAC;AACjB,cAAY;AAAA,IACV,yBAAyB,WAAW;AAAA,IACpC,8BAA8B,OAAO,KAAK,YAAY,CAAC,CAAC,CAAC;AAAA,EAC3D;AACA,QAAM,kBAAkB,YAAY;AAAA,IAClC,CAAC,gBAAgB,mBAAmB,CAAC,MAAM,SAAS,YAClD;AAAA,MACE,CAACA,UAASC,aAAY,eAAe,MAAMD,UAASC,QAAO;AAAA,MAC3D;AAAA,MACA;AAAA,IACF;AAAA,EACJ;AACA,QAAM,qBAAwD,CAC5D,EAAE,QAAQ,OAAO,GACjB,YACG,WAAW,MAAM,EAAE,UAAU,CAAC,GAAG,OAAO;AAC7C,QAAM,gBAAgB,CAAC,SAA4B,YACjD,gBAAgB,oBAAoB,SAAS,OAAO;AACtD,QAAM,UAAU,CAAI,QAAgB,WAAkB;AACpD,UAAM,KAAK,GAAG,QAAQ;AACtB,gBAAY,EAAE,IAAI,QAAQ,OAAO,CAAC;AAClC,WAAO,IAAI;AAAA,MAAW,CAAC,SAAS,WAC9B,QAAQ,IAAI,IAAI,EAAE,SAAS,OAAO,CAAC;AAAA,IACrC;AAAA,EACF;AACA,QAAM,SAAS,CAAC,QAAgB,WAAkB;AAChD,gBAAY,EAAE,QAAQ,OAAO,CAAC;AAAA,EAChC;AACA,QAAM,SAAS,OAAO,SAAqB,YAAsB;AAC/D,QAAI,CAAC,aAAa,OAAO;AAAG;AAC5B,QAAI,YAAY,SAAS;AACvB,UAAI;AACF,cAAM,cAAc,SAAS,OAAO;AAAA,MACtC,SAAS,OAAO;AACd,gBAAQ,MAAM,2BAA2B,SAAS,KAAK;AAAA,MACzD;AAAA,IACF,WAAW,QAAQ,SAAS;AAC1B,YAAM,EAAE,GAAG,IAAI;AACf,UAAI,CAAC,QAAQ,IAAI,EAAE;AACjB,eAAO,QAAQ,OAAO,OAAO,mBAAmB,OAAO;AACzD,YAAM,EAAE,SAAS,OAAO,IAAI,QAAQ,IAAI,EAAE;AAC1C,cAAQ,OAAO,EAAE;AACjB,UAAI,WAAW;AAAS,eAAO,OAAO,QAAQ,KAAK;AACnD,cAAQ,QAAQ,MAAM;AAAA,IACxB,OAAO;AACL,cAAQ,OAAO,OAAO,qBAAqB,OAAO;AAAA,IACpD;AAAA,EACF;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA,aAAuC;AACrC,YAAM,QAAQ,oBAAI,IAAqC;AACvD,YAAM,SAAS,IAAI,MAAM,CAAC,GAAe;AAAA,QACvC,IAAI,GAAG,MAAc;AACnB,cAAI,CAAC,MAAM,IAAI,IAAI;AACjB,kBAAM,IAAI,MAAM,IAAI,SAAS,QAAQ,MAAM,IAAI,CAAC;AAClD,iBAAO,MAAM,IAAI,IAAI;AAAA,QACvB;AAAA,MACF,CAAC;AACD,aAAO;AAAA,QACL,QAIE,QAAiB,QAAiB;AAClC,iBAAO,QAAiB,QAAQ,MAAM;AAAA,QACxC;AAAA,QACA,OAGE,QAAiB,QAAiB;AAClC,iBAAO,QAAQ,MAAM;AAAA,QACvB;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAEO,IAAM,eAAe,CAAC,YAC3B,OAAO,YAAY,aAAa,YAAY,WAAW,QAAQ;AAE1D,IAAM,sBAAsB,CACjC,YACiC,aAAa,OAAO,KAAK,YAAY;AAUxE,IAAM,gCACJ,CAAC,YACD,CAAC,MAAM,SAAS,YAAY;AAC1B,MAAI,CAAC,QAAQ,SAAS,QAAQ,MAAM;AAClC,UAAM,IAAI,SAAS,oBAAoB,MAAM;AAC/C,SAAO,KAAK,SAAS,OAAO;AAC9B;AAEF,IAAM,2BACJ,CAAC,gBACD,OAAO,MAAM,SAAS,YAAY;AAChC,QAAM,EAAE,GAAG,IAAI;AACf,MAAI;AACF,UAAM,SAAS,MAAM,KAAK,SAAS,OAAO;AAC1C,QAAI,CAAC;AAAI;AACT,gBAAY,EAAE,IAAI,OAAO,CAAC;AAAA,EAC5B,SAAS,OAAO;AACd,QAAI,CAAC;AAAI;AACT,QAAI,iBAAiB;AACnB,kBAAY;AAAA,QACV;AAAA,QACA,OAAO;AAAA,UACL,MAAM,MAAM;AAAA,UACZ,SAAS,MAAM;AAAA,UACf,MAAM,MAAM;AAAA,QACd;AAAA,MACF,CAAC;AACH,gBAAY;AAAA,MACV;AAAA,MACA,OAAO;AAAA,QACL,MAAM;AAAA,QACN,SACE,iBAAiB,QACb,MAAM,SAAS,IACf,OAAO,UAAU,WACf,QACA;AAAA,MACV;AAAA,IACF,CAAC;AAAA,EACH;AACF;AAEK,IAAM,WAAN,cAAuB,MAAM;AAAA,EAClC,YACW,SACA,MACA,MACT;AACA,UAAM;AAJG;AACA;AACA;AAAA,EAGX;AACF;", "names": ["request", "context"]}
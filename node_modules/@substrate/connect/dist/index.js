var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};

// src/WellKnownChain.ts
var WellKnownChain = /* @__PURE__ */ ((WellKnownChain2) => {
  WellKnownChain2["polkadot"] = "polkadot";
  WellKnownChain2["ksmcc3"] = "ksmcc3";
  WellKnownChain2["rococo_v2_2"] = "rococo_v2_2";
  WellKnownChain2["westend2"] = "westend2";
  return WellKnownChain2;
})(WellKnownChain || {});

// src/connector/getSpec.ts
var chains = /* @__PURE__ */ new Map();
function getSpec(chain) {
  return __async(this, null, function* () {
    if (!Object.keys(WellKnownChain).includes(chain))
      throw new Error("Invalid chain name");
    const knownChain = chain;
    if (!chains.has(knownChain))
      switch (knownChain) {
        case "polkadot" /* polkadot */: {
          chains.set(
            "polkadot" /* polkadot */,
            import("@substrate/connect-known-chains/polkadot")
          );
          break;
        }
        case "ksmcc3" /* ksmcc3 */: {
          chains.set(
            "ksmcc3" /* ksmcc3 */,
            import("@substrate/connect-known-chains/ksmcc3")
          );
          break;
        }
        case "westend2" /* westend2 */: {
          chains.set(
            "westend2" /* westend2 */,
            import("@substrate/connect-known-chains/westend2")
          );
          break;
        }
        case "rococo_v2_2" /* rococo_v2_2 */: {
          chains.set(
            "rococo_v2_2" /* rococo_v2_2 */,
            import("@substrate/connect-known-chains/rococo_v2_2")
          );
          break;
        }
      }
    return (yield chains.get(knownChain)).chainSpec;
  });
}

// src/connector/types.ts
var AlreadyDestroyedError = class extends Error {
  constructor() {
    super();
    this.name = "AlreadyDestroyedError";
  }
};
var CrashError = class extends Error {
  constructor(message) {
    super(message);
    this.name = "CrashError";
  }
};
var JsonRpcDisabledError = class extends Error {
  constructor() {
    super();
    this.name = "JsonRpcDisabledError";
  }
};

// src/connector/smoldot-light.ts
var isBrowser = ![typeof window, typeof document].includes("undefined");
var QueueFullError = class {
};
var startPromise = null;
var getStart = () => {
  if (startPromise)
    return startPromise;
  startPromise = import("smoldot").then((sm) => {
    QueueFullError = sm.QueueFullError;
    return sm.start;
  });
  return startPromise;
};
var startWithByteCodePromise = null;
var getStartWithByteCode = () => {
  if (startWithByteCodePromise)
    return startWithByteCodePromise;
  startWithByteCodePromise = import("smoldot/no-auto-bytecode").then(
    (sm) => sm.startWithBytecode
  );
  return startWithByteCodePromise;
};
var clientReferences = [];
var clientPromise = null;
var clientReferencesMaxLogLevel = 3;
var getClientAndIncRef = (config) => {
  if (config.maxLogLevel && config.maxLogLevel > clientReferencesMaxLogLevel)
    clientReferencesMaxLogLevel = config.maxLogLevel;
  if (clientPromise) {
    clientReferences.push(config);
    if (clientPromise instanceof Promise)
      return clientPromise;
    else
      return Promise.resolve(clientPromise);
  }
  let worker = void 0;
  let portToWorker = void 0;
  if (config.workerFactory) {
    worker = config.workerFactory();
    const { port1, port2 } = new MessageChannel();
    worker.postMessage(port1, [port1]);
    portToWorker = port2;
  }
  const clientOptions = {
    portToWorker,
    forbidTcp: true,
    // In order to avoid confusing inconsistencies between browsers and NodeJS, TCP connections are always disabled.
    forbidNonLocalWs: true,
    // Prevents browsers from emitting warnings if smoldot tried to establish non-secure WebSocket connections
    maxLogLevel: 9999999,
    // The actual level filtering is done in the logCallback
    cpuRateLimit: 0.5,
    // Politely limit the CPU usage of the smoldot background worker.
    logCallback: (level, target, message) => {
      if (level > clientReferencesMaxLogLevel)
        return;
      if (level <= 1) {
        console.error("[%s] %s", target, message);
      } else if (level === 2) {
        console.warn("[%s] %s", target, message);
      } else if (level === 3) {
        console.info("[%s] %s", target, message);
      } else if (level === 4) {
        console.debug("[%s] %s", target, message);
      } else {
        console.trace("[%s] %s", target, message);
      }
    }
  };
  const newClientPromise = worker ? getStartWithByteCode().then((start) => {
    return start(__spreadProps(__spreadValues({}, clientOptions), {
      bytecode: new Promise((resolve) => {
        if (isBrowser)
          worker.onmessage = (event) => resolve(event.data);
        else
          worker.on("message", (message) => resolve(message));
      })
    }));
  }) : getStart().then((start) => start(clientOptions));
  clientPromise = newClientPromise;
  newClientPromise.then((client) => {
    if (clientPromise === newClientPromise)
      clientPromise = client;
    else
      client.terminate();
    return client;
  });
  clientReferences.push(config);
  return clientPromise;
};
var decRef = (config) => {
  const idx = clientReferences.indexOf(config);
  if (idx === -1)
    throw new Error("Internal error within smoldot");
  clientReferences.splice(idx, 1);
  clientReferencesMaxLogLevel = 3;
  for (const cfg of clientReferences.values()) {
    if (cfg.maxLogLevel && cfg.maxLogLevel > clientReferencesMaxLogLevel)
      clientReferencesMaxLogLevel = cfg.maxLogLevel;
  }
  if (clientReferences.length === 0) {
    if (clientPromise && !(clientPromise instanceof Promise))
      clientPromise.terminate();
    clientPromise = null;
  }
};
var transformErrors = (thunk) => {
  try {
    thunk();
  } catch (e) {
    const error = e;
    if ((error == null ? void 0 : error.name) === "JsonRpcDisabledError")
      throw new JsonRpcDisabledError();
    if ((error == null ? void 0 : error.name) === "CrashError")
      throw new CrashError(error.message);
    if ((error == null ? void 0 : error.name) === "AlreadyDestroyedError")
      throw new AlreadyDestroyedError();
    throw new CrashError(
      e instanceof Error ? e.message : `Unexpected error ${e}`
    );
  }
};
var createScClient = (config) => {
  const configOrDefault = config || { maxLogLevel: 3 };
  const internalAddChain = (chainSpec, jsonRpcCallback, databaseContent, relayChain) => __async(void 0, null, function* () {
    const client = yield getClientAndIncRef(configOrDefault);
    try {
      const internalChain = yield client.addChain({
        chainSpec,
        potentialRelayChains: relayChain ? [relayChain] : void 0,
        disableJsonRpc: jsonRpcCallback === void 0,
        databaseContent
      });
      (() => __async(void 0, null, function* () {
        while (true) {
          let jsonRpcResponse;
          try {
            jsonRpcResponse = yield internalChain.nextJsonRpcResponse();
          } catch (_) {
            break;
          }
          try {
            jsonRpcCallback(jsonRpcResponse);
          } catch (error) {
            console.error("JSON-RPC callback has thrown an exception:", error);
          }
        }
      }))();
      return {
        sendJsonRpc: (rpc) => {
          transformErrors(() => {
            try {
              internalChain.sendJsonRpc(rpc);
            } catch (error) {
              if (error instanceof QueueFullError) {
                try {
                  const parsedRq = JSON.parse(rpc);
                  jsonRpcCallback(
                    JSON.stringify({
                      jsonrpc: "v2",
                      id: parsedRq.id,
                      error: {
                        code: -32e3,
                        message: "JSON-RPC server is too busy"
                      }
                    })
                  );
                } catch (_error) {
                }
              } else {
                throw error;
              }
            }
          });
        },
        remove: () => {
          try {
            transformErrors(() => {
              internalChain.remove();
            });
          } finally {
            decRef(configOrDefault);
          }
        },
        addChain: (chainSpec2, jsonRpcCallback2, databaseContent2) => {
          return internalAddChain(
            chainSpec2,
            jsonRpcCallback2,
            databaseContent2,
            internalChain
          );
        }
      };
    } catch (error) {
      decRef(configOrDefault);
      throw error;
    }
  });
  const addChain = (chainSpec, jsonRpcCallback, databaseContent) => internalAddChain(chainSpec, jsonRpcCallback, databaseContent);
  const addWellKnownChain = (supposedChain, jsonRpcCallback, databaseContent) => __async(void 0, null, function* () {
    getClientAndIncRef(configOrDefault);
    try {
      return yield internalAddChain(
        yield getSpec(supposedChain),
        jsonRpcCallback,
        databaseContent
      );
    } finally {
      decRef(configOrDefault);
    }
  });
  return {
    addChain,
    addWellKnownChain
  };
};

// src/connector/extension.ts
var wellKnownChainGenesisHashes = {
  polkadot: "0x91b171bb158e2d3848fa23a9f1c25182fb8e20313b2c1eb49219da7a70ce90c3",
  ksmcc3: "0xb0a8d493285c2df73290dfb7e61f870f17b41801197a149ca93654499ea3dafe",
  westend2: "0xe143f23803ac50e8f6f8e62695d1ce9e4e1d68aa36c1cd2cfd15340213f3423e",
  rococo_v2_2: "0x6408de7737c59c238890533af25896a2c20608d8b380bb01029acb392781063e"
};
var createScClient2 = (lightClientProviderPromise) => {
  const internalAddChain = (isWellKnown, chainSpecOrWellKnownName, jsonRpcCallback = () => {
  }, relayChainGenesisHash) => __async(void 0, null, function* () {
    const lightClientProvider = yield lightClientProviderPromise;
    let chain;
    if (isWellKnown) {
      const foundChain = Object.values(lightClientProvider.getChains()).find(
        ({ genesisHash }) => genesisHash === wellKnownChainGenesisHashes[chainSpecOrWellKnownName]
      );
      if (!foundChain)
        throw new Error("Unknown well-known chain");
      chain = foundChain;
    } else {
      chain = yield lightClientProvider.getChain(
        chainSpecOrWellKnownName,
        relayChainGenesisHash
      );
    }
    const jsonRpcProvider = chain.connect(jsonRpcCallback);
    return {
      sendJsonRpc(rpc) {
        jsonRpcProvider.send(rpc);
      },
      remove() {
        jsonRpcProvider.disconnect();
      },
      addChain: function(chainSpec, jsonRpcCallback2) {
        return internalAddChain(
          false,
          chainSpec,
          jsonRpcCallback2,
          chain.genesisHash
        );
      }
    };
  });
  return {
    addChain: (chainSpec, jsonRpcCallback) => internalAddChain(false, chainSpec, jsonRpcCallback),
    addWellKnownChain: (name, jsonRpcCallback) => internalAddChain(true, name, jsonRpcCallback)
  };
};

// src/connector/index.ts
var createScClient3 = (config) => {
  if (config == null ? void 0 : config.forceEmbeddedNode)
    return createScClient(config == null ? void 0 : config.embeddedNodeConfig);
  const lightClientProviderPromise = getExtensionLightClientProviderPromise();
  const client = lightClientProviderPromise ? createScClient2(lightClientProviderPromise) : createScClient(config == null ? void 0 : config.embeddedNodeConfig);
  return {
    addChain(chainSpec, jsonRpcCallback, databaseContent) {
      return __async(this, null, function* () {
        return (yield client).addChain(
          chainSpec,
          jsonRpcCallback,
          databaseContent
        );
      });
    },
    addWellKnownChain(id, jsonRpcCallback, databaseContent) {
      return __async(this, null, function* () {
        return (yield client).addWellKnownChain(
          id,
          jsonRpcCallback,
          databaseContent
        );
      });
    }
  };
};
function getExtensionLightClientProviderPromise() {
  if (typeof document !== "object" || typeof CustomEvent !== "function")
    return;
  let lightClientProviderPromise;
  window.dispatchEvent(
    new CustomEvent("lightClient:requestProvider", {
      detail: {
        onProvider(detail) {
          if (detail.info.rdns === "io.github.paritytech.SubstrateConnectLightClient") {
            lightClientProviderPromise = detail.provider;
          }
        }
      }
    })
  );
  return lightClientProviderPromise;
}
export {
  AlreadyDestroyedError,
  CrashError,
  JsonRpcDisabledError,
  WellKnownChain,
  createScClient3 as createScClient
};
//# sourceMappingURL=index.js.map
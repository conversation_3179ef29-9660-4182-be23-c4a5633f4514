{"version": 3, "sources": ["../src/worker.ts"], "sourcesContent": ["/// <reference lib=\"WebWorker\" />\n\n// @ts-ignore TODO: fix types in smoldot/worker\nimport * as smoldot from \"smoldot/worker\"\n// @ts-ignore TODO: fix types in smoldot/bytecode\nimport { compileBytecode } from \"smoldot/bytecode\"\n\ndeclare var self: DedicatedWorkerGlobalScope\n\ncompileBytecode().then((bytecode: unknown) => self.postMessage(bytecode))\n\nself.onmessage = ({ data }) =>\n  smoldot\n    .run(data)\n    .catch((error: any) => console.error(\"[smoldot-worker]\", error))\n    .finally(self.close)\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAGA,cAAyB;AAEzB,sBAAgC;AAAA,IAIhC,iCAAgB,EAAE,KAAK,CAAC,aAAsB,KAAK,YAAY,QAAQ,CAAC;AAExE,KAAK,YAAY,CAAC,EAAE,KAAK,MAEpB,YAAI,IAAI,EACR,MAAM,CAAC,UAAe,QAAQ,MAAM,oBAAoB,KAAK,CAAC,EAC9D,QAAQ,KAAK,KAAK;", "names": []}
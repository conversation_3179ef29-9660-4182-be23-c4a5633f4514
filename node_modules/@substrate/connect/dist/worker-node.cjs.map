{"version": 3, "sources": ["../src/worker-node.ts"], "sourcesContent": ["// @ts-ignore TODO: fix types in smoldot/worker\nimport * as smoldot from \"smoldot/worker\"\n// @ts-ignore TODO: fix types in smoldot/bytecode\nimport { compileBytecode } from \"smoldot/bytecode\"\nimport { parentPort } from \"node:worker_threads\"\n\ncompileBytecode().then((bytecode: any) => parentPort!.postMessage(bytecode))\n\nparentPort!.once(\"message\", (data) =>\n  smoldot\n    .run(data)\n    .catch((error: any) => console.error(\"[smoldot-worker]\", error))\n    .finally(() => process.exit()),\n)\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,cAAyB;AAEzB,sBAAgC;AAChC,iCAA2B;AAAA,IAE3B,iCAAgB,EAAE,KAAK,CAAC,aAAkB,sCAAY,YAAY,QAAQ,CAAC;AAE3E,sCAAY;AAAA,EAAK;AAAA,EAAW,CAAC,SAExB,YAAI,IAAI,EACR,MAAM,CAAC,UAAe,QAAQ,MAAM,oBAAoB,KAAK,CAAC,EAC9D,QAAQ,MAAM,QAAQ,KAAK,CAAC;AACjC;", "names": []}
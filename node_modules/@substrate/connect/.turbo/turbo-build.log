
> @substrate/connect@0.8.11 build /home/<USER>/work/substrate-connect/substrate-connect/packages/connect
> tsc --noEmit && tsup-node src/index.ts src/worker.ts src/worker-node.ts --clean --sourcemap --platform neutral --target=es2015 --format esm,cjs --dts

[34mCLI[39m Building entry: src/index.ts, src/worker.ts, src/worker-node.ts
[34mCLI[39m Using tsconfig: tsconfig.json
[34mCLI[39m tsup v8.0.2
[34mCLI[39m Target: es2015
[34mCLI[39m Cleaning output folder
[34mESM[39m Build start
[34mCJS[39m Build start
[32mCJS[39m [1mdist/index.cjs           [22m[32m14.95 KB[39m
[32mCJS[39m [1mdist/worker.cjs          [22m[32m1.51 KB[39m
[32mCJS[39m [1mdist/worker-node.cjs     [22m[32m1.66 KB[39m
[32mCJS[39m [1mdist/index.cjs.map       [22m[32m36.20 KB[39m
[32mCJS[39m [1mdist/worker.cjs.map      [22m[32m836.00 B[39m
[32mCJS[39m [1mdist/worker-node.cjs.map [22m[32m868.00 B[39m
[32mCJS[39m ⚡️ Build success in 110ms
[32mESM[39m [1mdist/index.js           [22m[32m13.48 KB[39m
[32mESM[39m [1mdist/worker.js          [22m[32m341.00 B[39m
[32mESM[39m [1mdist/worker-node.js     [22m[32m430.00 B[39m
[32mESM[39m [1mdist/index.js.map       [22m[32m32.06 KB[39m
[32mESM[39m [1mdist/worker.js.map      [22m[32m816.00 B[39m
[32mESM[39m [1mdist/worker-node.js.map [22m[32m851.00 B[39m
[32mESM[39m ⚡️ Build success in 111ms
[34mDTS[39m Build start
[32mDTS[39m ⚡️ Build success in 6918ms
[32mDTS[39m [1mdist/worker.d.ts       [22m[32m13.00 B[39m
[32mDTS[39m [1mdist/index.d.ts        [22m[32m9.63 KB[39m
[32mDTS[39m [1mdist/worker-node.d.ts  [22m[32m13.00 B[39m
[32mDTS[39m [1mdist/worker.d.cts      [22m[32m13.00 B[39m
[32mDTS[39m [1mdist/index.d.cts       [22m[32m9.63 KB[39m
[32mDTS[39m [1mdist/worker-node.d.cts [22m[32m13.00 B[39m

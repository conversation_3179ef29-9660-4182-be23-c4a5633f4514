{"name": "@substrate/connect", "version": "0.8.11", "description": "Substrate-connect to Smoldot clients. Using either substrate extension with predefined clients or an internal smoldot client based on chainSpecs provided.", "author": "Parity Team <<EMAIL>>", "license": "GPL-3.0-only", "publishConfig": {"access": "public"}, "type": "module", "main": "./dist/index.cjs", "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}, "./worker": {"node": {"import": "./dist/worker-node.js", "require": "./dist/worker-node.cjs"}, "import": "./dist/worker.js", "require": "./dist/worker.cjs"}}, "repository": {"type": "git", "url": "git+https://github.com/paritytech/substrate-connect.git"}, "bugs": {"url": "https://github.com/paritytech/substrate-connect/issues"}, "homepage": "https://github.com/paritytech/substrate-connect#readme", "dependencies": {"smoldot": "2.0.26", "@substrate/light-client-extension-helpers": "^1.0.0", "@substrate/connect-extension-protocol": "^2.0.0", "@substrate/connect-known-chains": "^1.1.5"}, "devDependencies": {"@polkadot-api/json-rpc-provider-proxy": "^0.1.0", "@polkadot-api/substrate-client": "^0.1.2", "eslint": "^8.57.0", "jsdom": "^24.0.0", "vitest": "^1.5.0"}, "scripts": {"pretest": "pnpm build", "test": "vitest --dangerouslyIgnoreUnhandledErrors --environment jsdom --exclude \"test/flaky/**\"", "test:flaky": "vitest test/flaky", "deep-clean": "pnpm clean && rm -rf node_modules", "clean": "rm -rf dist", "build": "tsc --noEmit && tsup-node src/index.ts src/worker.ts src/worker-node.ts --clean --sourcemap --platform neutral --target=es2015 --format esm,cjs --dts", "lint": "eslint . --ext .js,.ts"}}